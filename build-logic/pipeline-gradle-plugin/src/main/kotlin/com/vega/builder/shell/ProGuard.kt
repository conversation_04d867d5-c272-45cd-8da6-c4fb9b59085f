package com.vega.builder.shell

import StandaloneExtensions
import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar
import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.tasks.TaskProvider
import org.gradle.api.tasks.bundling.Jar
import org.gradle.kotlin.dsl.task
import proguard.gradle.ProGuardTask
import java.io.File

val proguardWorkspaces = "proguard"
val proguardResultFileName = "proguard.jar"
fun Project.createProguardTask(extensions: StandaloneExtensions): Task {
    return if (!extensions.enableProguard) {
        task("proguard") {
            doLast {
                logger.info("ProGuard is disabled")
            }
        }
    } else {
        task("proguard", ProGuardTask::class) {
            configuration(extensions.proguardFiles)

            if (System.getProperty("java.version").startsWith("1.")) {
                // Before Java 9, the runtime classes were packaged in a single jar file.
                libraryjars("${System.getProperty("java.home")}/lib/rt.jar")
            } else {
                // As of Java 9, the runtime classes are packaged in modular jmod files.
                File("${System.getProperty("java.home")}/jmods").listFiles().forEach {
                    if (it.name.endsWith(".jmod")) {
                        libraryjars(
                            mapOf<String, String>("jarfilter" to "!**.jar", "filter" to "'!module-info.class'"),
                            it.absolutePath
                        )
                    }
                }
            }
            injars(
                tasks.withType(ShadowJar::class.java).first()
                    .archiveFile
                    .get()
                    .asFile
            )
            outjars(layout.projectDirectory.file("output/${extensions.baseName}-${proguardResultFileName}").asFile.apply {
                parentFile.mkdirs()
            })
        }
    }
}


