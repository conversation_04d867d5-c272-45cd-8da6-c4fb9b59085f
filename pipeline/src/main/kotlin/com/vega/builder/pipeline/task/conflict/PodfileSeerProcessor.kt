package com.vega.builder.pipeline.task.conflict

import java.io.File

class PodfileSeerProcessor : FileConflictProcessor {
    constructor(filePath: String) : super(filePath)
    constructor(file: File, fileReader: ConflictFileReader) : super(file, fileReader)

    override suspend fun processConflictBlock(conflictBlock: ConflictBlock): List<String> {
        // First determine whether the number of conflicting rows is the same. Podfile.seer only resolves conflicts with the same number of rows
        if (conflictBlock.conflictLinesHead.size != conflictBlock.conflictLinesInsert.size) {
            throw IllegalStateException("Podfile.seer only supports conflicts with the same number of lines")
        }
        // Determine the version to use by using the version in the conflict block
        // Find the line with version first
        val versionHead = conflictBlock.conflictLinesHead.first { extractVersion(it).isNotEmpty() }
        val versionInsert =
            conflictBlock.conflictLinesInsert.first { extractVersion(it).isNotEmpty() }

        val headVersion = extractVersion(versionHead)
        val insertVersion = extractVersion(versionInsert)

        // Check if both are git commits (hex strings)
        val isHeadGitCommit = isGitCommit(versionHead)
        val isInsertGitCommit = isGitCommit(versionInsert)

        return if (isHeadGitCommit && isInsertGitCommit) {
            // For git commits, use string comparison (lexicographic order)
            if (headVersion >= insertVersion) {
                conflictBlock.conflictLinesHead
            } else {
                conflictBlock.conflictLinesInsert
            }
        } else {
            // For regular versions, use version comparison
            if (isLargeThan(headVersion, insertVersion)) {
                conflictBlock.conflictLinesHead
            } else {
                conflictBlock.conflictLinesInsert
            }
        }
    }

    private fun extractVersion(input: String): String {
        // Extract version from Podfile.seer format
        // Matches patterns like: - DependencyName (version, from 'repo')
        // Also handles git commits: - DependencyName (git 'repo', commit 'hash')
        // And path dependencies: - DependencyName (path 'path')

        // First try to match version pattern: (version, from 'repo')
        // Updated regex to handle more complex version patterns including alpha, beta, rc, etc.
        val versionRegex = """-\s+(\w+)\s+\(([0-9]+(?:\.[0-9]+)*(?:[-.][a-zA-Z0-9]+)*),\s+from\s+['"].*['"]""".toRegex()
        val versionMatch = versionRegex.find(input)

        if (versionMatch != null) {
            return versionMatch.groupValues[2]
        }

        // Try to match git commit pattern: (git 'repo', commit 'hash')
        val gitRegex = """-\s+\w+\s+\(git\s+['"].*['"],\s+commit\s+['"]([a-fA-F0-9]+)['"]""".toRegex()
        val gitMatch = gitRegex.find(input)

        if (gitMatch != null) {
            // For git commits, we'll use the commit hash as version for comparison
            // This is not ideal for version comparison, but we'll treat it as a string
            return gitMatch.groupValues[1]
        }

        // For path dependencies or other formats, return empty string (no version to compare)
        return ""
    }

    private fun isGitCommit(input: String): Boolean {
        // Check if the line contains git commit pattern
        return input.contains("git ") && input.contains("commit ")
    }
}