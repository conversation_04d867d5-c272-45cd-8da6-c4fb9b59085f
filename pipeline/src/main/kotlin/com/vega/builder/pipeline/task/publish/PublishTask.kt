package com.vega.builder.pipeline.task.publish

import com.google.gson.Gson
import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.GitParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.context.isGradleLoggerDebugMode
import com.vega.builder.pipeline.task.publish.service.TTPService
import org.koin.core.component.inject
import java.io.File
import kotlin.getValue

@TaskDefinition("PublishTask", stage = Stage.Build, "binary Publish")
open class PublishTask : PipelineTask() {
    val buildParams: BuildParams by inject()
    val gitParams: GitParams by inject()
    val workspaceParams: WorkspaceParams by inject()
    val pipelineContext: PipelineContextImpl by inject()

    override suspend fun run() {
        if (buildParams.isEnable("PUBLISH_TPP", false)) {
            publishTPP()
        } else {
            publishLuban()
        }
    }

    fun publishLuban() {
        val result =
            Command("./gradlew")
                .directory(File(workspaceParams.targetProjectDir))
                .arg("publishAar")
                .arg("--parallel")
                .arg("-PAarPublishInfo=ciPublish")
                .arg("-PDISABLE_UPLOAD_SO_SYMBOLS=true")
                .arg("--no-configure-on-demand")
                .arg("--stacktrace")
                .apply {
                    if (buildParams.containsKey("PUBLISH_FLAVOR")) {
                        arg("-PPUBLISH_FLAVOR=${buildParams["PUBLISH_FLAVOR"]}")
                    }
                }
                .apply {
                    if (buildParams.isGradleLoggerDebugMode) {
                        arg("--debug")
                    }
                }
                .env(
                    "JAVA_TOOL_OPTIONS",
                    "${
                        getenvSafe("JAVA_TOOL_OPTIONS", "")
                    } -XX:-UseContainerSupport -Djava.net.preferIPv6Addresses=true",
                ).default()
                .spawn()
                .wait()
        if (result != 0) {
            throw PipelineThrowable(ErrorType.GradleBuildError)
        }
    }

    suspend fun publishTPP() {
        val branch = gitParams.mainBranch ?: throw PipelineThrowable(ErrorType.GitBranchNotFound)
        val version = branch.replace("/", "_") + "${System.currentTimeMillis()}"
//        TTPService.triggerJob(
//            repoId = 51566,//https://bits.bytedance.net/client_component/detail/client_component/cc_ttp_job_wrapper_51566?tab=overview
//            branch = branch,
//            version = version,
//            skipCheckResult = true,
//            taskName = "publish_ttp_binary_videoeditor"
//        )
    }
}