#!/bin/bash

echo "TOS_AK=PEUSZO6PGN8DZIFHGFTS" >> local.properties
echo "TOS_BUCKET=lv-android-build-script" >> local.properties
echo "TOS_ENDPOINT=tos-cn-north.byted.org" >> local.properties
echo "UPLOAD_TOS=yes" >> local.properties
# 检查 TTP_BUILD_ENV 是否设置
if [ -z "$TTP_BUILD_ENV" ]; then
    echo "Error: TTP_BUILD_ENV is not set."
    echo "Only ttp publish support this function.Prod use bits MR publish"
    exit 0
else
    echo "TTP_BUILD_ENV is set to $TTP_BUILD_ENV."
    sed -i 's#maven.byted.org/repository/gradle/#services.gradle.org/distributions/#g' gradle/wrapper/gradle-wrapper.properties
    ./gradlew :pipeline:uploadTos -S
fi