package com.vega.builder.shell.utils.kommand.io

import com.vega.builder.shell.utils.kommand.KommandException
import java.io.BufferedReader

class BufferedReader(
    private val reader: BufferedReader,
) {
    @Throws(KommandException::class)
    fun readLine(): String = reader.readLine()

    @Throws(KommandException::class)
    fun readAll(): String = reader.readText()

    @Throws(KommandException::class)
    fun lines(): Sequence<String> = reader.lineSequence()

    fun close() {
        reader.close()
    }
}
