package com.vega.builder.pipeline.task.publish.service

import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.utils.SHA256Utils
import com.vega.builder.common.utils.compression.Compression
import com.vega.builder.common.utils.compression.TarGz
import com.vega.builder.pipeline.context.PipelineOutput
import org.eclipse.jgit.lib.Repository
import org.eclipse.jgit.storage.file.FileRepositoryBuilder
import org.eclipse.jgit.treewalk.TreeWalk
import org.eclipse.jgit.treewalk.filter.TreeFilter
import java.io.File


class CompressionRepoService(val repoRootFile: File, val repoName: String, val withLaryCommit: Boolean = false) {
    companion object {
        val LYRA_REGEX = listOf<String>(
            "/cryptopp/ios/",
            "/cryptopp/linux/",
            "/cryptopp/swift/",
            "/cryptopp/oc/",
            "/cryptopp/mac/",
            "/cryptopp/win/",
            "^unittests/",
            "^pc_config/",
            "^linux_config/",
            "^\\.",
        ).joinToString(separator = "|")
    }

    val excludeRegex = if (repoName == "videoeditor") {
        LYRA_REGEX
    } else {
        ""
    }

    val repoPath: String by lazy {
        File(repoRootFile, repoName).absolutePath
    }

    val repository: Repository by lazy {
        val builder = FileRepositoryBuilder()
        builder.setGitDir(File(repoPath, ".git"))
            .readEnvironment() // 扫描环境变量，寻找 Git 目录
            .findGitDir() // 自动寻找 Git 目录
            .build()
    }


    fun compressionRepo(): File {
        preCollectFile()
        val fileList = collectRepositoryFileList()
        postCollectFile(repoName, repoPath, fileList)
        val tempFile = File(PipelineOutput.ttpSourceCompressionPath)
        Compression.compressFiles(TarGz, fileList, File(repoPath), tempFile)
        return tempFile
    }


    fun collectRepositoryFileList(): MutableList<File> {
        val fileList = mutableListOf<File>()
        TreeWalk(repository).use { treeWalk ->
            treeWalk.addTree(repository.resolve("HEAD^{tree}"))
            treeWalk.filter = RegexTreeFilter(excludeRegex)
            treeWalk.isRecursive = true
            while (treeWalk.next()) {
                val srcFile = File(repository.directory.parentFile, treeWalk.pathString)
                fileList.add(srcFile)
            }
        }
        return fileList
    }

    fun preCollectFile() {
        if (repoName == "videoeditor") {
            Command("./autogen.sh").directory(File(repoPath, "android_config")).default().spawn().wait()
            Command("./service_autogen.sh")
                .directory(File(repoPath, "lyra"))
                .args("-s", "clip_flow_service.json")
                .args("-os", "android")
                .default().spawn().wait()
        }
    }

    fun postCollectFile(repoName: String, repoPath: String, fileList: MutableList<File>) {
        val repoFile = File(repoPath)
        val fileHash = fileList.map {
            it.relativeTo(repoFile) to SHA256Utils.ofFile(it)
        }.joinToString(separator = "\n") { "${it.first}: ${it.second}" }
        val hashFile = File(repoPath, "file-list.txt")
        hashFile.writeText(fileHash)
        fileList.add(hashFile)
        if (repoName == "videoeditor" && withLaryCommit) {
            val commitId = repository.resolve("HEAD").name
            val commitFile = File(repoPath, "lyra/script/draft/scheme/TOS_REPO_COMMIT")
            commitFile.writeText(commitId)
            fileList.add(commitFile)
        }
    }


}

internal class RegexTreeFilter(val exclude: String) : TreeFilter() {
    val regex = Regex(exclude, RegexOption.IGNORE_CASE)
    override fun include(walker: TreeWalk): Boolean {
        return !walker.pathString.contains(regex)
    }

    override fun shouldBeRecursive(): Boolean {
        return true
    }

    override fun clone(): TreeFilter? {
        return RegexTreeFilter(exclude)
    }
}