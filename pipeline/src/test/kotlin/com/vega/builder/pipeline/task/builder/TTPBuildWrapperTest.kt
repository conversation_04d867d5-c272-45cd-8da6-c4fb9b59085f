package com.vega.builder.pipeline.task.builder

import com.google.gson.Gson
import com.vega.builder.common.network.api.ITiktokMavenApi
import com.vega.builder.common.network.request
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.tos.slicePutObject
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.md5
import com.vega.builder.common.utils.retry
import com.vega.builder.pipeline.BuildConfig
import com.vega.builder.pipeline.task.artifacts.ArtifactsUploadResult
import com.vega.builder.pipeline.task.artifacts.TTPArtifactsUpload
import com.vega.builder.pipeline.task.publish.service.TTPService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class TTPBuildWrapperTest {
    @Test
    fun `request ttp build result and upload`() {
        runBlocking {

            val buildVersion = "apk-build-1753782803751"
            val jobInfo = TTPService.searchTTPJobInfo("52081", version = buildVersion)
            val uploadBaseKey = "${TTPArtifactsUpload.UPLOAD_KEY_PREFIX}${jobInfo?.jobInfo?.jobId}"
            val manifest = retry(3, 50) {
                val result =
                    request(ITiktokMavenApi::download, "${uploadBaseKey}/manifest.json")
                if (result.isSuccessful) {
                    val manifestContent = result.body()?.string() ?: throw Exception("Failed to download artifact manifest")
                    Gson().fromJson<List<ArtifactsUploadResult>>(manifestContent)
                } else {
                    throw Exception("Failed to download artifact manifest")
                }
            }
            manifest.map{
                CoroutineScope(Dispatchers.IO).launch {
                    downloadAndUploadToTos(it)
                }

            }.joinAll()
        }
    }
    private suspend fun downloadAndUploadToTos(
        artifactsUploadResult: ArtifactsUploadResult
    ): ArtifactsUploadResult {
        val file = retry(3, 50) {
            val tempFile = kotlin.io.path.createTempFile().toFile()

            val result =
                request(ITiktokMavenApi::download, artifactsUploadResult.url)
            if (result.isSuccessful) {
                result.body()?.byteStream()?.use { inputStream ->
                    tempFile.outputStream().use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                if (tempFile.md5() != artifactsUploadResult.md5) {
                    throw Exception("Failed to download artifact file error ${artifactsUploadResult.url}")
                }
            } else {
                throw Exception("Failed to download artifact file error ${artifactsUploadResult.url}")
            }
            tempFile
        }

        val taskId = getenvSafe("TASK_ID")
        val remoteName = listOfNotNull("pipeline_result", taskId, artifactsUploadResult.name).joinToString("/")
        val tosClient = TosConfig.LvBuildResult.createTosClient()
        if (tosClient.slicePutObject(remoteName, file)) {
            return artifactsUploadResult.copy(url = "https://${BuildConfig.hide_voffline_rul}/download/tos/schedule/${TosConfig.LvBuildResult.bucket}/${remoteName}")
        } else {
            throw Exception("Upload file[${file.name}] error!")
        }
    }
}