import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure

abstract class StandaloneExtensions() {
    abstract var mainClass: String
    abstract var baseName: String
    open var enableProguard: Boolean = false
    open var proguardFiles: MutableList<String> = mutableListOf<String>()
}
fun Project.standalone(action: StandaloneExtensions.() -> Unit) {
    configure<StandaloneExtensions>(action)
}
