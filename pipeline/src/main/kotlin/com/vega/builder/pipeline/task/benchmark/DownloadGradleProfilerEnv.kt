package com.vega.builder.pipeline.task.benchmark

import com.vega.builder.common.logger.logger
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.utils.compression.Compression
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.WorkspaceParams
import org.koin.core.component.inject
import kotlin.io.path.createTempFile
import java.io.File

@TaskDefinition("DownloadGradleProfilerEnv", stage = Stage.Build, "Gradle Build Profiler Env")
class DownloadGradleProfilerEnv : PipelineTask() {
    companion object {
        const val GRADLE_PROFILER_TOS_KEY = "tools/gradle-profiler/0.22.0.zip"
        const val GRADLE_PROFILER_SCENARIOS_TOS_KEY = "config/lv_full_compile_scenarios"
    }

    val workspaceParams: WorkspaceParams by inject()

    override suspend fun run() {
        val tosClient = TosConfig.LvBuildScript.createTosClient()
        val tempFile = createTempFile("gradle-profiler", ".zip").toFile()
        val gradleProfiler = File(workspaceParams.workspace, "gradle-profiler").apply { mkdirs() }
        tempFile.deleteOnExit()

        logger().info("Download gradle-profiler to ${tempFile.absolutePath}")
        tosClient.getObject(GRADLE_PROFILER_TOS_KEY)?.objectContent?.use { tosContent ->
            tempFile.outputStream().use { tempContent ->
                tosContent.copyTo(tempContent)
            }
        } ?: throw PipelineThrowable(ErrorType.GradleProfilerDownloadError)
        if (tempFile.exists()) {
            val toolsFile = File(gradleProfiler, "tools").apply { mkdirs() }
            logger().info("Decompress gradle-profiler to ${toolsFile.absolutePath}")
            Compression.decompress(tempFile, toolsFile)
        }
        val scenariosFile = File(gradleProfiler, "scenarios.txt")
        logger().info("Download scenarios to ${scenariosFile.absolutePath}")
        tosClient.getObject(GRADLE_PROFILER_SCENARIOS_TOS_KEY)?.objectContent?.use { tosContent ->
            scenariosFile.outputStream().use { tempContent ->
                tosContent.copyTo(tempContent)
            }
        } ?: throw PipelineThrowable(ErrorType.GradleProfilerScenariosDownloadError)
    }
}