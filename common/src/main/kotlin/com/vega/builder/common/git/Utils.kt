package com.vega.builder.common.git

fun extractRepositoryName(gitUrl: String): String? {
    if (gitUrl.isEmpty()) {
        return null
    }

    // Remove the ".git" suffix if present
    var cleanUrl = if (gitUrl.endsWith(".git")) gitUrl.dropLast(4) else gitUrl

    // Find the last '/' or ':' character and extract the substring after it
    val lastSlashIndex = cleanUrl.lastIndexOf('/')
    val lastColonIndex = cleanUrl.lastIndexOf(':')
    val startIndex = maxOf(lastSlashIndex, lastColonIndex) + 1

    return if (startIndex > 0 && startIndex < cleanUrl.length) cleanUrl.substring(startIndex) else null
}
