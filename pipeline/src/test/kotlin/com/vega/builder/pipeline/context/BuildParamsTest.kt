package com.vega.builder.pipeline.context

import com.vega.builder.common.utils.setenvMock
import com.vega.builder.pipeline.di.configureKoin
import kotlinx.coroutines.runBlocking
import org.koin.mp.KoinPlatform
import kotlin.test.Test

class BuildParamsTest {
    @Test
    fun `test build BuildParams`() {
        setenvMock("ext_params","publish_ttp_source_videoeditor")
        configureKoin()
        runBlocking {
            KoinPlatform.getKoin().get<BuildParams>().loadParams()
        }

    }
}