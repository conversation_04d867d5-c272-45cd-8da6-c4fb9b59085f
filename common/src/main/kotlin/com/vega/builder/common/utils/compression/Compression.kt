package com.vega.builder.common.utils.compression

import java.io.File
import java.io.IOException

sealed class Compression {
    abstract val extension: String
    open fun validateOutputFile(file: File) {
        require(file.name.endsWith(extension)) { "Output file must have $extension extension" }
    }

    abstract fun compress(
        entries: List<FileEntry>, outputFile: File
    )

    abstract fun decompress(archiveFile: File, outputDir: File)

    // Key extension functions and utility methods
    protected fun File.validateSafeDestination(baseDir: File): File {
        val target = canonicalFile
        val base = baseDir.canonicalFile

        require(target.startsWith(base)) {
            "Path safety validation failed: ${this.path} is outside target directory scope"
        }
        return target
    }

    protected fun File.mkdirsSafe() {
        if (!exists() && !mkdirs()) throw IOException("Failed to create directory: $path")
    }

    companion object {
        fun compressFiles(
            format: Compression,
            files: List<File>,
            basePath: File,
            outputFile: File
        ) {
            require(basePath.isDirectory) { "Base path must be a valid directory" }
            format.validateOutputFile(outputFile)

            val entries = collectFileEntries(files, basePath)

            when (format) {
                SevenZ -> SevenZ.compress(entries, outputFile)
                TarGz -> TarGz.compress(entries, outputFile)
                Zip -> Zip.compress(entries, outputFile)
            }
        }

        fun decompress(archiveFile: File, outputDir: File) {
            require(archiveFile.exists()) { "Archive file ${archiveFile.path} does not exist" }
            require(outputDir.isDirectory) { "Output directory must exist" }

            when {
                archiveFile.name.endsWith(SevenZ.extension) -> SevenZ.decompress(archiveFile, outputDir)
                archiveFile.name.endsWith(TarGz.extension) -> TarGz.decompress(archiveFile, outputDir)
                archiveFile.name.endsWith(Zip.extension) -> Zip.decompress(archiveFile, outputDir)
                else -> throw IllegalArgumentException("Unsupported archive format")
            }
        }
    }
}

// File collection logic optimization
private fun collectFileEntries(
    files: List<File>,
    basePath: File
): List<FileEntry> {
    require(basePath.isDirectory) { "Base path must be a directory" }

    return files.flatMap { file ->
        when {
            file.isDirectory -> listOf(file.toEntry(basePath)) +
                    file.walk()
                        .onEnter { it != file }
                        .map { it.toEntry(basePath) }
                        .toList()

            file.isFile -> listOf(file.toEntry(basePath))
            else -> emptyList()
        }
    }.distinctBy { it.path }
}

private fun File.toEntry(base: File): FileEntry {
    val relativePath = resolveSafePath(base)
    return when {
        isDirectory -> FileEntry(this, "$relativePath/")
        else -> FileEntry(this, relativePath)
    }
}

private fun File.resolveSafePath(base: File): String {
    require(base.isDirectory) { "Base path must be a directory" }
    val normalizedBase = base.canonicalFile
    val normalizedFile = canonicalFile

    require(normalizedFile.startsWith(normalizedBase)) {
        "File ${normalizedFile.path} is not contained in base path ${normalizedBase.path}"
    }

    return normalizedFile.relativeTo(normalizedBase).invariantSeparatorsPath
}

data class FileEntry(
    val file: File,
    val path: String // Relative path in the archive
)
