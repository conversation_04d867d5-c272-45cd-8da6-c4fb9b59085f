package com.vega.builder.pipeline.task.prepare.parses.properties

import com.vega.builder.common.utils.CommonPropertiesEditor

class DefaultParse(
    private val pKey: String? = null,
    val custom: CommonPropertiesEditor.(String, String) -> Unit = { _, _ -> },
) : IPares {
    override fun CommonPropertiesEditor.parse(
        key: String,
        value: String,
    ) {
        edit(pKey ?: key, value)
        custom(key, value)
    }
}

fun customParse(custom: CommonPropertiesEditor.(String, String) -> Unit = { _, _ -> }) =
    object : IPares {
        override fun CommonPropertiesEditor.parse(
            key: String,
            value: String,
        ) {
            custom(key, value)
        }
    }
