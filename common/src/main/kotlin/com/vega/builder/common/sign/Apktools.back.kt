// package com.vega.builder.pipeline.core.tools.sign
//
// import java.io.RandomAccessFile
// import java.nio.ByteBuffer
// import java.nio.ByteOrder
//
// class Apktoolsback {
//    fun completeV3Sign(sourceFilePath: String, destFilePath: String) {
//        val sourceFile = RandomAccessFile(sourceFilePath, "r")
//        val destFile = RandomAccessFile(destFilePath, "rw")
//
//        val sourceCommentLength = getCommentLength(sourceFilePath)
//        val sourceCentralDirOffset = findCentralDirStartOffset(sourceFilePath, sourceCommentLength)
//        val (sourceSigBlockOffset, sourceSigBlockSize) = findApkSigningBlock(sourceFilePath, sourceCentralDirOffset)
//        val sourceIdValueDict = findIdValue(sourceFilePath, sourceSigBlockOffset, sourceSigBlockSize)
//
//        if (V3_SIGN_BLOCK !in sourceIdValueDict) {
//            throw RuntimeException("v3 sign not exist")
//        }
//
//        if (COMPLETION_ID in sourceIdValueDict) {
//            throw RuntimeException("completion block exist")
//        }
//
//        sourceFile.seek(0)
//        val buffer = ByteArray(sourceSigBlockOffset.toInt())
//        sourceFile.readFully(buffer)
//        destFile.write(buffer)
//
//        val idValue = mutableMapOf<Int, ByteArray>()
//        sourceIdValueDict.forEach { (key, value) ->
//            println("add source apk key : %x".format(key))
//            val (startPosition, endPosition) = value
//            val size = endPosition - startPosition
//            sourceFile.seek(startPosition)
//            val data = ByteArray(size.toInt())
//            sourceFile.readFully(data)
//            idValue[key] = data
//        }
//
//        var totalLength = 24L
//        idValue.forEach { (key, value) ->
//            println("item # %x size %d".format(key, value.size))
//            totalLength += 12 + value.size
//        }
//        println("total size %d %x".format(totalLength, totalLength))
//
//        val completionBlockSize = 4096 - (totalLength + 8) % 4096 - 12
//        println("completion block size : $completionBlockSize")
//        val completionBlock = ByteArray(completionBlockSize.toInt()) { '1'.code.toByte() }
//        idValue[COMPLETION_ID] = completionBlock
//        totalLength += completionBlockSize + 12
//
//        val totalLengthBuffer = ByteBuffer.allocate(8).order(ByteOrder.LITTLE_ENDIAN).putLong(totalLength).array()
//        destFile.write(totalLengthBuffer)
//
//        idValue.forEach { (key, value) ->
//            val itemLength = value.size + 4
//            destFile.write(ByteBuffer.allocate(8).order(ByteOrder.LITTLE_ENDIAN).putLong(itemLength.toLong()).array())
//            destFile.write(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(key).array())
//            destFile.write(value)
//        }
//
//        destFile.write(totalLengthBuffer)
//        destFile.write(ByteBuffer.allocate(8).order(ByteOrder.LITTLE_ENDIAN).putLong(APK_SIG_BLOCK_MAGIC_LO).array())
//        destFile.write(ByteBuffer.allocate(8).order(ByteOrder.LITTLE_ENDIAN).putLong(APK_SIG_BLOCK_MAGIC_HI).array())
//
//        val destCentralDirOffset = destFile.filePointer
//
//        sourceFile.seek(sourceCentralDirOffset)
//        val centralDirBuffer = ByteArray((sourceFile.length() - sourceCentralDirOffset).toInt())
//        sourceFile.readFully(centralDirBuffer)
//        destFile.write(centralDirBuffer)
//
//        destFile.seek(destFile.length() - sourceCommentLength - 6)
//        destFile.write(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(destCentralDirOffset.toInt()).array())
//
//        destFile.close()
//        sourceFile.close()
//    }
//
//    private fun getCommentLength(filePath: String): Int {
//        val file = RandomAccessFile(filePath, "r")
//        val fileSize = file.length()
//        require(fileSize >= 22) { "APK too small for ZIP End of Central Directory (EOCD) record" }
//        val maxCommentLength = minOf(fileSize - 22, 65535).toInt()
//        val eocdWithoutCommentPos = fileSize - 22
//
//        for (expectedCommentLength in 0 until maxCommentLength) {
//            val eocdPos = eocdWithoutCommentPos - expectedCommentLength
//            file.seek(eocdPos)
//            val signatureBuffer = ByteArray(4)
//            file.readFully(signatureBuffer)
//            val signature = ByteBuffer.wrap(signatureBuffer).order(ByteOrder.LITTLE_ENDIAN).int
//            if (signature == 0x06054b50) {
//                file.seek(eocdPos + 20)
//                val commentLengthBuffer = ByteArray(2)
//                file.readFully(commentLengthBuffer)
//                val actualCommentLength = ByteBuffer.wrap(commentLengthBuffer).order(ByteOrder.LITTLE_ENDIAN).short.toInt()
//                if (actualCommentLength == expectedCommentLength) {
//                    return expectedCommentLength
//                }
//            }
//        }
//        throw IllegalStateException("Could not determine the comment length.")
//    }
//
//    private fun findCentralDirStartOffset(filePath: String, commentLength: Int): Long {
//        val file = RandomAccessFile(filePath, "r")
//        val fileSize = file.length()
//
//        // Seek to the position of the central directory offset in the EOCD
//        file.seek(fileSize - commentLength - 6)
//
//        // Read the 4 bytes of the central directory offset
//        val offsetBuffer = ByteArray(4)
//        file.readFully(offsetBuffer)
//        val offset = ByteBuffer.wrap(offsetBuffer).order(ByteOrder.LITTLE_ENDIAN).int
//
//        return offset.toLong()
//    }
//
//    private fun findApkSigningBlock(filePath: String, centralDirOffset: Long): Pair<Long, Long> {
//        require(centralDirOffset >= APK_SIG_BLOCK_MIN_SIZE) {
//            "APK too small for APK Signing Block. ZIP Central Directory offset: $centralDirOffset"
//        }
//
//        val file = RandomAccessFile(filePath, "r")
//        val fileSize = file.length()
//
//        file.seek(centralDirOffset - 16)
//        val magicBuffer = ByteArray(16)
//        file.readFully(magicBuffer)
//        val magic = ByteBuffer.wrap(magicBuffer).order(ByteOrder.LITTLE_ENDIAN).asLongBuffer()
//        val magicLow = magic.get()
//        val magicHigh = magic.get()
//        require(magicHigh == APK_SIG_BLOCK_MAGIC_HI && magicLow == APK_SIG_BLOCK_MAGIC_LO) {
//            "No APK Signing Block before ZIP Central Directory"
//        }
//
//        file.seek(centralDirOffset - 24)
//        val sizeBuffer = ByteArray(8)
//        file.readFully(sizeBuffer)
//        val apkSigBlockSizeInFooter = ByteBuffer.wrap(sizeBuffer).order(ByteOrder.LITTLE_ENDIAN).long
//
//        val totalSize = apkSigBlockSizeInFooter + 8
//        val apkSigBlockOffset = centralDirOffset - totalSize
//
//        file.seek(apkSigBlockOffset)
//        file.readFully(sizeBuffer)
//        val apkSigBlockSizeInHeader = ByteBuffer.wrap(sizeBuffer).order(ByteOrder.LITTLE_ENDIAN).long
//
//        require(apkSigBlockSizeInFooter == apkSigBlockSizeInHeader) {
//            "APK Signing Block sizes in header and footer do not match: $apkSigBlockSizeInHeader vs $apkSigBlockSizeInFooter"
//        }
//
//        file.close()
//        return Pair(apkSigBlockOffset, totalSize)
//    }
//
//    private fun findIdValue(filePath: String, apkSigBlockOffset: Long, apkSigBlockLength: Long): Map<Int, Pair<Long, Long>> {
//        val file = RandomAccessFile(filePath, "r")
//        val pairsStartOffset = apkSigBlockOffset + 8
//        val pairsEndOffset = apkSigBlockOffset + apkSigBlockLength - 24
//
//        val idValueDict = mutableMapOf<Int, Pair<Long, Long>>()
//        var entryCount = 0
//        file.seek(pairsStartOffset)
//        var remaining = pairsEndOffset - file.filePointer
//
//        while (remaining > 0) {
//            entryCount++
//            require(remaining >= 8) { "Insufficient data to read size of APK Signing Block entry #$entryCount" }
//            val sizeBuffer = ByteArray(8)
//            file.readFully(sizeBuffer)
//            val pairLength = ByteBuffer.wrap(sizeBuffer).order(ByteOrder.LITTLE_ENDIAN).long
//            require(pairLength in 4..0x7fffffff) { "APK Signing Block entry #$entryCount size out of range: $pairLength" }
//
//            val nextEntryPosition = file.filePointer + pairLength
//            require(nextEntryPosition <= pairsEndOffset) {
//                "APK Signing Block entry #$entryCount size out of range: $pairLength, available: ${pairsEndOffset - file.filePointer}"
//            }
//
//            val idBuffer = ByteArray(4)
//            file.readFully(idBuffer)
//            val pairId = ByteBuffer.wrap(idBuffer).order(ByteOrder.LITTLE_ENDIAN).int
//            idValueDict[pairId] = Pair(file.filePointer, nextEntryPosition)
//
//            file.seek(nextEntryPosition)
//            remaining = pairsEndOffset - file.filePointer
//        }
//
//        file.close()
//        return idValueDict
//    }
//
//    companion object{
//        const val V3_SIGN_BLOCK = 0x7109871a
//        const val COMPLETION_ID = 0x7109871b
//        const val APK_SIG_BLOCK_MAGIC_LO = 0x3234206b636f6c42L
//        const val APK_SIG_BLOCK_MAGIC_HI = 0x20676953204b5041L
//        const val APK_SIG_BLOCK_MIN_SIZE = 32
//    }
// }
