package com.vega.builder.pipeline.task.prepare.parses.xml

import com.vega.builder.common.logger.logger
import org.w3c.dom.Comment
import org.w3c.dom.Document
import org.w3c.dom.Node
import java.io.ByteArrayInputStream
import java.io.Closeable
import java.io.File
import java.io.OutputStreamWriter
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.transform.OutputKeys
import javax.xml.transform.TransformerFactory
import javax.xml.transform.dom.DOMSource
import javax.xml.transform.stream.StreamResult

open class DefaultXmlParse(val file: File) : Closeable {

    val document: Document = load(file)

    private fun load(file: File): Document {
        val factory = DocumentBuilderFactory.newInstance()
        val builder = factory.newDocumentBuilder()
        try {
            return builder.parse(file)
        } catch (e: Exception) {
            file.readLines().forEach(logger()::info)
            throw e
        }
    }

    fun updateAttribute(
        tagName: String,
        attributeName: String,
        attributeValue: String,
    ) {
        val elements = document.getElementsByTagName(tagName)
        for (i in 0 until elements.length) {
            val element = elements.item(i)
            element.attributes.getNamedItem(attributeName).textContent = attributeValue
        }
    }

    fun uncommentNode(
        parentTagName: String,
        uncommentTagName: String,
    ) {
        val parentNode = document.getElementsByTagName(parentTagName).item(0)
        val childNodes = parentNode.childNodes
        for (i in 0 until childNodes.length) {
            val node = childNodes.item(i)
            if (node is Comment && node.data.contains(uncommentTagName)) {
                // 解析注释内容为一个新的 DocumentFragment
                val fragment = parseFragmentFromComment(node.data.trim())
                parentNode.replaceChild(fragment, node)
            }
        }
    }

    private fun parseFragmentFromComment(
        commentContent: String,
    ): Node {
        val fragment = document.createDocumentFragment()
        val factory = DocumentBuilderFactory.newInstance()
        val builder = factory.newDocumentBuilder()
        val input = ByteArrayInputStream("<root>$commentContent</root>".toByteArray(charset("UTF-8")))
        val tempDoc = builder.parse(input)
        val root = tempDoc.documentElement
        if (root.hasChildNodes()) {
            fragment.appendChild(document.importNode(root.firstChild, true))
        }
        return fragment
    }

    fun removeNodes(
        tagName: String,
    ) {
        val elements = document.getElementsByTagName(tagName)
        // Since NodeList is dynamic, we need to delete nodes from back to forward
        for (i in elements.length - 1 downTo 0) {
            val node = elements.item(i)
            node.parentNode.removeChild(node)
        }
    }

    fun removeNodesWithAttribute(
        tagName: String,
        attributeName: String,
        attributeValue: String,
    ) {
        val elements = document.getElementsByTagName(tagName)
        // Since NodeList is dynamic, we need to delete nodes from back to forward
        for (i in elements.length - 1 downTo 0) {
            val element = elements.item(i)
            val attr = element.attributes.getNamedItem(attributeName)
            if (attr != null && attr.nodeValue == attributeValue) {
                element.parentNode.removeChild(element)
            }
        }
    }

    private fun save(
        file: File,
    ) {
        val osw = OutputStreamWriter(file.outputStream())
        osw.write("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
        val transformer = TransformerFactory.newInstance().newTransformer()
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes")
        val source = DOMSource(document)
        val result = StreamResult(osw)
        transformer.transform(source, result)
    }

    override fun close() {
        save(file)
    }
}
