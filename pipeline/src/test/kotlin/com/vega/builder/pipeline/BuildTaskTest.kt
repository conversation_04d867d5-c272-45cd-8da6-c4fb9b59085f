package com.vega.builder.pipeline

import com.vega.builder.common.utils.setenvMock
import org.junit.jupiter.api.Test

/**
 *
 *
 * <AUTHOR>
 * @time 2025/2/13
 */
class BuildTaskTest {

    fun mockEnv() {
        setenvMock("SYSTEM_MOCK", "true")
        setenvMock("WORKSPACE", System.getProperty("user.home"))
        setenvMock("MAIN_GIT_URL", "******************:faceu-android/cc.git")
        setenvMock("MAIN_GIT_BRANCH", "overseas/release/14.9.0")
        setenvMock("DEFAULT_TEMPLATE", "OUT_CHANNEL_CC_GOOGLEPLAY")
        setenvMock(
            "BUILD_PARAMS", """{"RUNNING_TYPE":"ttp_build_wrapper","TTP_BUILD_SUCCESS_VERSION":"apk-build-1754054701402"}""".trimIndent()
        )
    }

    @Test
    fun testBuildTask() {
        mockEnv()
        main()
    }
}