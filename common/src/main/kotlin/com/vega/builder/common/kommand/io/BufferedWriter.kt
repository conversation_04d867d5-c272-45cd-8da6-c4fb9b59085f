package com.vega.builder.pipeline.core.kommand.io

import com.vega.builder.common.kommand.KommandException

class BufferedWriter(
    private val writer: java.io.BufferedWriter,
) {
    @Throws(KommandException::class)
    fun writeLine(line: String) {
        writer.write(line)
        writer.newLine()
    }

    @Throws(KommandException::class)
    fun flush() {
        writer.flush()
    }

    @Throws(KommandException::class)
    fun close() {
        writer.close()
    }
}
