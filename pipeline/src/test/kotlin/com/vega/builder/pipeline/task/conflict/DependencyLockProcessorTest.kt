package com.vega.builder.pipeline.task.conflict

import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.io.File

/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/20
 */
class DependencyLockProcessorTest : BaseProcessorTest() {

    override val filePath: String = ""

    override var fileContent = """
            {
                "dependencies": [
                    {
                        "artifactId": "videoeditor_repo_info", 
                        "groupId": "com.lemon.faceu", 
                        "projectName": ":videoeditor_repo_info", 
                        "publish": true, 
                        "repo": "******************:faceu/videoeditor.git", 
                        "type": "source", 
            <<<<<<< HEAD
                        "version": "*********"
            =======
                        "version": "*********"
            >>>>>>> 4a9586539e594b3d73feffdda19f0d3af5cfade8
                    }, 
                    {
                        "artifactId": "xigua-publish-common", 
                        "groupId": "com.ixigua.author", 
                        "projectName": ":xigua-publish-common", 
                        "repo": "******************:video_android/xigua_publish_vega.git", 
                        "type": "aar", 
                        "version": "11.2.0.1"
                    }, 
                    {
                        "artifactId": "lvopenplugin-core", 
                        "flavorType": true, 
                        "groupId": "com.vega.openplugin", 
                        "projectName": ":openplugin-core", 
                        "repo": "******************:faceu/LVOpenPlugin.git", 
                        "targets": [
                            {
                                "name": "prod", 
                                "version": "15.3.0.8"
                            }, 
                            {
                                "name": "oversea", 
                                "version": "15.3.0.8"
                            }
                        ], 
                        "type": "aar"
                    }, 
                    {
                        "artifactId": "lvopenplugin-assets", 
                        "flavorType": true, 
                        "groupId": "com.vega.openplugin", 
                        "projectName": ":openplugin-assets", 
                        "repo": "******************:faceu/LVOpenPlugin.git", 
                        "targets": [
                            {
                                "name": "prod", 
                                "version": "15.3.0.8"
                            }, 
                            {
                                "name": "oversea", 
                                "version": "15.3.0.8"
                            }
                        ], 
                        "type": "aar"
                    }
                ], 
                "type": "aar"
            }
        """.trimIndent()

    override val resolveConflict = """
        {
            "dependencies": [
                {
                    "artifactId": "videoeditor_repo_info", 
                    "groupId": "com.lemon.faceu", 
                    "projectName": ":videoeditor_repo_info", 
                    "publish": true, 
                    "repo": "******************:faceu/videoeditor.git", 
                    "type": "source", 
                    "version": "*********"
                }, 
                {
                    "artifactId": "xigua-publish-common", 
                    "groupId": "com.ixigua.author", 
                    "projectName": ":xigua-publish-common", 
                    "repo": "******************:video_android/xigua_publish_vega.git", 
                    "type": "aar", 
                    "version": "11.2.0.1"
                }, 
                {
                    "artifactId": "lvopenplugin-core", 
                    "flavorType": true, 
                    "groupId": "com.vega.openplugin", 
                    "projectName": ":openplugin-core", 
                    "repo": "******************:faceu/LVOpenPlugin.git", 
                    "targets": [
                        {
                            "name": "prod", 
                            "version": "15.3.0.8"
                        }, 
                        {
                            "name": "oversea", 
                            "version": "15.3.0.8"
                        }
                    ], 
                    "type": "aar"
                }, 
                {
                    "artifactId": "lvopenplugin-assets", 
                    "flavorType": true, 
                    "groupId": "com.vega.openplugin", 
                    "projectName": ":openplugin-assets", 
                    "repo": "******************:faceu/LVOpenPlugin.git", 
                    "targets": [
                        {
                            "name": "prod", 
                            "version": "15.3.0.8"
                        }, 
                        {
                            "name": "oversea", 
                            "version": "15.3.0.8"
                        }
                    ], 
                    "type": "aar"
                }
            ], 
            "type": "aar"
        }
    """.trimIndent()

    @Test
    fun testProcessConflictBlock() {
        val mockFile = mockFile()
        val mockFileReader = mockFileReader()
        val processor = DependencyLockProcessor(mockFile, mockFileReader)
        runBlocking {
            processor.process()
        }
        Assertions.assertEquals(processor.getContent(), resolveConflict)
    }
}