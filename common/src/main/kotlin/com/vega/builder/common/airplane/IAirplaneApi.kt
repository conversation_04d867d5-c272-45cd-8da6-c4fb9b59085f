package com.vega.builder.common.airplane

import com.google.gson.annotations.SerializedName
import com.vega.builder.common.network.BaseUrl
import com.vega.builder.pipeline.BuildConfig
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 *
 *
 * <AUTHOR>
 * @time 2025/2/17
 */
data class ReportConflictInfoRequest(
    @SerializedName("conflict_auto_fix")
    val conflictAutoFix: Boolean,
    @SerializedName("branch")
    val branch: String,
    @SerializedName("target_branch")
    val targetBranch: String,
    @SerializedName("project_id")
    val projectId: Int,
    @SerializedName("mr_id")
    val mrId: Int,
)

@BaseUrl("https://${BuildConfig.hide_pa_url}/")
interface IAirplaneApi {
    @POST("api/open/reportConflictInfo")
    suspend fun reportConflictInfo(@Body req: ReportConflictInfoRequest): ResponseBody

    @GET("api/open/build/out/template/search")
    suspend fun queryTemplate(
        @Query("id") id: String,
    ): AirplaneResponse<Map<String, String>>
}