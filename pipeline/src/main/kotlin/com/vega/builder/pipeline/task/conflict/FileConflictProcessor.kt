package com.vega.builder.pipeline.task.conflict

import kotlinx.coroutines.delay
import java.io.BufferedReader
import java.io.BufferedWriter
import java.io.File
import java.io.FileInputStream
import java.io.FileReader
import java.io.FileWriter
import java.io.InputStreamReader

/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/13
 */

data class ConflictBlock(
    var startLine: Int = 0,
    var endLine: Int = 0,
    val conflictLinesHead: MutableList<String> = mutableListOf(),
    val conflictLinesInsert: MutableList<String> = mutableListOf(),
    val conflictLinesBase: MutableList<String> = mutableListOf(),
)

open class ConflictFileReader(filePath: String) {
    private val file = File(filePath)

    open fun readByLine(action: (index: Int, lineContent: String) -> Unit) {
        var index = 0
        BufferedReader(InputStreamReader(FileInputStream(file), Charsets.UTF_8)).forEachLine {
            action(index, it)
            index++
        }
    }

    open fun replaceLines(conflictBlock: List<ConflictBlock>, resolveContent: List<List<String>>) {
        val tempFile = File(file.parent, "${file.name}.tmp")
        var currentBlockIndex = 0
        var blockLength = conflictBlock.size
        BufferedReader(FileReader(file)).use { reader ->
            BufferedWriter(FileWriter(tempFile)).use { writer ->
                var currentLine = 0
                var currentBlock = conflictBlock[currentBlockIndex]
                var currentBlockLines = resolveContent[currentBlockIndex]
                reader.forEachLine { line ->
                    if (currentLine >= currentBlock.startLine && currentLine <= currentBlock.endLine) {
                        if (currentLine == currentBlock.startLine) {
                            currentBlockLines.forEach { newLine ->
                                writer.write(newLine)
                                writer.newLine()
                            }
                        } else if (currentLine == currentBlock.endLine) {
                            if (currentBlockIndex < blockLength - 1) {
                                currentBlockIndex++
                                currentBlock = conflictBlock[currentBlockIndex]
                                currentBlockLines = resolveContent[currentBlockIndex]
                            }
                        }
                    } else {
                        writer.write(line)
                        writer.newLine()
                    }
                    currentLine++
                }
            }
        }
        file.delete()
        tempFile.renameTo(file)
    }

    open fun getContent(): String {
        return file.readText()
    }

    open fun verifyFile(): Boolean {
        if (!file.exists()) {
            return false
        }
        var hasConflict = false
        this.readByLine { _, line ->
            if (line.startsWith("<<<<<<<")) {
                hasConflict = true
                return@readByLine
            } else if (line.startsWith("|||||||")) {
                hasConflict = true
                return@readByLine
            }
            else if (line.startsWith(">>>>>>>")) {
                hasConflict = true
                return@readByLine
            } else if (line.startsWith("=======")) {
                hasConflict = true
                return@readByLine
            }
        }
        return hasConflict
    }
}

abstract class FileConflictProcessor {

    private val conflictBlocks: MutableList<ConflictBlock>
    protected val file: File
    protected val fileReader: ConflictFileReader

    constructor(filePath: String) : this(File(filePath), null)
    constructor(file: File, fileReader: ConflictFileReader?) {
        this.file = file
        this.fileReader = fileReader ?: ConflictFileReader(file.absolutePath)
        this.conflictBlocks = mutableListOf()
    }

    suspend fun process(): Boolean {
        try {
            println("preprocess file: ${file.absolutePath}")
            preProcess()
            println("process file: ${file.absolutePath}")
            val resolveContents = conflictBlocks.map { processConflictBlock(it) }
            replaceConflictBlock(conflictBlocks, resolveContents)
            val result = processEnd()
            println("process file end:${result}, ${file.absolutePath}")
            return result
        } catch (e: Exception) {
            e.printStackTrace()
            println("process file error: ${file.absolutePath}")
            conflictBlocks.forEach { block ->
                println("<<<<<<<")
                block.conflictLinesHead.forEach {
                    println(it)
                }
                println("|||||||")
                block.conflictLinesBase.forEach {
                    println(it)
                }
                println("=======")
                block.conflictLinesInsert.forEach {
                    println(it)
                }
                println(">>>>>>>")
                println()
            }
            return false
        }
    }

    open fun preProcess() {
        // Processing file content
        var processType = 0
        var conflictBlock = ConflictBlock()
        fileReader.readByLine { index, line ->
            if (line.startsWith("<<<<<<<")) {
                // Start a conflict block
                processType = 1
                conflictBlock.startLine = index
            } else if (line.startsWith("|||||||")) {
                processType = 2
            } else if (line.startsWith("=======")) {
                // Split conflict blocks
                processType = 3
            } else if (line.startsWith(">>>>>>>")) {
                // Ending a conflict block
                processType = 0
                conflictBlock.endLine = index
                conflictBlocks.add(conflictBlock)
                conflictBlock = ConflictBlock()
            } else {
                when (processType) {
                    1 -> {
                        conflictBlock.conflictLinesHead.add(line)
                    }
                    2 -> {
                        conflictBlock.conflictLinesBase.add(line)
                    }
                    3 -> {
                        conflictBlock.conflictLinesInsert.add(line)
                    }
                }
            }
        }
    }

    abstract suspend fun processConflictBlock(conflictBlock: ConflictBlock): List<String>

    open fun replaceConflictBlock(
        conflictBlock: List<ConflictBlock>,
        resolveContent: List<List<String>>
    ) {
        fileReader.replaceLines(conflictBlock, resolveContent)
    }

    open suspend fun processEnd(): Boolean {
        delay(1000)
        return !fileReader.verifyFile()
    }

    fun getContent(): String {
        return fileReader.getContent()
    }
}


fun isLargeThan(version1: String, version2: String): Boolean {
    val versionList1 = version1.split("[._-]+".toRegex()).filter { it.matches("\\d+".toRegex()) }
    val versionList2 = version2.split("[._-]+".toRegex()).filter { it.matches("\\d+".toRegex()) }
    val length = minOf(versionList1.size, versionList2.size)
    for (i in 0 until length) {
        if (versionList1[i].toLong() > versionList2[i].toLong()) {
            return true
        } else if (versionList1[i].toLong() < versionList2[i].toLong()) {
            return false
        }
    }
    return versionList1.size > versionList2.size
}