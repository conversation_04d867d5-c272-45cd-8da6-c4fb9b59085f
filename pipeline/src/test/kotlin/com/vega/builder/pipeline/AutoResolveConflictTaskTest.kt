package com.vega.builder.pipeline

import com.vega.builder.common.utils.setenvMock
import org.jetbrains.kotlin.context.GlobalContext
import org.junit.jupiter.api.Test
import org.koin.core.context.stopKoin

/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/23
 */
class AutoResolveConflictTaskTest {

    fun mockEnv(sourceCommit: String, targetCommit: String) {
        setenvMock("SYSTEM_MOCK", "true")
        setenvMock("WORKSPACE", System.getProperty("user.home"))
        setenvMock("MAIN_GIT_URL", "******************:faceu-android/vega.git")
        setenvMock("MAIN_GIT_COMMIT", sourceCommit)
        setenvMock("MAIN_CODE_TARGERT_PATH", "")
        setenvMock("CLONE_PATH", "")
        setenvMock("CLEAN_ALL", "")
        setenvMock("CLEAN_EXTRA_PARAMS", "")
        setenvMock("SUBMODULE_IGNORE", "")
        setenvMock("CUSTOM_CI_PROJECT_ID", "40279")
        setenvMock("CUSTOM_CI_MR_ID", "7178897")
        setenvMock(
            "BUILD_PARAMS", """
            {
                "RUNNING_TYPE":"auto_resolve_conflict",
                "TARGET_COMMIT": "$targetCommit",
                "MERGE_TARGET": "true"
            }
        """.trimIndent()
        )
    }

    fun mockCommits(): Map<String, String> {
        val commits = mutableMapOf<String, String>()
        commits["c627e84ce0a83a31f6d6c61241d034167d12a74c"] = "ca1d80d1c3deb08b0200e271a63e6d9c91b7c554"
        commits["16d631faf00c95983c103acefd3cd3523302cbbd"] = "7b6cedf058d4510bb07905b8105081b0ba7e952c"
        commits["744e8beaa3bf38b20d0478a1f7e873425b357c02"] = "e5e15eb125234989ee80822697577117edcb0ac5"
        commits["e8628e3e3e5abd125819b59e4480c16fb094cc83"] = "dbc6362c020a3fd61b796c1eb2eea7cd1dc824b6"
        commits["92d1eda71b55d1479d04254626a086d5b0dc0e41"] = "ed5fcf947f0464a3b8307b2d509215c52f5bb3c9"
        commits["14a71964058fba7f57b06565063c53dac046c6ff"] = "a8a9d66417a9ffe749005f4a338436490dc63185"
        commits["2b2ff0daa2cae62e8f7e124b6929952fed0d5599"] = "56120725a4b073a9374a94183c5a9cca87986cc6"
        commits["1e318aba8ef9341dcc86ff65d75a1e99699a264e"] = "5b0012fa1c13ac36dd39ce7cd8df7e9051b90a9f"
        commits["e83e5871cfd6e9998f00753af94d9b642b6527a8"] = "5678d55d9187ea37826b98968c907cd92882da3b"
        commits["dde3e2b1a34f75afca792fd772ace8df26e4ca61"] = "fdadf1e903e105abcdc3a0637488a605f5a064d8"
        commits["738f5f2d7948519f32ec4dd891cfaba99ff19559"] = "8e00d14d5b27e85b01131607e0f3a33d1d69b2db"
        commits["2cb74b0be628748bc1559063992d54cc21a85582"] = "caf6077a7b2f40279225bfba0fab7908fe9ed944"
        commits["f2ca99e80961f5379f1e6c51b92d113e9582778d"] = "f57df4d4007d199383474d6f0b3033c3c5a84a95"
        commits["9a903284aa4dc3d837dca21ea5efb9f4af49232c"] = "06b9db0d29b4ae74f8904beb519be8b7366dfe67"
        commits["da7bdc99e4f1b4212e63c714d395b99b0e70f055"] = "75fbd044af0bca539b51e2dc472868de4e4f9a16"
        commits["cfbc0cb84ab087143cfc53548d991ac2fccb97d8"] = "92beedb15a3cbfd1f7c4b720d773c1bab5e3d4dc"
        commits["e1dfdb4c36e21cce3a5be6437234199f33bad894"] = "a6a55cbc21c3625319b187604b31aa91bd5e7cb8"
        return commits
    }

    @Test
    fun testConflictTask() {
        val conflicts = mockCommits()
        var successCount = 0
        for ((sourceCommit, targetCommit) in conflicts) {
            println("=============================$sourceCommit, $targetCommit=============================")
            mockEnv(sourceCommit, targetCommit)
            try {
                main()
                successCount++
            } catch (e: Exception) {
            }
            stopKoin()
        }
        print("success rate: ${(successCount.toDouble() / conflicts.size) * 100}%")
    }
}