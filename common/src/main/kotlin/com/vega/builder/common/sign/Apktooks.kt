package com.vega.builder.common.sign

import com.bytedance.ByteSignJar
import com.bytedance.bytesignplugincore.ByteSignPluginExtension
import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.logger.logger
import com.vega.builder.common.utils.ResourceUtils
import com.vega.builder.common.utils.getenvSafe
import java.io.File
import kotlin.random.Random
import kotlin.random.nextInt

object Apktooks {
    private const val TAG = "APKTOOK"

    private val CHANNEL_UTIL_PATH = "/tmp/${Random.nextInt(1000..10000)}/vega/channel_utils"

    private val APK_TOOLS_FILES =
        listOf(
            "tools/apktools/apktools.py",
            "tools/apktools/channel_util.py",
            "tools/apktools/v3_apktools.py",
        )
    private val channelUtil: File
        get() = File(CHANNEL_UTIL_PATH, "tools/apktools/channel_util.py")

    init {
        APK_TOOLS_FILES.forEach {
            val target = File(CHANNEL_UTIL_PATH, it)
            val fileContent = ResourceUtils.readResource(it)
            if (!target.parentFile.exists()) {
                target.parentFile.mkdirs()
            }
            logger().info("copy channel ")
            target.writeText(fileContent)
        }
    }

    fun writeChannel(
        src: String,
        dest: String,
        channelName: String,
    ) {
        Command("python3")
            .args(channelUtil.absolutePath)
            .args("--input", src)
            .args("--output", dest)
            .args("--channel", channelName)
            .directory(channelUtil.parentFile)
            .default()
            .spawn()
            .wait()
    }

    fun signSingleApk(
        src: String,
        dest: String,
        appId: String,
        debug: Boolean,
        signType: SignType,
        minSdkVersion: String? = null,
    ) {
        val extension = ByteSignPluginExtension()
        extension.applicationId = appId
        extension.signApkFileName = src
        extension.outputApkFileName = dest
        extension.minSdkVersion = minSdkVersion
        extension.isV1SigningEnabled = true
        extension.isV2SigningEnabled =
            (signType.ordinal >= SignType.V2.ordinal)
                .apply { logger().info("enableV2Signing: $this") }
        extension.isV3SigningEnabled =
            (signType.ordinal >= SignType.V3.ordinal)
                .apply { logger().info("enableV3Signing: $this") }
        extension.isV4SigningEnabled =
            (signType.ordinal >= SignType.V4.ordinal)
                .apply { logger().info("enableV4Signing: $this") }
        val signJar = ByteSignJar()
        signJar::class.java
            .getDeclaredMethod("signSingleApk", ByteSignPluginExtension::class.java, String::class.java)
            .apply { isAccessible = true }
            .invoke(
                ByteSignJar(),
                extension,
                if (debug) "debug-code-signing-service.byted.org:31022" else "release-code-signing-service.byted.org:31022",
            )
    }

    enum class SignType {
        V1,
        V2,
        V3,
        V4,
    }
}
