package com.vega.builder.common.tos

import com.bytedance.storage.tos.Tos
import com.bytedance.storage.tos.TosClient
import com.bytedance.storage.tos.TosProperty
import com.bytedance.storage.tos.auth.BucketAccessKeyCredentials
import com.vega.builder.common.logger.logger
import com.vega.builder.pipeline.BuildConfig
import java.io.File
import kotlin.time.measureTime


enum class TosConfig(
    val bucket: String,
    private val ak: String,
    private val timeout: Int = 10,
    private val endpoint: String = BuildConfig.hide_tos_endpoint_url
) {
    LvBuildScript(
        bucket = "lv-android-build-script",
        ak = BuildConfig.hide_LvBuildScript_ak,
    ),
    LvBuildArtifact(
        bucket = "lv-build-artifact",
        ak = BuildConfig.hide_LvBuildArtifact_ak,
    ),
    LvBuildResult(
        bucket = "faceu-android-package-backup",
        ak = BuildConfig.hide_LvBuildResult_ak,
        timeout = 600
    );

    fun createTosClient(): TosClient = Tos.defaultTosClient(
        TosProperty() // 并发安全
            .setCredentials(BucketAccessKeyCredentials(bucket, ak))
            .setBucket(bucket)
            .setTimeout(timeout)
            .setEndpoints(endpoint)
    )
}

suspend fun TosClient.slicePutObject(key: String, file: File): Boolean {
    if (file.exists()) {
        try {
            val time = SliceUpload(this, key).upload(file)
            logger().info("Uploaded: $key cost time: $time")
            return true
        } catch (e: Exception) {
            logger().error("Upload file error", e)
        }
    } else {
        logger().warn("Upload file does not exist: $file")
    }
    return false
}