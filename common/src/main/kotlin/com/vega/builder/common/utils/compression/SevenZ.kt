package com.vega.builder.common.utils.compression

import com.vega.builder.common.logger.logger
import org.apache.commons.compress.archivers.sevenz.SevenZArchiveEntry
import org.apache.commons.compress.archivers.sevenz.SevenZFile
import org.apache.commons.compress.archivers.sevenz.SevenZOutputFile
import java.io.File
import kotlin.collections.forEach

object SevenZ : Compression() {
    override val extension = ".7z"

    override fun decompress(archiveFile: File, outputDir: File) {
        require(archiveFile.extension == "7z") { "File must be a .7z archive" }
        require(outputDir.isDirectory) { "Output directory must exist" }

        SevenZFile.builder().setFile(archiveFile).get().use { sevenZInput ->
            generateSequence { sevenZInput.nextEntry }.forEach { entry ->
                processSevenZEntry(entry, sevenZInput, outputDir)
            }
        }
    }

    override fun compress(entries: List<FileEntry>, outputFile: File) {
        SevenZOutputFile(outputFile).use { sevenZOutput ->
            entries.forEach { (file, path) ->
                file.inputStream().buffered().use { input ->
                    logger().info("Compressing: $path")
                    val entry = sevenZOutput.createArchiveEntry(file, path)
                    sevenZOutput.putArchiveEntry(entry)
                    var bytesCopied: Long = 0
                    val buffer = ByteArray(DEFAULT_BUFFER_SIZE)
                    var bytes = input.read(buffer)
                    while (bytes >= 0) {
                        sevenZOutput.write(buffer, 0, bytes)
                        bytesCopied += bytes
                        bytes = input.read(buffer)
                    }
                    sevenZOutput.closeArchiveEntry()
                }
            }
        }
    }

    private fun processSevenZEntry(
        entry: SevenZArchiveEntry,
        sevenZInput: SevenZFile,
        outputDir: File
    ) {
        val targetFile = outputDir.resolve(entry.name)
            .validateSafeDestination(outputDir)

        when {
            entry.isDirectory -> targetFile.mkdirsSafe()
            else -> {
                targetFile.parentFile.mkdirsSafe()
                targetFile.outputStream().buffered().use { out ->
                    val buffer = ByteArray(DEFAULT_BUFFER_SIZE)
                    var bytesRead = sevenZInput.read(buffer)
                    while (bytesRead >= 0) {
                        out.write(buffer, 0, bytesRead)
                        bytesRead = sevenZInput.read(buffer)
                    }
                }
                entry.lastModifiedDate?.time?.let {
                    targetFile.setLastModified(it)
                }
            }
        }
    }
}
