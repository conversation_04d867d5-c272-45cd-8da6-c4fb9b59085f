plugins {
    kotlin("jvm")
    alias(libs.plugins.kotlinx.serialization)
    alias(libs.plugins.build.shell)

}
version = "1.0.0"

dependencies {
    implementation(fileTree(mapOf("dir" to rootProject.file("libs").absolutePath, "include" to listOf("*.jar"))))
    implementation(kotlin("stdlib-common"))
    implementation(libs.bundles.kotlinx.official.libs)
    implementation(libs.okio)
    implementation(libs.bundles.retrofit)
    implementation(libs.jgit)
    implementation(libs.gson)
    implementation(libs.bundles.log)
    implementation(libs.tos.core)
    implementation(libs.clikt) //https://ajalt.github.io/clikt/whyclikt/
    implementation(project(":common"))
    implementation(kotlin("reflect"))
}
kotlin {
    jvmToolchain(11)
}

standalone {
    mainClass = "com.vega.builder.launcher.MainKt"
    baseName = "launcher-cli"
}

