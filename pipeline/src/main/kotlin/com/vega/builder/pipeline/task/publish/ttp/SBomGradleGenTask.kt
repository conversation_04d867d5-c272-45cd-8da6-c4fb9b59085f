package com.vega.builder.pipeline.task.publish.ttp

import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.context.isGradleLoggerDebugMode
import org.koin.core.component.inject
import java.io.File

@TaskDefinition("SBomTask", stage = Stage.After, "sbom generate and upload")
class SBomGradleGenTask:PipelineTask() {
    val buildParams: BuildParams by inject()
    val workspaceParams: WorkspaceParams by inject()
    override suspend fun run() {
        val result =
            Command("./gradlew")
                .directory(File(workspaceParams.targetProjectDir))
                .arg(":CapCut:cyclonedxBom")
                .arg("-PwithSBOM")
                .arg("-info")
                .arg("--stacktrace")
                .apply {
                    if (buildParams.isGradleLoggerDebugMode) {
                        arg("--debug")
                    }
                }
                .default()
                .spawn()
                .wait()
        if (result != 0) {
            throw PipelineThrowable(ErrorType.SBomGenerateFailed)
        }
    }
}