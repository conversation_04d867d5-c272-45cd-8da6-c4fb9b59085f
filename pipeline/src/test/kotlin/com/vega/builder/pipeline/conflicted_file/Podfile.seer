- AALVLaunchTracker (path 'Modules/AALVLaunchTracker')
- ABRInterface (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- ABRModule (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- ABUAdCsjAdapter (*******.1, from '******************:ad/ad_union_source_repo.git')
- ADFeelGood (2.1.16, from '******************:iOS_Library/privatethird_binary_repo.git')
- Ads-CN (*******-alpha.4, from '******************:ad/ad_union_source_repo.git')
- AFgzipRequestSerializer (0.0.6, from '******************:iOS_Library/privatethird_source_repo.git')
- AFNetworking (2.7.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- AGFX_pub (********-lv, from '******************:iOS_Library/privatethird_source_repo.git')
- AlipaySDK (2.0.9-noutdid, from '******************:iOS_Library/privatethird_binary_repo.git')
- AliyunFaceSDK (2.0.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- AMapFoundation-NO-IDFA (1.6.6, from '******************:iOS_Library/publicthird_binary_repo.git')
- AnimaX (3.2.3-rc.5, from '******************:iOS_Library/privatethird_source_repo.git')
- Annie (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- AnyCodable (0.4.1, from '******************:iOS_Library/privatethird_source_repo.git')
- AppAuth (1.6.2, from '******************:iOS_Library/publicthird_source_repo.git')
- AppCheckCore (10.18.0, from '******************:iOS_Library/publicthird_source_repo.git')
- AppLovinMediationFacebookAdapter (********, from '******************:iOS_Library/publicthird_binary_repo.git')
- AppLovinMediationGoogleAdapter (*********, from '******************:iOS_Library/publicthird_source_repo.git')
- AppLovinMediationIronSourceAdapter (*******.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- AppLovinMediationMintegralAdapter (7.7.2.0.1, from '******************:iOS_Library/publicthird_source_repo.git')
- AppLovinMediationUnityAdsAdapter (4.8.0.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- AppLovinMediationVungleAdapter (7.4.3.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- AppLovinSDK (13.1.0, from '******************:iOS_Library/publicthird_source_repo.git')
- AppsFlyerFramework (6.14.3, from '******************:iOS_Library/publicthird_source_repo.git')
- arkcrypto-minigame-iOS (0.1.6, from '******************:iOS_Library/privatethird_binary_repo.git')
- ArtistOpenPlatformSDK (0.0.32, from '******************:iOS_Library/privatethird_source_repo.git')
- Aspects (1.4.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- audiosdk (14.72.3-lv, from '******************:iOS_Library/privatethird_binary_repo.git')
- AWEAnywhereArena (0.2.8, from '******************:iOS_Library/privatethird_binary_repo.git')
- AWECloudCommand (1.3.13, from '******************:iOS_Library/privatethird_binary_repo.git')
- AWEContext (0.0.12, from '******************:iOS_Library/privatethird_binary_repo.git')
- AWELazyRegister (11.2.0.71-alpha.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- AWEPrereviewDebug (1.0.1, from '******************:iOS_Library/privatethird_source_repo.git')
- AWERTL (12.4.0.46-capcut, from '******************:iOS_Library/douyin_binary_repo.git')
- AWESDKManager (2.0.2, from '******************:ugc/AWESpecs.git')
- BaseDevtool (3.2.3-rc.5, from '******************:iOS_Library/privatethird_source_repo.git')
- BCAAFrameworkKit (1.0.14, from '******************:iOS_Library/privatethird_binary_repo.git')
- BCAAKit (1.0.17, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDAAppStore (2.6.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDAAppStoreI18N (0.3.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDABTestSDK (3.0.11, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDAComponents (7.4.5.6, from '******************:iOS_Library/toutiao_binary_repo.git')
- BDAComponentsI18N (0.0.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDADetailSDK (0.4.42--jianying, from '******************:iOS_Library/toutiao_source_repo.git')
- BDAdHubSDK (6.9.3.4-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDADSDK (1.0.26, from '******************:iOS_Library/privatethird_source_repo.git')
- BDALog (0.9.3.3-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDAlogProtocol (1.3.3.0-rc.6, from '******************:iOS_Library/privatethird_source_repo.git')
- BDALokiSDK (5.0.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDARewardedVideo (2.23.0.2, from '******************:iOS_Library/privatethird_source_repo.git')
- BDARifleSDK (1.0.24, from '******************:iOS_Library/privatethird_source_repo.git')
- BDARuntimeDefaultImpl (1.0.5--defaultimpl-jianying, from '******************:iOS_Library/privatethird_source_repo.git')
- BDASDKBaseKit (1.0.11, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDASDKMediator (7.6.0.8, from '******************:iOS_Library/privatethird_source_repo.git')
- BDASDKMediatorI18N (0.0.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDASplashSDKI18N (2.0.3-mediation, from '******************:iOS_Library/privatethird_source_repo.git')
- BDAssert (3.0.8, from '******************:iOS_Library/privatethird_source_repo.git')
- BDAtmSDK (2.0.5, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDATrackerI18N (0.0.8, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDBOEManager (4.8.8-rc.0-jianying, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDCodeCoverageCollectTool (0.2.11, from '******************:iOS_Library/toutiao_binary_repo.git')
- BDCommonABTestSDK (0.1.17, from '******************:iOS_Library/toutiao_binary_repo.git')
- bdcpython (2.0.33, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDDataDecorator (2.0.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDDyldNotification (0.0.8-alpha.1, from '******************:iOS_Library/privatethird_source_repo.git')
- BDEPCV (0.0.1.7-rc.3-jy, from '******************:iOS_Library/privatethird_source_repo.git')
- BDETKit (1.3.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDExtensionTracker (1.1.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDFeedBack (1.4.0, from '******************:iOS_Library/privatethird_source_repo.git')
- BDFishhook (0.2.13-rc.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDInstall (1.12.6-alpha.24, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDInstallExtension (1.0.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDInstallPopup (0.4.3-alpha.11, from '******************:iOS_Library/privatethird_source_repo.git')
- BDiOSDebugBinary (0.0.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDJSBridgeAuthManager (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDLarkAuth (1.0.23.9-bugfix, from '******************:iOS_Library/privatethird_source_repo.git')
- BDLRepairer (1.0.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDMannor (4.1.12, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDMannorDebugTool (0.1.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDMannorModel (0.5.56, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDMemoryMatrix (0.0.24-rc.5-heimdallr, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDMetaSecInterface (0.0.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDModel (0.1.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDModelDynamic (1.0.10, from '******************:iOS_Library/toutiao_binary_repo.git')
- BDMonitorProtocol (1.2.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDMPConfigCenter (1.0.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDNativeWebComponent (1.0.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDNetworkTag (0.2.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDPeephole (0.1.0-alpha.81, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDPipoProtectKit (2.0.0, from '******************:iOS_Library/privatethird_source_repo.git')
- BDPolicyKit (4.0.0-cn, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDPreloadSDK (0.4.23, from '******************:iOS_Library/international_crossplatform_source_repo.git')
- BDRegionData (1.5.14, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDRpcService (0.0.21-rc.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDRuleEngine (3.4.25, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDSheoSDK (5.0.0, from '******************:iOS_Library/privatethird_source_repo.git')
- BDSiriSuggestion (0.1.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDSSOAuthSDK (1.0.8, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDSword (1.0.2-alpha.6-fixConfictNoLog, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDTestCoverage (1.1.0-alpha.16--precise, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDTicketGuard (2.0.6, from '******************:iOS_Library/privatethird_source_repo.git')
- BDTracker (2.13.6-rc.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDTrackerAgent (1.0.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDTrackerProtocol (2.6.11, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDTrackerTracer (1.2.5, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDTuring (3.6.4-rc.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDTuringI18n (2.3.10-alpha.12-non-tt, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGAccountOnekeyLogin (2.1.6, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGADAttribution (0.0.2, from '******************:iOS_Library/privatethird_source_repo.git')
- BDUGChannel (0.1.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGContainer (1.2.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGCrossDebugTool (0.0.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGDeepLink (3.0.5, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGIDFAProviderInterface (0.0.2, from '******************:iOS_Library/privatethird_source_repo.git')
- BDUGLocationKit (2.16.11, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGPushSDK (2.14.6.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGSDKStatistics (0.2.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGSecGuardInterface (1.0.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGSecure (1.0.8.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGSettingsInterface (1.2.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGSyncSDK (1.0.1-rc.33-v2, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGTrackerInterface (1.2.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGTuringInterface (2.5.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGUnionSDK (6.2.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUGXAccountKit (1.5.12-alpha.26, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUPCRpc (0.0.66, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDUPCSDK (1.0.7-rc.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDVCFileUploadClient (1.0.202.24, from '******************:iOS_Library/privatethird_source_repo.git')
- BDWCDB (1.0.6, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDWCDB.swift (1.0.7, from '******************:iOS_Library/privatethird_source_repo.git')
- BDWebCore (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDWebDebugger (0.2.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDWebImage (1.8.89.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDWebKit (3.0.0-douyin, from '******************:iOS_Library/privatethird_binary_repo.git')
- BDXBridgeKit (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDXCommon (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDXDebugTool (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDXLynxKit (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDXMonitor (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDXResourceLoader (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDXSchemaKit (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDXServiceCenter (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- BDXWebKit (4.4.8, from '******************:iOS_Library/privatethird_source_repo.git')
- BeeNotify (13.1.0.1, from '******************:iOS_Library/lv-ios_source_repo.git')
- BMFMods (2.53.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- BMFSDK (2.1.18, from '******************:iOS_Library/privatethird_binary_repo.git')
- Bolts (1.9.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- boost (1.72.0, from '******************:iOS_Library/privatethird_source_repo.git')
- boringssl (0.3.12-qishui, from '******************:iOS_Library/toutiao_binary_repo.git')
- BraveTroops (14.0.0.1, from '******************:iOS_Library/privatethird_source_repo.git')
- BTDADApplink (2.0.15, from '******************:iOS_Library/privatethird_binary_repo.git')
- BTDPoketto (0.1.11, from '******************:iOS_Library/privatethird_source_repo.git')
- BUPlayableAdKit (6.7.0.2-bugfix, from '******************:iOS_Library/privatethird_source_repo.git')
- BURelyFoundation_Global (1.0.0.6, from '******************:iOS_Library/privatethird_source_repo.git')
- BVCParser (0.9.1, from '******************:iOS_Library/privatethird_source_repo.git')
- ByteADTracker (1.1.25, from '******************:iOS_Library/privatethird_binary_repo.git')
- ByteBenchiOS (2.0.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- byted_bach_sdk (0.0.33-alpha.0, from '******************:iOS_Library/privatethird_source_repo.git')
- byted_cert (4.10.4-alpha.95, from '******************:iOS_Library/privatethird_binary_repo.git')
- ByteDanceKit (git '******************:ugc/ByteDanceKit.git', commit 'd993a91d42c29fda3ee32fe1cedf54f46ec08f72')
- bytenn-ios (3.10.30, from '******************:iOS_Library/privatethird_source_repo.git')
- ByteRtcSDK (3.45.708, from '******************:iOS_Library/privatethird_source_repo.git')
- ByteWebView (3.4.5.6-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- CCCreator_iOS (19.6.0.999-LV, from '******************:iOS_Library/lv-ios_source_repo.git')
- CCUX (path 'Modules/CCUX')
- CCUXDebug (path 'Modules/CCUX')
- ChunkStreamPredict (2.1.0-alpha.0-capcut, from '******************:iOS_Library/privatethird_source_repo.git')
- CJPay (7.3.1-alpha.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- CJSwiftIAPStore (7.3.1-alpha.3, from '******************:iOS_Library/privatethird_source_repo.git')
- CocoaAsyncSocket (7.6.6, from '******************:iOS_Library/privatethird_binary_repo.git')
- CocoaMarkdown (0.0.17-rc.0-capcut, from '******************:iOS_Library/privatethird_source_repo.git')
- CodableWrapper (0.0.2, from '******************:iOS_Library/privatethird_source_repo.git')
- CombineX (0.1.2-alpha.7, from '******************:iOS_Library/retouch_source_repo.git')
- CommerceSDK (16.9.0.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- Curry (5.0.0, from '******************:iOS_Library/publicthird_source_repo.git')
- Cyber (0.1.2, from '******************:ugc/UGCSpecs.git')
- DebugRouter (5.0.11, from '******************:iOS_Library/privatethird_binary_repo.git')
- DebugRouterServiceImpl (2.0.0, from '******************:iOS_Library/privatethird_source_repo.git')
- DiffMatchPc (1.0.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- Dolphin (0.1.19, from '******************:iOS_Library/privatethird_source_repo.git')
- DouyinOpenPlatformSDK (5.32.2, from '******************:iOS_Library/privatethird_source_repo.git')
- DouyinSDK (1.0.4.11-jy, from '******************:iOS_Library/privatethird_binary_repo.git')
- DypaySDK (0.2.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- EAccountApiSDK (4.0.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- EffectPlatformSDK (2.9.153-jianying, from '******************:iOS_Library/privatethird_binary_repo.git')
- espresso (3.0.9, from '******************:iOS_Library/privatethird_binary_repo.git')
- EverCloud (14.3.0-rc.0, from '******************:iOS_Library/lv-ios_source_repo.git')
- FastCoding (3.2.2, from '******************:iOS_Library/publicthird_binary_repo.git')
- fastcv (8.2.2-lv, from '******************:iOS_Library/privatethird_binary_repo.git')
- FBAudienceNetwork (6.15.2, from '******************:iOS_Library/publicthird_source_repo.git')
- FBRetainCycleDetector (0.2.0-alpha.39-swift, from '******************:iOS_Library/toutiao_binary_repo.git')
- FBSDKCoreKit (9.3.0, from '******************:iOS_Library/publicthird_source_repo.git')
- FBSDKLoginKit (9.3.0, from '******************:iOS_Library/publicthird_source_repo.git')
- FBSDKShareKit (9.3.0, from '******************:iOS_Library/publicthird_source_repo.git')
- FileMD5Hash (2.0.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- FirebaseAnalytics (10.23.0, from '******************:iOS_Library/publicthird_source_repo.git')
- FirebaseCore (10.23.0, from '******************:iOS_Library/publicthird_source_repo.git')
- FirebaseCoreInternal (10.23.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- FirebaseInstallations (10.23.0, from '******************:iOS_Library/publicthird_source_repo.git')
- FLAnimatedImage (1.0.16.1-bugfix, from '******************:iOS_Library/lv-ios_binary_repo.git')
- FLEX (4.4.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- FMDB (2.7.11, from '******************:iOS_Library/privatethird_binary_repo.git')
- FrameRecover (********-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- Gaia (3.1.5, from '******************:iOS_Library/privatethird_binary_repo.git')
- gaia_lib_publish (********-LV, from '******************:iOS_Library/IES-UGC_binary_repo.git')
- GCDWebServer (path 'Modules/GCDWebServer')
- Godzippa (2.1.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- Google-Mobile-Ads-SDK (11.13.0, from '******************:iOS_Library/publicthird_source_repo.git')
- GoogleAPIClientForREST (1.3.11, from '******************:iOS_Library/publicthird_binary_repo.git')
- GoogleAppMeasurement (10.23.0, from '******************:iOS_Library/publicthird_source_repo.git')
- GoogleDataTransport (9.1.2, from '******************:iOS_Library/publicthird_binary_repo.git')
- GoogleMobileAdsMediationAppLovin (********, from '******************:iOS_Library/publicthird_binary_repo.git')
- GoogleMobileAdsMediationFacebook (********, from '******************:iOS_Library/publicthird_binary_repo.git')
- GoogleMobileAdsMediationIronSource (*******, from '******************:iOS_Library/publicthird_binary_repo.git')
- GoogleMobileAdsMediationMintegral (*******, from '******************:iOS_Library/publicthird_binary_repo.git')
- GoogleMobileAdsMediationUnity (*******, from '******************:iOS_Library/publicthird_binary_repo.git')
- GoogleMobileAdsMediationVungle (7.4.3.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- GoogleSignIn (7.1.0-fac-beta-1.0.0, from '******************:iOS_Library/publicthird_source_repo.git')
- GoogleUserMessagingPlatform (2.1.0, from '******************:iOS_Library/publicthird_source_repo.git')
- GoogleUtilities (7.12.0, from '******************:iOS_Library/publicthird_source_repo.git')
- GTMAppAuth (4.0.0, from '******************:iOS_Library/publicthird_source_repo.git')
- GTMSessionFetcher (2.3.0, from '******************:iOS_Library/publicthird_source_repo.git')
- Heimdallr (0.8.71.0-rc.1.1-bugfix.1-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- HeliumEffectAdapterHeader (0.1.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- Hermas (1.0.18, from '******************:iOS_Library/privatethird_binary_repo.git')
- HeycanSDK (1.4.0.2.2-bugfix, from '******************:iOS_Library/privatethird_source_repo.git')
- HMDDoctor (3.1.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- HMDGWPASan (0.1.0-alpha.15, from '******************:iOS_Library/privatethird_binary_repo.git')
- HTSEventKit (21.6.0.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- HTSKVStore (1.3.6, from '******************:iOS_Library/livebroadcast_binary_repo.git')
- HTSLiveAsyncLabel (0.1.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- HTSLiveGiftSyncKit (1.0.5, from '******************:iOS_Library/privatethird_binary_repo.git')
- HTSServiceKit (0.1.3.88, from '******************:iOS_Library/privatethird_source_repo.git')
- HTTPParserC (2.9.4, from '******************:iOS_Library/publicthird_source_repo.git')
- HybridKit (*******-rc.1-lv, from '******************:iOS_Library/international_crossplatform_source_repo.git')
- iBaseKit (1.0.48, from '******************:iOS_Library/privatethird_source_repo.git')
- IESAppLogger_iOS (0.1.11, from '******************:iOS_Library/privatethird_source_repo.git')
- IESDI (1.1.2, from '******************:iOS_Library/privatethird_source_repo.git')
- IESDynamicPB (2.6.4.0-alpha.2, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECLiveImpl (3.1.8.19, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECLiveInterface (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECMallImpl (3.1.8.26, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECMallInterface (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECMarketingInterface (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECommerce (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECommerceBasic (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECommerceBizUI (3.1.8.19, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECommerceInterface (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECommerceModel (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECommerceService (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECProductDetailImpl (3.1.8.19, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECProductDetailInterface (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECShopImpl (3.1.8.19, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECShopInterface (3.1.8.10-saas-1775-1703751504, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECSliceX (2.2.19, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECSliceXYoga (2.2.19, from '******************:iOS_Library/privatethird_source_repo.git')
- IESECSliceXYogaKit (2.2.19, from '******************:iOS_Library/privatethird_source_repo.git')
- IESForestKit (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- IESGeckoEncrypt (0.0.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- IESGeckoKit (4.0.9.7-bugfix, from '******************:iOS_Library/privatethird_source_repo.git')
- IESGeckoKitDebug (4.0.9.1-bugfix, from '******************:iOS_Library/privatethird_source_repo.git')
- IESInject (3.6.0, from '******************:iOS_Library/douyin_binary_repo.git')
- IESJSBridgeCore (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- IESLatch (2.3.9, from '******************:iOS_Library/privatethird_binary_repo.git')
- IESLiveCommunity (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveCommunityImpl (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveDFAnimation (1.0.21-jy, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveFoundation (3.1.3.117, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveGame (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveGameImpl (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveIAPManager (0.1.1, from '******************:iOS_Library/privatethird_binary_repo.git')
- IESLiveIM (2.8.7.2, from '******************:iOS_Library/livebroadcast_binary_repo.git')
- IESLiveInfrastructure (3.1.3.7-alpha.0-jy, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveKit (3.1.3.117, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveModel (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLivePlayer (3.0.7.5-alpha.1-jy, from '******************:iOS_Library/privatethird_source_repo.git')
- IESLiveResourcesButler (1.9.6.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- IESLiveRevenue (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveRevenueImpl (3.1.3.117, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveSaaSKit (3.4.7.19, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveSaaSMiddleware (3.4.7.19, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveService (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveShow (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveShowImpl (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveSocial (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveSocialImpl (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveVendor (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveVendorImpl (3.1.3.116, from '******************:iOS_Library/livebroadcast_source_repo.git')
- IESLiveVideoGift (1.4.21, from '******************:iOS_Library/privatethird_binary_repo.git')
- IESMetadataStorage (1.0.20, from '******************:iOS_Library/privatethird_binary_repo.git')
- IESPrefetch (1.1.13, from '******************:iOS_Library/privatethird_binary_repo.git')
- IESStore (0.3.10, from '******************:iOS_Library/privatethird_binary_repo.git')
- IESWebKit (4.4.5-jianying, from '******************:iOS_Library/privatethird_source_repo.git')
- IESWebViewMonitor (4.4.4, from '******************:iOS_Library/privatethird_source_repo.git')
- IGListKit (3.4.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- ImagineNetwork (1.2.1-alpha.0, from '******************:iOS_Library/privatethird_source_repo.git')
- IMCResourceSDK (1.0.3.3-bugfix, from '******************:iOS_Library/privatethird_source_repo.git')
- IntertrustDrm (1.0.0.61, from '******************:iOS_Library/privatethird_source_repo.git')
- iosMath (0.9.4, from '******************:iOS_Library/publicthird_binary_repo.git')
- IronSourceSDK (*******, from '******************:iOS_Library/publicthird_binary_repo.git')
- Jato (2.1.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- JLRoutes (2.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- JSONModel (1.8.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- JXPagingView (1.1.16, from '******************:iOS_Library/publicthird_source_repo.git')
- Kingfisher (5.14.4, from '******************:iOS_Library/lv-ios_source_repo.git')
- KKConnectorServer (1.0.7, from '******************:iOS_Library/retouch_source_repo.git')
- Krypton (3.2.3-rc.5, from '******************:iOS_Library/privatethird_source_repo.git')
- KVOController (1.2.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- LarkSSO (1.1.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- Lemon8OpenSDK (0.1.1-alpha.7, from '******************:iOS_Library/privatethird_binary_repo.git')
- lens (19.5.4-LV, from '******************:iOS_Library/privatethird_binary_repo.git')
- leveldb (1.18, from '******************:iOS_Library/publicthird_binary_repo.git')
- lib_h_dec (1.6.225, from '******************:iOS_Library/privatethird_source_repo.git')
- libbytevc1enc (1.9.7.2.3-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- libextobjc (0.6, from '******************:iOS_Library/publicthird_binary_repo.git')
- libttheif_ios (1.1.6, from '******************:iOS_Library/privatethird_binary_repo.git')
- libwebp (1.3.2, from '******************:iOS_Library/publicthird_binary_repo.git')
- LightDatabase (15.9.0.2, from '******************:iOS_Library/lv-ios_source_repo.git')
- LightNetwork (16.9.0.3, from '******************:iOS_Library/lv-ios_source_repo.git')
- LightNetworkSpeedManager (13.4.0.2, from '******************:iOS_Library/lv-ios_source_repo.git')
- LightRouter (0.0.2, from '******************:iOS_Library/lv-ios_source_repo.git')
- LightStorage (15.3.0.2, from '******************:iOS_Library/lv-ios_source_repo.git')
- LivePlayer (1.10.202.4-aweme, from '******************:iOS_Library/privatethird_binary_repo.git')
- LiveStreamStrategySDK (1.10.202.5-jianyin, from '******************:iOS_Library/privatethird_binary_repo.git')
- LMBaseKit (1.0.2, from '******************:iOS_Library/privatethird_source_repo.git')
- LMDB (1.1.0-alpha.0, from '******************:iOS_Library/privatethird_source_repo.git')
- LMDevice (1.0.11, from '******************:iOS_Library/privatethird_binary_repo.git')
- LMHybridMonitor (0.0.9, from '******************:iOS_Library/privatethird_binary_repo.git')
- LMLogger (2.0.7, from '******************:iOS_Library/privatethird_source_repo.git')
- LMLoggerALogImpl (2.0.7, from '******************:iOS_Library/privatethird_source_repo.git')
- LMLynxBridge (0.0.12, from '******************:iOS_Library/faceu_source_repo.git')
- LMLynxKit (********, from '******************:iOS_Library/privatethird_source_repo.git')
- LMNotificationService (1.0.5, from '******************:iOS_Library/faceu_binary_repo.git')
- LMShare (1.2.64, from '******************:iOS_Library/faceu_source_repo.git')
- LookinServer (1.2.7, from '******************:iOS_Library/publicthird_source_repo.git')
- lottie-ios (2.6.5, from '******************:iOS_Library/privatethird_source_repo.git')
- lottie-swift (3.1.11-alpha.8, from '******************:iOS_Library/lv-ios_source_repo.git')
- LVAbility (13.2.0.5, from '******************:iOS_Library/lv-ios_source_repo.git')
- LVABSetting (path 'Modules/LVABSetting')
- LVABSettingBiz (path 'Modules/LVABSetting')
- LVABSettingCommunity (path 'Modules/LVABSetting')
- LVABSettingEditor (path 'Modules/LVABSetting')
- LVABSettingRegister (path 'Modules/LVABSetting')
- LVAdCapCut (path 'Modules/LVAdCapCut')
- LVAdCapCutAPI (path 'Modules/LVAdCapCut')
- LVAdCommon (path 'Modules/LVAdCommon')
- LVAdCommonAPI (path 'Modules/LVAdCommon')
- LVAdPlatform (path 'Modules/LVAdPlatform')
- LVAIDubbingScene (path 'Modules/LVAIDubbingScene')
- LVAIDubbingSceneAPI (path 'Modules/LVAIDubbingScene')
- LVAIPrompt (path 'Modules/LVAIPrompt')
- LVAIPromptAPI (path 'Modules/LVAIPrompt')
- LVAIShorts (path 'Modules/LVAIShorts')
- LVAIShortsAPI (path 'Modules/LVAIShorts')
- LVAlbum (path 'Modules/LVAlbum')
- LVAppBizTracker (path 'Modules/LVAppBizTracker')
- LVAppConfiguration (path 'Modules/LVAppConfiguration')
- LVAppRestriction (path 'Modules/LVInfra')
- LVAppService (path 'Modules/LVAppService')
- LVAppSettings (path 'Modules/LVAppSettings')
- LVAppSettingsAPI (path 'Modules/LVAppSettings')
- LVAppVersion (path 'Modules/LVInfra')
- LVAssembly (path 'Modules/LVAssembly')
- LVAssetProcess (path 'Modules/LVAssetProcess')
- LVAssetProcessAPI (path 'Modules/LVAssetProcess')
- LVAssetProcessBasic (path 'Modules/LVAssetProcess')
- LVAuthority (path 'Modules/LVAuthority')
- LVCamera (path 'Modules/LVCamera')
- LVCameraSwift (path 'Modules/LVCameraSwift')
- LVCameraSwiftAPI (path 'Modules/LVCameraSwift')
- LVCanvasRatio (path 'Modules/LVCanvasRatio')
- LVCanvasRatioAPI (path 'Modules/LVCanvasRatio')
- LVCloudSpace (path 'Modules/LVCloudSpace')
- LVCloudSpaceAPI (path 'Modules/LVCloudSpace')
- LVCocreate (path 'Modules/LVCocreate')
- LVCocreateAPI (path 'Modules/LVCocreate')
- LVCommerceAI (path 'Modules/LVCommerceAI')
- LVCommerceAIAPI (path 'Modules/LVCommerceAI')
- LVCommonTracker (path 'Modules/LVInfra')
- LVCompliance (path 'Modules/LVCompliance')
- LVConsentSDKImpl (path 'Modules/LVConsentSDKImpl')
- LVCostModel (path 'Modules/LVCost')
- LVCreatorCenter (path 'Modules/LVCreatorCenter')
- LVCreatorCenterAPI (path 'Modules/LVCreatorCenter')
- LVDataComponentRenderService (path 'Modules/LVDataComponentRender')
- LVDataComponentRenderServiceAPI (path 'Modules/LVDataComponentRender')
- LVDeeplink (path 'Modules/LVDeeplink')
- LVDeeplinkModules (path 'Modules/LVDeeplink')
- LVDeeplinkNotify (path 'Modules/LVDeeplink')
- LVDigitalHuman (path 'Modules/LVDigitalHuman')
- LVDigitalHumanAPI (path 'Modules/LVDigitalHuman')
- LVDoraemon (path 'Modules/LVDoraemon')
- LVDoraemonAPI (path 'Modules/LVDoraemon')
- LVDraftBox (path 'Modules/LVDraftBox')
- LVDraftBoxAPI (path 'Modules/LVDraftBox')
- LVEasyAppConfig (path 'Modules/LVEasyAppConfig')
- LVEasyAppLaunch (path 'Modules/LVEasyAppLaunch')
- LVEasyTracker (path 'Modules/LVEasyTracker')
- LVEditor (path 'Modules/LVEditor')
- LVEditorAPI (path 'Modules/LVEditor')
- LVEditorBase (path 'Modules/LVEditorBase')
- LVEditorBizUI (path 'Modules/LVUIKit')
- LVEditorCommon (path 'Modules/LVEditorCommon')
- LVEditorCommonAPI (path 'Modules/LVEditorCommon')
- LVEditorCommonModel (path 'Modules/LVEditorCommon')
- LVEffectPlatform (path 'Modules/LVEffectPlatform')
- LVExport (path 'Modules/LVExport')
- LVExportAPI (path 'Modules/LVExport')
- LVExtension (path 'Modules/LVExtension')
- LVFeatureFlag (path 'Modules/LVFeatureFlag')
- LVFeed (path 'Modules/LVFeed')
- LVFeedAPI (path 'Modules/LVFeed')
- LVFeedCommon (path 'Modules/LVFeedCommon')
- LVFeedCommonAPI (path 'Modules/LVFeedCommon')
- LVFigureEditor (path 'Modules/LVFigureEditor')
- LVFileShare (0.0.45, from '******************:iOS_Library/privatethird_source_repo.git')
- LVFileUploadClient (path 'Modules/LVFileUploadClient')
- LVFileUploader (path 'Modules/LVFileUploader')
- LVFormula (path 'Modules/LVFormula')
- LVFormulaAPI (path 'Modules/LVFormula')
- LVFoundation (path 'Modules/LVFoundation')
- LVHandwrite (path 'Modules/LVHandwrite')
- LVHandwriteAPI (path 'Modules/LVHandwrite')
- LVHomepage (path 'Modules/LVHomepage')
- LVHomepageAPI (path 'Modules/LVHomepage')
- LVI18N (path 'Modules/LVI18N')
- LVI18NCapCut (path 'Modules/LVI18N')
- LVI18NVideoFusion (path 'Modules/LVI18N')
- LVImageCompress (path 'Modules/LVImageCompress')
- LVInbox (path 'Modules/LVInbox')
- LVInboxAPI (path 'Modules/LVInbox')
- LVInject (path 'Modules/LVInfra')
- LVInjectDebugAModule (path 'Modules/LVInfra')
- LVInjectDebugAModuleImpl (path 'Modules/LVInfra')
- LVInjectDebugBModule (path 'Modules/LVInfra')
- LVInjectDebugBModuleImpl (path 'Modules/LVInfra')
- LVInspiration (path 'Modules/LVInspiration')
- LVInspirationAPI (path 'Modules/LVInspiration')
- LVIntelligentCreation (path 'Modules/LVIntelligentCreation')
- LVIntelligentCreationAPI (path 'Modules/LVIntelligentCreation')
- LVLaboratory (path 'Modules/LVLaboratory')
- LVLaboratoryMain (path 'Modules/LVLaboratory')
- LVLive (path 'Modules/LVLive')
- LVLocalization (path 'Modules/LVLocalization')
- LVLogin (path 'Modules/LVLogin')
- LVLoginAPI (path 'Modules/LVLogin')
- LVLUIService (path 'Modules/LVLUI')
- LVLUIServiceAPI (path 'Modules/LVLUI')
- LVLynxKit (path 'Modules/LVLynxKit')
- LVMain (path 'Modules/LVMain')
- LVMaskEditor (path 'Modules/LVMaskEditor')
- LVMaterialGenerate (path 'Modules/LVMaterialGenerate')
- LVMaterialGenerateAPI (path 'Modules/LVMaterialGenerate')
- LVMaterialGenerateModel (path 'Modules/LVMaterialGenerateModel')
- LVMaterialModel (path 'Modules/LVMaterialModel')
- LVMaterialPanel (path 'Modules/LVMaterialPanel')
- LVMaterialPanelAPI (path 'Modules/LVMaterialPanelAPI')
- LVMaterialPanelCommon (path 'Modules/LVMaterialPanelCommon')
- LVMaterialResourceServiceModel (path 'Modules/LVMaterialResource')
- LVMigration (path 'Modules/LVMigration')
- LVMonitor (path 'Modules/LVInfra')
- LVMusic (path 'Modules/LVMusic')
- LVMusicAPI (path 'Modules/LVMusic')
- LVNextEditorCore (path 'Modules/LVNextEditorCore')
- LVNextEditorUI (path 'Modules/LVNextEditorUI')
- LVOpenPluginCore (15.4.0.1, from '******************:iOS_Library/lv-ios_source_repo.git')
- LVOpenPluginUI (15.4.0.1, from '******************:iOS_Library/lv-ios_source_repo.git')
- LVPaymentCenter (path 'Modules/LVPaymentCenter')
- LVPaymentCenterAPI (path 'Modules/LVPaymentCenter')
- LVPaymentService (path 'Modules/LVPaymentCenter')
- LVPaymentServiceAPI (path 'Modules/LVPaymentCenter')
- LVPerformanceMonitor (path 'Modules/LVPerformanceMonitor')
- LVPersonalizedMaterial (path 'Modules/LVPersonalizedMaterial')
- LVPersonalizedMaterialAPI (path 'Modules/LVPersonalizedMaterial')
- LVProfile (path 'Modules/LVProfile')
- LVProfileAPI (path 'Modules/LVProfile')
- LVPublish (path 'Modules/LVPublish')
- LVPublishAPI (path 'Modules/LVPublish')
- LVResourcePool (********.**********.18, from '******************:iOS_Library/lv-ios_source_repo.git')
- LVSAMIAudioService (path 'Modules/LVSAMIAudioService')
- LVSAMIAudioServiceAPI (path 'Modules/LVSAMIAudioService')
- LVShare (path 'Modules/LVShare')
- LVShareAPI (path 'Modules/LVShare')
- LVSmartPackage (path 'Modules/LVSmartPackage')
- LVSmartPackageAPI (path 'Modules/LVSmartPackage')
- LVSmartScriptTemplate (path 'Modules/LVSmartScriptTemplate')
- LVSmartScriptTemplateAPI (path 'Modules/LVSmartScriptTemplate')
- LVStickerEditor (path 'Modules/LVStickerEditor')
- LVSubscription (path 'Modules/LVPaymentCenter')
- LVSubscriptionAPI (path 'Modules/LVPaymentCenter')
- LVSubscriptionBase (path 'Modules/LVPaymentCenter')
- LVSwiftAsyncSocket (path 'Modules/LVSwiftAsyncSocket')
- LVTargetDiff (path 'Modules/LVTargetDiff')
- LVTemplateAssembler (path 'Modules/LVTemplateAssembler')
- LVTemplateAssemblerAPI (path 'Modules/LVTemplateAssembler')
- LVTemplateFoundation (path 'Modules/LVTemplateFoundation')
- LVTemplateFoundationAPI (path 'Modules/LVTemplateFoundation')
- LVTextAIGC (path 'Modules/LVTextAIGC')
- LVTextAIGCAPI (path 'Modules/LVTextAIGC')
- LVTextRecognize (path 'Modules/LVTextRecognize')
- LVTextRecognizeAPI (path 'Modules/LVTextRecognize')
- LVTheme (path 'Modules/LVTheme')
- LVTopMigration (path 'Modules/LVTopMigration')
- LVTracker (path 'Modules/LMTracker')
- LVTrackerCapCut (path 'Modules/LMTracker')
- LVTrackerVideoFusion (path 'Modules/LMTracker')
- LVUGKit (path 'Modules/LVUGKit')
- LVUGKitAPI (path 'Modules/LVUGKit')
- LVUIKit (path 'Modules/LVUIKit')
- LVUniteAd (********, from '******************:iOS_Library/privatethird_source_repo.git')
- LVUser (path 'Modules/LVUser')
- LVUserAPI (path 'Modules/LVUser')
- LVUserGuidance (path 'Modules/LVUserGuidance')
- LVVideoEditor (17.0.0.11, from '******************:iOS_Library/lv-ios_source_repo.git')
- LVVideoEngine (path 'Modules/LVFoundation')
- LVWebView (path 'Modules/LVWebView')
- Lynx (3.2.3-rc.5, from '******************:iOS_Library/privatethird_source_repo.git')
- LynxDevtool (3.2.3-rc.5, from '******************:iOS_Library/privatethird_source_repo.git')
- LynxService (3.2.3-rc.5, from '******************:iOS_Library/privatethird_source_repo.git')
- lz4 (1.0.1, from '******************:iOS_Library/privatethird_source_repo.git')
- MagicTransition (1.0.3-alpha.4.1-bugfix, from '******************:iOS_Library/faceu_source_repo.git')
- Mantle (2.1.2, from '******************:iOS_Library/publicthird_binary_repo.git')
- Masonry (1.1.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- MBProgressHUD (0.9.2, from '******************:iOS_Library/publicthird_binary_repo.git')
- MDLMediaDataLoader (1.1.202.43, from '******************:iOS_Library/privatethird_source_repo.git')
- MemoryGraphCapture (1.4.9-rc.7, from '******************:iOS_Library/privatethird_binary_repo.git')
- MetaSecML (4.7.0, from '******************:iOS_Library/privatethird_source_repo.git')
- MintegralAdSDK (7.7.2, from '******************:iOS_Library/publicthird_source_repo.git')
- MJRefresh (3.6.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- MMKV (1.0.24, from '******************:iOS_Library/publicthird_source_repo.git')
- mobilecv2 (1.8.33.2-alpha.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- MSSDK (5.0.0-alpha.115, from '******************:iOS_Library/privatethird_source_repo.git')
- nanopb (2.30908.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- nanosvg (0.1.13.6-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- NodeProber (1.0.4.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- Objective-LevelDB (1.1.0, from '******************:iOS_Library/faceu_source_repo.git')
- ObjectMapper (4.0.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- OpenCombine (0.12.5-xt, from '******************:iOS_Library/privatethird_source_repo.git')
- OpenCombineDispatch (0.12.4-xt, from '******************:iOS_Library/privatethird_source_repo.git')
- OpenCombineFoundation (0.12.4-xt, from '******************:iOS_Library/privatethird_source_repo.git')
- PAGMAdmobAdapter (*********-alpha.39, from '******************:ad/ad_union_source_repo.git')
- PAGMAppLovinAdapter (********-alpha.30, from '******************:ad/ad_union_source_repo.git')
- PAGMGamAdapter (*********-alpha.37, from '******************:ad/ad_union_source_repo.git')
- PAGMMaxAdapter (********-alpha.40, from '******************:ad/ad_union_source_repo.git')
- PAGMMintegralAdapter (7.7.2.0-alpha.34, from '******************:ad/ad_union_source_repo.git')
- PAGMVungleAdapter (7.4.3.0-alpha.34, from '******************:ad/ad_union_source_repo.git')
- PangleAdSDK-iOS (*******-alpha.4, from '******************:iOS_Library/privatethird_source_repo.git')
- PangleAppLovinMediationByteDanceAdapter (********, from '******************:ad/ad_union_source_repo.git')
- PangleGoogleMobileAdsMediation (********, from '******************:ad/ad_union_source_repo.git')
- PanglemForXDAPISDK (0.0.6, from '******************:iOS_Library/privatethird_source_repo.git')
- pcdn (1.1.132.6404, from '******************:iOS_Library/privatethird_source_repo.git')
- PhotoBrowser (********, from '******************:iOS_Library/faceu_source_repo.git')
- PIPOIAP (2.5.1, from '******************:iOS_Library/privatethird_source_repo.git')
- PIPOIAPSK2 (2.5.0, from '******************:iOS_Library/privatethird_source_repo.git')
- Pitaya (*********-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- PitayaDebugger (2.2.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- PlaylistCacheModule (1.16.3-jy, from '******************:iOS_Library/privatethird_binary_repo.git')
- PNSServiceKit (2.0.5, from '******************:iOS_Library/privatethird_binary_repo.git')
- PocketSVG (2.5.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- pop (1.0.10, from '******************:iOS_Library/publicthird_binary_repo.git')
- PromiseKit (0.0.2, from '******************:iOS_Library/privatethird_source_repo.git')
- PromisesObjC (2.3.0, from '******************:iOS_Library/publicthird_source_repo.git')
- Protobuf (3.8.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- protobuf_lite (1.0.5-alpha.0, from '******************:iOS_Library/toutiao_source_repo.git')
- Pumbaa (1.0.68.1-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- Puzzle (git '******************:iOS_Library/AnnieX-iOS.git', commit '47bed9e0ff3cf61c46902b5572cc760bd3da3705')
- Quaterback (4.2.3-rc.2, from '******************:iOS_Library/privatethird_source_repo.git')
- QuietTown (1.0.8, from '******************:iOS_Library/lv-ios_source_repo.git')
- ReactiveObjC (3.1.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- redbookopensdk (1.2.19, from '******************:iOS_Library/privatethird_source_repo.git')
- ReSwift (4.0.1, from '******************:iOS_Library/publicthird_source_repo.git')
- ReSwift-Diff (1.0.4, from '******************:iOS_Library/faceu_source_repo.git')
- RetouchMiddleware (16.8.0.1, from '******************:iOS_Library/retouch_source_repo.git')
- RSSwizzle (1.0.1-alpha.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- RTImageEditor (17.0.0.1, from '******************:iOS_Library/retouch_source_repo.git')
- RTPhotoBrowser (1.0.29, from '******************:iOS_Library/faceu_source_repo.git')
- RTSDKBasic (17.0.0.1, from '******************:iOS_Library/retouch_source_repo.git')
- RTSDKModel (17.0.0.1, from '******************:iOS_Library/retouch_source_repo.git')
- RTSDKResources (17.0.0.1, from '******************:iOS_Library/retouch_source_repo.git')
- RTSDKService (17.0.0.1, from '******************:iOS_Library/retouch_source_repo.git')
- RxCoreComponents (1.3.5, from '******************:iOS_Library/privatethird_binary_repo.git')
- RxDiffableDataSources (3.0.0-rc.11, from '******************:iOS_Library/privatethird_source_repo.git')
- RxDiffableDataSourcesSwift (2.0.0-rc.16, from '******************:iOS_Library/privatethird_source_repo.git')
- RxDiffing (0.1.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- RxFoundation (1.2.5-rc.0, from '******************:iOS_Library/privatethird_binary_repo.git')
- Saitama (2.1.11.4-alpha.3, from '******************:iOS_Library/toutiao_binary_repo.git')
- SAMKeychain (1.5.3, from '******************:iOS_Library/publicthird_binary_repo.git')
- SDWebImage (5.11.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- SDWebImageWebPCoder (0.6.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- sechwm (0.0.6-alpha.1, from '******************:iOS_Library/privatethird_source_repo.git')
- ServiceKitMacro (path 'Modules/LVMacros')
- silky_ios (2.0.4, from '******************:iOS_Library/privatethird_source_repo.git')
- skity (0.5.1-rc.16, from '******************:iOS_Library/privatethird_source_repo.git')
- smash (9.0.6-alpha.13, from '******************:iOS_Library/privatethird_binary_repo.git')
- SnapKit (5.0.10, from '******************:iOS_Library/privatethird_binary_repo.git')
- Socket.IO-Client-Swift (15.2.0, from '******************:iOS_Library/publicthird_source_repo.git')
- SocketRocket (0.6.0, from '******************:iOS_Library/toutiao_binary_repo.git')
- spdlog (*******, from '******************:iOS_Library/privatethird_binary_repo.git')
- SpeechEngineCapCut (*******, from '******************:iOS_Library/privatethird_binary_repo.git')
- SQLiteRepairKit (1.2.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- SSFQBDImageOpt (0.6.1-rc.3-jy, from '******************:iOS_Library/privatethird_source_repo.git')
- SSZipArchive (3.0.0, from '******************:iOS_Library/douyin_binary_repo.git')
- StarkPrecise (0.1.27-alpha.0, from '******************:iOS_Library/privatethird_source_repo.git')
- Starscream (3.1.1, from '******************:iOS_Library/publicthird_source_repo.git')
- Stinger (1.0.17, from '******************:iOS_Library/privatethird_source_repo.git')
- SVGKit (*******, from '******************:iOS_Library/privatethird_binary_repo.git')
- SwiftALog (1.0.7, from '******************:iOS_Library/privatethird_source_repo.git')
- SwiftyJSON (5.0.0, from '******************:iOS_Library/publicthird_source_repo.git')
- SwiftyRSA (*******, from '******************:iOS_Library/lv-ios_source_repo.git')
- Swinject (2.8.1, from '******************:iOS_Library/publicthird_source_repo.git')
- SwipeCellKit (2.7.1, from '******************:iOS_Library/publicthird_source_repo.git')
- Telegraph (0.30.0, from '******************:iOS_Library/publicthird_source_repo.git')
- TencentQQSDK (1.1.2-rc.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- tfccsdk (2.1.0, from '******************:iOS_Library/privatethird_source_repo.git')
- TikTokFBSDK (14.1.0, from '******************:ugc/AWESpecs.git')
- TikTokOpenAuthSDK (2.5.2, from '******************:iOS_Library/privatethird_source_repo.git')
- TikTokOpenSDKCore (2.5.2, from '******************:iOS_Library/privatethird_source_repo.git')
- TikTokOpenShareSDK (2.5.2, from '******************:iOS_Library/privatethird_source_repo.git')
- Timon (4.46.30, from '******************:iOS_Library/privatethird_binary_repo.git')
- TimonInhouse (4.8.28, from '******************:iOS_Library/privatethird_binary_repo.git')
- Toast (4.0.0, from '******************:iOS_Library/publicthird_binary_repo.git')
- TSPrivacyKit (********-alpha.1-dy, from '******************:iOS_Library/privatethird_binary_repo.git')
- TSPrivacyKitInhouse (2.2.5-dy.2-bugfix.1-bugfix.1-bugfix, from '******************:ugc/AWESpecs.git')
- tt265 (1.2.0, from '******************:iOS_Library/privatethird_source_repo.git')
- TTAccountSDK (5.20.8-alpha.86.4-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTAdSplashSDK (28.0.15.1-bugfix, from '******************:iOS_Library/privatethird_source_repo.git')
- TTAppStoreStarManager (1.2.5-rc.1-capcut, from '******************:iOS_Library/toutiao_binary_repo.git')
- TTAppUpdateHelper (3.1.0.5, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTBaseLib (2.3.2.1-bugfix, from '******************:iOS_Library/toutiao_binary_repo.git')
- TTBridgeUnify (5.2.28-alpha.1, from '******************:iOS_Library/privatethird_source_repo.git')
- TTFFmpeg (1.25.202.105-jy-net4, from '******************:iOS_Library/privatethird_source_repo.git')
- TTHelium (10.3.2.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTKitchen (4.3.17-alpha.5.1-bugfix, from '******************:iOS_Library/privatethird_source_repo.git')
- TTMacroManager (1.1.0, from '******************:iOS_Library/privatethird_source_repo.git')
- TTMd5 (0.0.0.7, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTMLeaksFinder (2.1.9-alpha.19-swift, from '******************:iOS_Library/toutiao_source_repo.git')
- TTNetworkDownloader (1.3.18, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTNetworkManager (4.2.210.15-douyin, from '******************:iOS_Library/toutiao_binary_repo.git')
- TTNetworkPredict (0.5.2, from '******************:iOS_Library/privatethird_source_repo.git')
- TTNetworkUploader (1.1.20.1-bugfix, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTPlayerSDK (2.10.202.243, from '******************:iOS_Library/privatethird_source_repo.git')
- TTReachability (1.10.13-tiktok, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTSVGView (0.1.15-rc.0-DCar, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTTAttributedLabel (2.0.0, from '******************:iOS_Library/publicthird_source_repo.git')
- TTTopSignature (0.0.22, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTVideoEngine (1.10.202.140-jy, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTVideoLive (1.10.188.2-jianyin, from '******************:iOS_Library/privatethird_binary_repo.git')
- TTVideoSetting (0.0.5, from '******************:iOS_Library/publicthird_binary_repo.git')
- TYRZApiSDK (9.6.4, from '******************:iOS_Library/privatethird_binary_repo.git')
- UnityAds (4.8.0, from '******************:iOS_Library/publicthird_source_repo.git')
- VCBaseKit (1.22.0, from '******************:iOS_Library/privatethird_source_repo.git')
- VCNVCloudNetwork (5.2.12-static-net4, from '******************:iOS_Library/privatethird_source_repo.git')
- VCPreloadStrategy (1.202.3, from '******************:iOS_Library/privatethird_binary_repo.git')
- VCVodSettings (1.0.5, from '******************:iOS_Library/privatethird_binary_repo.git')
- videoprocessor (0.65.522-xg, from '******************:iOS_Library/privatethird_binary_repo.git')
- vmsdk (2.12.2, from '******************:iOS_Library/privatethird_source_repo.git')
- VungleAds (7.4.3, from '******************:iOS_Library/publicthird_binary_repo.git')
- WCDBOptimizedSQLCipher (1.2.1, from '******************:iOS_Library/publicthird_binary_repo.git')
- WDBFrontEnd (path 'Modules/WDBFrontEnd')
- WebDebuggingTool (path 'Modules/WebDebuggingTool')
- WebDevtool (1.0.5, from '******************:iOS_Library/privatethird_source_repo.git')
- WechatSDK (0.4.2, from '******************:iOS_Library/privatethird_source_repo.git')
- XConsole (********, from '******************:iOS_Library/lv-ios_source_repo.git')
- XDebugger (5.2.0-rc.2, from '******************:iOS_Library/privatethird_binary_repo.git')
- XElement (3.2.3-rc.5, from '******************:iOS_Library/privatethird_source_repo.git')
- XIGPhoto (0.0.2, from '******************:iOS_Library/xigua_source_repo.git')
- XIGVideoPublish (0.0.37, from '******************:iOS_Library/xigua_source_repo.git')
- XLPagerTabStrip (9.0.0, from '******************:iOS_Library/lv-ios_source_repo.git')
- XShellBiz (path 'Modules/XShellBiz')
- XShellMain (path 'Modules/XShellMain')
- yaml-cpp (*******, from '******************:iOS_Library/privatethird_binary_repo.git')
- Yoga (********-bugfix, from '******************:iOS_Library/privatethird_source_repo.git')
- YYCache (********, from '******************:iOS_Library/toutiao_binary_repo.git')
- YYModel (1.0.4, from '******************:iOS_Library/publicthird_binary_repo.git')
- YYText (1.0.18, from '******************:iOS_Library/douyin_binary_repo.git')
- ZstdDecompressKit (1.0.3, from '******************:iOS_Library/privatethird_binary_repo.git')
