package com.vega.builder.pipeline.task.self

import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.WorkspaceParams
import org.koin.core.component.inject

@TaskDefinition("publish_pipeline_cli", stage = Stage.Build, displayName = "Publish pipeline-cli")
class PublishSelfTask : PipelineTask() {
    val buildParams by inject<BuildParams>()
    val pipelineContext by inject<PipelineContextImpl>()
    val workflow by inject<WorkspaceParams>()
    override suspend fun run() {

    }
}