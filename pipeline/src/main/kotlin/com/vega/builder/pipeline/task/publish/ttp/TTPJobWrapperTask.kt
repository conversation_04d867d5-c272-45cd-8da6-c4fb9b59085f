package com.vega.builder.pipeline.task.publish.ttp

import com.vega.builder.common.logger.logger
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.task.publish.service.TTPArtifactsService
import com.vega.builder.pipeline.task.publish.service.TTPService

@TaskDefinition("TTPWrapperTask", stage = Stage.Build, "TTP Wrapper")
class TTPJobWrapperTask : PipelineTask() {
    override suspend fun run() {
        val repoId = getenvSafe("repo_id", "")
        val version = getenvSafe("version", "")
        TTPService.checkJobStatus(repoId, version)
        val jobInfo = TTPService.searchTTPJobInfo(repoId, version = version)
        val jobId = jobInfo?.jobInfo?.jobId ?: return
        try {
            TTPArtifactsService.processSymbolArtifacts(jobId)
        }catch (e:Exception) {
            logger().warn("Exception occurred during processing symbol artifact results", e)
        }
    }
}