package com.vega.builder.common.network.api

import com.vega.builder.common.network.BaseUrl
import com.vega.builder.pipeline.BuildConfig
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

@BaseUrl("https://${BuildConfig.hide_pa_build_url}/api/ktor/open/")
//@BaseUrl("http://127.0.0.1:3000/api/ktor/open/")
interface IBuildServiceApi {
    @POST("build/sub_repo_publish/trigger")
    suspend fun subRepoPublishTrigger(
        @Body body: SubRepoPublishTriggerReq,
    ): BaseResponse<Long>

    @POST("build/sub_repo_publish/state")
    suspend fun subRepoPublishState(@Query("jobId") jobId: String): BaseResponse<String>

    @POST("build/build_record/result")
    suspend fun jobBuildResult(@Body body: BuildJobRecordReq): BaseResponse<Any>

    @POST("build/sub_repo_publish/ttp/state/v3")
    suspend fun ttpJobBuildResult(
        @Query("repo_id") repoId: String? = null,
        @Query("version") version: String? = null,
    ): BaseResponse<String>

    @POST("build/sub_repo_publish/component/state")
    suspend fun componentJobState(
        @Query("repo_id") repoId: String,
        @Query("version") version: String
    ): BaseResponse<String>


    @POST("build/sub_repo_publish/ttp/triggerV3")
    suspend fun ttpJobBuildTrigger(
        @Body ttpTrigger: TTPJobTriggerReq,
    ): BaseResponse<String>

    @GET("build/sub_repo_publish/ttp/state/info")
    suspend fun ttpJobBuildInfo(
        @Query("repo_id") repoId: String,
        @Query("version") version: String
    ): BaseResponse<TTPJobHistory>


    data class BaseResponse<T>(val code: Int, val msg: String, val data: T? = null) {
        fun isSuccess() = code == 0
    }
}

data class SubRepoPublishTriggerReq(
    val operator: String, val branch: String, val flavor: String, val replaceVersion: String
)

data class TTPRepoPublishHost(
    val branch: String,
    val commitId: String? = null,
    val taskName: String? = null
)

data class BuildJobRecordReq(
    val jobId: String,
    val errorTaskName: String,
    val params: Map<String, String>
)


data class TTPJobTriggerReq(
    val baseInfo: TTPJobBaseInfo,
    val repoList: Map<Int, TTPJobTriggerInfo>
)

data class TTPJobTriggerInfo(
    val repoId: Int,
    val nextRepoId: Int? = null,
    val branch: String,
    val taskParams: String,
    val createNewBranch: Boolean = true,
)

data class TTPJobBaseInfo(
    val operator: String,
    val version: String,
    val projectId: Int,
    val mrIid: Int,
    val from: String
)

data class TTPJobHistory(
    val version: String,
    val repoId: Int,
    val startTime: Long = Long.MAX_VALUE,
    val operator: String = "<EMAIL>",
    val mrId: String? = null,
    val nextID: String = "down",//
    val state: TTPJobState = TTPJobState.Waiting,
    val repoInfo: TTPJobRepoInfo,
    val jobInfo: TTPJobInfo = TTPJobInfo()
)

data class TTPJobInfo(
    val eventId: String = "",
    val jobId: String = "",
)


data class TTPJobRepoInfo(
    val branch: String,
    val originBranch: String,
    val commitId: String = "",
    val taskParams: String
)

enum class TTPJobState {
    Waiting, Success, Failed, Padding, Start, Alarmed
}