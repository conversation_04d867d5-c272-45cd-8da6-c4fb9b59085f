package com.vega.builder.common.utils

import com.vega.builder.pipeline.BuildConfig
import kotlin.reflect.full.memberProperties

fun printHello() {
    val keyMap =
        mapOf(
            "VERSION" to "Build Version",
            "BUILD_DATE" to "Build Date",
            "pipelineId" to "Build Pipeline",
            "commitId" to "CommitId",
            "author" to "Author",
            "commitMsg" to "Commit Message",
        )
    val maxKeyLength = keyMap.values.maxOf { it.length }
    println(
        """
         _                  _______ _________ _______  _______  _       _________ _        _______ 
        ( \    |\     /|   (  ____ )\__   __/(  ____ )(  ____ \( \      \__   __/( (    /|(  ____ \
        | (    | )   ( |   | (    )|   ) (   | (    )|| (    \/| (         ) (   |  \  ( || (    \/
        | |    | |   | |   | (____)|   | |   | (____)|| (__    | |         | |   |   \ | || (__    
        | |    ( (   ) )   |  _____)   | |   |  _____)|  __)   | |         | |   | (\ \) ||  __)   
        | |     \ \_/ /    | (         | |   | (      | (      | |         | |   | | \   || (      
        | (____/\\   /     | )      ___) (___| )      | (____/\| (____/\___) (___| )  \  || (____/\
        (_______/ \_/      |/       \_______/|/       (_______/(_______/\_______/|/    )_)(_______/                                                                  
        """.trimIndent(),
    )
    println()
    for (memberProperty in BuildConfig::class.memberProperties) {
        if (memberProperty.isConst) {
            if (memberProperty.name.startsWith("hide_")) {
                println("${memberProperty.name.padEnd(maxKeyLength,' ')} : ********")
                continue
            }
            val key = keyMap[memberProperty.name] ?: memberProperty.name
            val value =
                memberProperty.getter
                    .call()
                    .toString()
                    .replace("\n", "\n${"".padEnd(maxKeyLength, ' ')}   ")
            println("${key.padEnd(maxKeyLength, ' ')} : $value")
        }
    }
}
