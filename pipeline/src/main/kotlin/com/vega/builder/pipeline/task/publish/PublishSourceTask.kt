package com.vega.builder.pipeline.task.publish

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.vega.builder.common.git.extractRepositoryName
import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.IBuildServiceApi
import com.vega.builder.common.network.api.INetworkApi
import com.vega.builder.common.network.api.SubRepoPublishTriggerReq
import com.vega.builder.common.network.api.TTPJobBaseInfo
import com.vega.builder.common.network.api.TTPJobTriggerInfo
import com.vega.builder.common.network.api.TTPJobTriggerReq
import com.vega.builder.common.network.request
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.tos.slicePutObject
import com.vega.builder.common.utils.PollingResult
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.polling
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.task.publish.service.CompressionRepoService
import com.vega.builder.pipeline.task.publish.service.TTPService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import org.koin.core.component.inject
import java.io.File

/**
 * Use for NoTTP Evn publish source code
 */
@TaskDefinition("PublishSourceTask", stage = Stage.Build, "Source Publish")
class PublishSourceTask : PipelineTask() {
    val buildParams by inject<BuildParams>()
    val pipelineContext by inject<PipelineContextImpl>()

    companion object {
        const val SOURCE_FILE_EXTENSION = ".tar.gz"
    }

    val repoRootFile: File by lazy {
        File(buildParams["WORK_DIR"]!!).parentFile
    }


    override suspend fun run() {
        val (artifactsInfo, bytebusComponentInfo) = unpackPublishInfo()
        val shouldUploadComponentList = bytebusComponentInfo.componentList.filter {
            it.upgrade && it.groupId in artifactsInfo.artifacts.map { it.groupId } && it.artifactId in artifactsInfo.artifacts.map { it.artifactId }
        }
        val repos = shouldUploadComponentList.distinctBy { it.git }
        for (component in repos) {
            val repoName =
                extractRepositoryName(component.git) ?: throw PipelineThrowable(ErrorType.SUB_REPO_PUBLISH_FAILED)
            publishRepo(repoName, component, bytebusComponentInfo.type == "release")
            if (bytebusComponentInfo.type == "release") {
                publishDist(repoName, bytebusComponentInfo, component)
            }
        }
    }

    private suspend fun unpackPublishInfo(): Pair<ArtifactsInfo, BytebusComponentInfo> {
        if (buildParams["PUBLISH_PARAMS"] == null) {
            throw Exception("PUBLISH_PARAMS is null")
        }
        val artifactsInfoArg = buildParams["PUBLISH_PARAMS"].toString()

        val artifactsInfo = Gson().fromJson(artifactsInfoArg, ArtifactsInfo::class.java)
        val byteBusInfo = request(INetworkApi::download, artifactsInfo.bytebusInfoUrl)
        return artifactsInfo to Gson().fromJson(byteBusInfo.string(), BytebusComponentInfo::class.java)

    }

    private suspend fun publishRepo(repoName: String, repo: Component, withLyraCommit: Boolean = false) {

        val result = CompressionRepoService(repoRootFile, repoName, withLyraCommit).compressionRepo()
        TosConfig.LvBuildArtifact.createTosClient()
            .slicePutObject(
                PublishUtils.buildTosId(
                    "${repo.groupId}/${repo.artifactId}".replace(".", "_"),
                    repo.version
                ), result
            )
    }


    private suspend fun publishDist(repoName: String, bytebusComponentInfo: BytebusComponentInfo, repo: Component) {
        //先发布源码，如果是release分支就再发布二进制
        if (bytebusComponentInfo.mainProjectId == 40279) {
            if (checkReleasePublish(bytebusComponentInfo)) {
                CoroutineScope(Dispatchers.IO).launch {
                    publishDistByte(bytebusComponentInfo, "prod")
                }.join()
            }
        } else {
            val jobList = mutableListOf<Job>()
            if (checkReleasePublish(bytebusComponentInfo)) {
                val rowPublishJob = CoroutineScope(Dispatchers.IO).launch {
                    publishDistByte(bytebusComponentInfo, "oversea")
                }
                jobList.add(rowPublishJob)
            }

            if (bytebusComponentInfo.mainGitTargetBranch != "overseas/release/14.7.0") {
                val ttpPublishJob = CoroutineScope(Dispatchers.IO).launch {
                    //先发布TTP环境下的源码，TTP环境下的源码依赖二进制发布
                    publishTTPSource(repoName, bytebusComponentInfo, repo)
//                    if (checkReleasePublish(bytebusComponentInfo)) {
//                        publishTTPArtifact(bytebusComponentInfo, repo)
//                    }
                }
                jobList.add(ttpPublishJob)
            }
            jobList.joinAll()
        }
    }

    private fun checkReleasePublish(bytebusComponentInfo: BytebusComponentInfo): Boolean {
        return (bytebusComponentInfo.type == "release"
                && (bytebusComponentInfo.mainGitTargetBranch.startsWith("release/")
                || bytebusComponentInfo.mainGitTargetBranch.startsWith("overseas/release/")))

    }

    /**
     * 发布TTP环境下的源码
     */
    private suspend fun publishTTPSource(
        repoName: String,
        bytebusComponentInfo: BytebusComponentInfo,
        repo: Component
    ) {
        val repoFile = File(repoRootFile, repoName)
        val finalBranch = if (bytebusComponentInfo.type == "release") {

            val publishBranch = "p.robot/${repo.sourceBranch.replace("/", "_")}_${
                repo.targetBranch.replace(
                    "/",
                    "_"
                )
            }_${System.currentTimeMillis()}"
            Command("git").directory(repoFile).args("clean", "-xdf").default().spawn().wait()
            Command("git").directory(repoFile)
                .args("checkout", "-b", publishBranch).default().spawn().wait()
            Command("git").directory(repoFile)
                .args("push", "--set-upstream", "origin", publishBranch).default().spawn().wait()
            logger().info("publish merged branch $publishBranch")
            publishBranch
        } else {
            logger().info("publish origin branch ${repo.branch},releaseBeforeMerge:${bytebusComponentInfo.releaseBeforeMerge},type:${bytebusComponentInfo.type}")
            repo.branch
        }
        TTPService.triggerJob(
            TTPJobTriggerReq(
                baseInfo = TTPJobBaseInfo(
                    operator = bytebusComponentInfo.userName,
                    version = repo.version,
                    projectId = bytebusComponentInfo.mainProjectId,
                    mrIid = bytebusComponentInfo.mainMrIid,
                    from = getenvSafe("WORKFLOW_JOB_ID", "none"),
                ),
                repoList = mapOf(
                    0 to TTPJobTriggerInfo(
                        repoId = 51594, nextRepoId = 51566,
                        branch = finalBranch,
                        taskParams = "publish_ttp_source_videoeditor"
                    ),
                    1 to TTPJobTriggerInfo(
                        repoId = 51566, nextRepoId = null,
                        branch = bytebusComponentInfo.mainGitBranch,
                        taskParams = Gson().toJson(
                            mapOf<String, String>(
                                "REPLACE_VERSION" to Gson().toJson(bytebusComponentInfo.componentList.associate {
                                    "${it.groupId}:${it.artifactId}" to it.version
                                }),
                                "ext_template" to "publish_ttp_binary_videoeditor"
                            )
                        )
                    )
                )
            ),
            mapOf(
                51594 to (bytebusComponentInfo.mainGitTargetBranch == "rc/develop")
            )
        )
    }


    private suspend fun publishDistByte(
        bytebusComponentInfo: BytebusComponentInfo, flavor: String,
    ) {
        val result = request(
            IBuildServiceApi::subRepoPublishTrigger, SubRepoPublishTriggerReq(
                bytebusComponentInfo.userName,
                bytebusComponentInfo.mainGitBranch,
                flavor,
                Gson().toJson(bytebusComponentInfo.componentList.associate {
                    "${it.groupId}:${it.artifactId}" to it.version
                })
            )
        )
        if (result.isSuccess()) {
            logger().info("trigger publish sub repo success，jobId: ${result.data}")
            val result = polling(initialDelay = 120, interval = 60) {
                val d = request(IBuildServiceApi::subRepoPublishState, result.data.toString())
                if (d.isSuccess()) {
                    when (d.data) {
                        "success" -> {
                            PollingResult.Success(Unit)
                        }

                        "failed" -> {
                            logger().error("sub repo publish failed[${result.data}]: ${d.data}")
                            PollingResult.Failure(PipelineThrowable(ErrorType.TtpJobFailed))
                        }

                        "cancel" -> {
                            logger().error("sub repo publish cancel[${result.data}]: ${d.data}")
                            PollingResult.Failure(PipelineThrowable(ErrorType.TtpJobFailed))
                        }

                        else -> {
                            logger().info("sub repo publishing[${result.data}]，state：${d.data}")
                            PollingResult.Waiting
                        }
                    }
                } else {
                    PollingResult.Failure(PipelineThrowable(ErrorType.TtpJobFailed))
                }
            }
            if (result is PollingResult.Failure) {
                throw result.reason
            }
            logger().info("Sub repo publish success!")
        } else {
            logger().error("sub repo publish failed:，${result.msg}")
            throw PipelineThrowable(ErrorType.SUB_REPO_PUBLISH_DIST_FAILED)
        }
    }


}


data class ArtifactsInfo(
    @SerializedName("bytebus_info_url")
    val bytebusInfoUrl: String,

    @SerializedName("artifacts")
    val artifacts: List<Artifact>
)

data class Artifact(
    @SerializedName("groupId")
    val groupId: String,
    @SerializedName("artifactId")
    val artifactId: String
)

data class BytebusComponentInfo(
    @SerializedName("componentList")
    val componentList: List<Component>,//留
    @SerializedName("mainGit")
    val mainGit: String,
    @SerializedName("mainGitBranch")
    val mainGitBranch: String, //留
    @SerializedName("mainGitSourceBranch")
    val mainGitSourceBranch: String,
    @SerializedName("mainGitTargetBranch")
    val mainGitTargetBranch: String,
    @SerializedName("mainProjectId")
    val mainProjectId: Int,
    @SerializedName("release_before_merge")
    val releaseBeforeMerge: Boolean,
    @SerializedName("type")
    val type: String,
    @SerializedName("userName")
    val userName: String, //留g
    @SerializedName("mainMrIid")
    val mainMrIid: Int //留
)

data class Component(
    @SerializedName("appId")
    val appId: Int,
    @SerializedName("artifactId")
    val artifactId: String,
    @SerializedName("branch")
    val branch: String,
    @SerializedName("changeLog")
    val changeLog: String,
    @SerializedName("git")
    val git: String,
    @SerializedName("groupId")
    val groupId: String,
    @SerializedName("projectId")
    val projectId: Int,
    @SerializedName("repoId")
    val repoId: Int,
    @SerializedName("sourceBranch")
    val sourceBranch: String,
    @SerializedName("targetBranch")
    val targetBranch: String,
    @SerializedName("upgrade")
    val upgrade: Boolean,
    @SerializedName("version")
    val version: String,
)

