package com.vega.builder.pipeline.task.artifacts

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.tos.slicePutObject
import com.vega.builder.common.utils.BitsUtils
import com.vega.builder.common.utils.compression.Compression
import com.vega.builder.common.utils.compression.SevenZ
import com.vega.builder.common.utils.compression.TarGz
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.md5
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.BuildConfig
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.context.WorkspaceParams
import org.koin.core.component.inject
import java.io.File
import java.util.regex.Pattern
import kotlin.io.path.createTempDirectory
import kotlin.io.path.createTempFile

@TaskDefinition(ArtifactsUpload.TAG, stage = Stage.After, displayName = "Artifacts Upload")
class ArtifactsUpload : PipelineTask() {
    companion object {
        const val TAG = "artifactsUpload"
    }

    val buildParams: BuildParams by inject()

    val workspaceParams: WorkspaceParams by inject()

    val pipelineContext: PipelineContextImpl by inject()

    override suspend fun run() {
        val uploadResult = mutableListOf<ArtifactsUploadResult>()
        val tosClient = TosConfig.LvBuildResult.createTosClient()
        val taskId = getenvSafe("TASK_ID")
        PipelineOutput.buildOutputs().forEach { (type, value) ->
            val file = File(value)
            if (file.exists()) {
                val remoteName = listOfNotNull("pipeline_result", taskId, file.name).joinToString("/")
                if (tosClient.slicePutObject(remoteName, file)) {
                    uploadResult.add(
                        ArtifactsUploadResult(
                            name = file.name,
                            url = "https://${BuildConfig.hide_voffline_rul}/download/tos/schedule/${TosConfig.LvBuildResult.bucket}/${remoteName}",
                            type = type,
                            md5 = file.md5(),
                            size = file.length()
                        )
                    )
                } else {
                    logger().error("Upload file[${file.name}] error!")
                }
            } else {
                logger().info("file does not exist: ${file.absolutePath},skip upload")
            }
        }
        BitsUtils.setEnv("artifacts_output", Gson().toJson(uploadResult))
        mrUploadBuildArtifacts()

    }

    /**
     * 上传构建过程中的中间层产物，用于排查问题
     */
    suspend fun mrUploadBuildArtifacts() {
        try {
            val tosClient = TosConfig.LvBuildResult.createTosClient()

            val result = zipBuildMiddleResult(buildParams, pipelineContext, workspaceParams)
            if (result?.exists() == true) {
                val taskId = getenvSafe("TASK_ID")
                tosClient.slicePutObject(listOfNotNull(taskId, result.name).joinToString("/"), result)
            }
        } catch (_: Exception) {
        }
    }
}

fun zipBuildMiddleResult(
    buildParams: BuildParams,
    pipelineContext: PipelineContextImpl,
    workspaceParams: WorkspaceParams
): File? {
    val mrUploadParams = buildParams.getOrDefault("upload_build_dir_regex", "")
    if (mrUploadParams.isBlank()) {
        return null
    }
    val params =
        Gson().fromJson<UploadBuildArtifactsParams>(mrUploadParams)
    if (params.regex.isBlank()) {
        return null
    }
    logger().info("enable custom upload build artifacts")
    val directory = File(pipelineContext.workspace, params.rootPath)

    val tempRoot = createTempDirectory()
    val artifactsFile =
        createTempFile(directory = tempRoot, suffix = TarGz.extension).toFile()
    val pattern = Pattern.compile(params.regex)
    val fileList =
        directory.walkTopDown().toList()
            .filter { file ->
                pattern.matcher(file.absolutePath).matches()
                        && file.isFile
            }
    Compression.compressFiles(
        SevenZ,
        fileList,
        File(workspaceParams.targetProjectDir),
        artifactsFile
    )
    return artifactsFile
}

data class UploadBuildArtifactsParams(
    val rootPath: String,
    val regex: String
)

data class ArtifactsUploadResult(
    val name: String,
    val url: String,
    val type: String,
    val md5: String,
    val size: Long,
)