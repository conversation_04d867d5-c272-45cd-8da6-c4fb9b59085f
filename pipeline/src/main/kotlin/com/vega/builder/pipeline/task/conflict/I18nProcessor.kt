package com.vega.builder.pipeline.task.conflict

import com.vega.builder.common.network.api.II18nApi
import com.vega.builder.common.network.request
import com.vega.builder.common.network.api.sign
import okhttp3.ResponseBody
import java.io.File
import java.net.URLEncoder
import java.util.zip.ZipInputStream
import kotlin.io.path.Path

class I18nProcessor : FileConflictProcessor {

    companion object {
        @JvmStatic
        fun extractI18nKey(line: String): String? {
            val regex = """<string\sname="([^"]+)">""".toRegex()
            val matchResult = regex.find(line)
            return matchResult?.groupValues?.get(1)
        }
    }

    private var fileDownloadContent = ""

    constructor(filePath: String) : super(filePath)
    constructor(file: File, fileReader: ConflictFileReader) : super(file, fileReader)

    private val overseaI18nDirMap = mapOf(
        "values-in-rID" to "id-ID",
        "values-iw" to "he-IL",
        "values-es-rES" to "es-LA",
        "values-es-rLA" to "es-LA",
        "values-zh-rTW" to "zh-Hant-TW",
        "values-zh-rHK" to "zh-Hant-TW",
        "values-zh-rMO" to "zh-Hant-TW",
        "values-zh-rCN" to "zh-Hans",
        "values" to "en",
    )

    private val prodI18nDirMap = mapOf(
//        "values-in-rID" to "id-ID",
//        "values-iw" to "he-IL",
//        "values-es-rES" to "es-LA",
//        "values-es-rLA" to "es-LA",
//        "values-zh-rTW" to "zh-Hant-TW",
//        "values-zh-rHK" to "zh-Hant-TW",
//        "values-zh-rMO" to "zh-Hant-TW",
        "values-zh-rCN" to "zh-Hans",
        "values" to "zh-Hans",
        "values-en" to "en",
    )

    override suspend fun processConflictBlock(conflictBlock: ConflictBlock): List<String> {
        if (fileDownloadContent.isNotEmpty()) {
            // Description needs to be processed as files and regenerated files
            return emptyList()
        }
        var keyException: Exception? = null
        try {
            return processConflictByKey(conflictBlock)
        } catch (e: Exception) {
            keyException = e
        }
        // If processing by key fails, try processing by file
        val fileContent = queryI18nFile(i18nLocalize(extractLocale()))
        if (fileContent.isNotEmpty()) {
            fileDownloadContent = fileContent
            return emptyList()
        }
        throw keyException!!
    }

    override suspend fun processEnd(): Boolean {
        if (fileDownloadContent.isNotEmpty()) {
            file.writeText(fileDownloadContent)
            fileDownloadContent = ""
        }
        return super.processEnd()
    }

    @Throws(Exception::class)
    private suspend fun processConflictByKey(conflictBlock: ConflictBlock): List<String> {
        val i18nKeys = mutableSetOf<String>()
        conflictBlock.conflictLinesHead.forEach {
            val key = extractI18nKey(it)
            if (key != null) {
                i18nKeys.add(key)
            } else {
                throw IllegalStateException("i18n key not found: $it")
            }
        }
        conflictBlock.conflictLinesInsert.forEach {
            val key = extractI18nKey(it)
            if (key != null) {
                i18nKeys.add(key)
            } else {
                // Skip if you cannot find it first
                throw IllegalStateException("i18n key not found: $it")
            }
        }
        return queryI18nIds(i18nKeys, i18nLocalize(extractLocale()))
    }

    private fun extractLocale(): String {
        val path = file.absolutePath
        return Path(path).parent.fileName.toString()
    }

    /**
     * Retrieving file names on i18n through conflicting files
     * eg: values-ja-rJP
     */
    private fun i18nLocalize(localDir: String): String {
        val i18nDirMap = getI18nDirMap(file.absolutePath)
        return if (i18nDirMap.containsKey(localDir)) {
            i18nDirMap[localDir]!!
        } else {
            val localInfo = localDir.split("-")
            when (localInfo.size) {
                3 -> {
                    val language = localInfo[1]
                    val country = localInfo[2].replace("r", "")
                    "$language-$country"
                }

                2 -> {
                    localInfo[1]
                }
                else -> {
                    throw IllegalStateException("i18n localize not support")
                }
            }
        }
    }

    private suspend fun queryI18nIds(keySet: Set<String>, locale: String): List<String> {
        if (keySet.isEmpty()) {
            return emptyList()
        }
        val i18nConfig = getI18nConfig(file.absolutePath)
        val queryMap = mutableMapOf(
            "projectId" to i18nConfig["projectId"]!!,
            "namespaceId" to i18nConfig["namespaceId"]!!,
            "keyTexts" to keySet.joinToString(","),
            "locale" to locale,
        )
        val queryStr = queryMap.entries.joinToString("&") {
            "${it.key}=${
                URLEncoder.encode(
                    it.value,
                    "UTF-8"
                )
            }"
        }
        val queryByteArray = queryStr.encodeToByteArray()
        val signature = sign(
            "auth-v2",
            "fabd163ad0cf41886e3a6990c7682da5",
            "d0b7993e83daa51b77f99c076351c389",
            queryByteArray
        )
        val response = request(II18nApi::queryI18nIds, queryMap, signature)
        return keySet.map { key ->
            val i18nIdData = response.data.find { it.keyText == key }
                ?: throw IllegalStateException("i18n key not found: $key")
            val i18nTarget = i18nIdData.targetTexts.find { it.lang == locale }
                ?: throw IllegalStateException("i18n lang not found: $key")
            val content =
                i18nTarget.content ?: throw IllegalStateException("i18n content not found: $key")
            return@map "    <string name=\"$key\">$content</string>"
        }
    }

    private suspend fun queryI18nFile(locale: String): String {
        val i18nConfig = getI18nConfig(file.absolutePath)
        val queryMap = mutableMapOf(
            "projectId" to i18nConfig["projectId"]!!,
            "namespaceId" to i18nConfig["namespaceId"]!!,
            "onlySource" to "0",
            "onlyTarget" to "1",
            "format" to "xml",
            "languages" to locale,
        )
        val queryStr = queryMap.entries.joinToString("&") {
            "${it.key}=${
                URLEncoder.encode(
                    it.value,
                    "UTF-8"
                )
            }"
        }
        val queryByteArray = queryStr.encodeToByteArray()
        val signature = sign(
            "auth-v2",
            "7bf5ff08fafcee04a93d1bc094c56dd3",
            "dc869f278fd27b433310f0feac6ab2de",
            queryByteArray
        )
        val downloadResponse: ResponseBody = request(II18nApi::downloadI18n, queryMap, signature)
        val zipInputStream = ZipInputStream(downloadResponse.byteStream())
        return zipInputStream.use { zip ->
            var entry = zip.nextEntry
            while (entry != null) {
                if (entry.name.endsWith(".xml")) {
                    val content = zip.readBytes().decodeToString()
                    println(entry.name)
                    return@use content
                }
                entry = zip.nextEntry
            }
            return ""
        }
    }

    private fun getI18nConfig(filePath: String): Map<String, String> {
        if (filePath.contains("VideoCut") || filePath.contains("prod")) {
            return mapOf<String, String>(
                "projectId" to "30586",
                "namespaceId" to "133308"
            )
        } else if (filePath.contains("CapCut")|| filePath.contains("oversea")) {
            return mapOf<String, String>(
                "projectId" to "798",
                "namespaceId" to "3523",
            )
        } else {
            throw IllegalStateException("i18n config not found: $filePath")
        }
    }

    private fun getI18nDirMap(filePath: String): Map<String, String> {
        if (filePath.contains("VideoCut") || filePath.contains("prod")) {
            return this.prodI18nDirMap
        } else if (filePath.contains("CapCut")|| filePath.contains("oversea")) {
            return this.overseaI18nDirMap
        }else {
            throw IllegalStateException("i18n dir map not found: $filePath")
        }
    }
}