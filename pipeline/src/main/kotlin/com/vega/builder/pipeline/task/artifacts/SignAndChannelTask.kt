package com.vega.builder.pipeline.task.artifacts

import com.vega.builder.common.sign.Apktooks
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.context.TargetInfo
import com.vega.builder.pipeline.context.buildTarget
import com.vega.builder.pipeline.context.channel
import com.vega.builder.pipeline.context.isDebug
import com.vega.builder.pipeline.context.withAab
import org.koin.core.component.inject
import java.io.File

@TaskDefinition(SignAndChannelTask.TAG, stage = Stage.Artifacts, displayName = "sign&channel")
class SignAndChannelTask() : PipelineTask() {
    companion object {
        const val TAG = "afterBuildTask"
    }

    val buildParams: BuildParams by inject()

    val pipelineContext:PipelineContextImpl by inject()

    val signId: String
        get() {
            return when(buildParams.buildTarget) {
                TargetInfo.Lv -> "${pipelineContext.appId}.rotated"
                TargetInfo.Dreamina -> "com.lemon.lv.rotated"
                else -> pipelineContext.appId
            }
        }

    override suspend fun run() {
        for (apkFile in PipelineOutput.buildApkPaths.map(::File)) {
            if (apkFile.exists()) {
                val signTemp = File(PipelineOutput.buildApkRootPath, "sign-tmp.apk")
                sign(apkFile.absolutePath, signTemp, isApk = true)
                signTemp.copyTo(apkFile, overwrite = true)
                writeChannel(apkFile.absolutePath)
                val apkPath = listOf(
                    PipelineOutput.buildApkRootPath,
                    "${pipelineContext.finalTargetName}-${apkFile.nameWithoutExtension}.apk"
                ).joinToString(File.separator)
                apkFile.copyTo(File(apkPath), overwrite = true)
                PipelineOutput.recordFinalApkPath(apkPath)
            } else if (!buildParams.withAab) {
                throw PipelineThrowable(ErrorType.ApkNotFound)
            }
        }

        if (buildParams.withAab) {
            val aabFile = File(PipelineOutput.buildBundlePath)
            if (aabFile.exists()) {
                val aabTemp = File(PipelineOutput.buildBundleRootPath, "sign-tmp.aab")
                sign(PipelineOutput.buildBundlePath, aabTemp, false)
                aabTemp.copyTo(File(PipelineOutput.buildFinalBundlePath), overwrite = true)
            } else {
                throw PipelineThrowable(ErrorType.AabNotFound)
            }
        }
    }

    private fun sign(
        src: String,
        dest: File,
        isApk: Boolean,
    ) {
        Apktooks.signSingleApk(
            src,
            dest.absolutePath,
            signId,
            buildParams.isDebug,
            Apktooks.SignType.V3,
            if (isApk) {
                null
            } else {
                "21"
            },
        )
    }

    private fun writeChannel(src: String) {
        buildParams.channel?.let { channel ->
            val channelTemp = File(PipelineOutput.buildApkRootPath, "channel-tmp.apk")
            Apktooks.writeChannel(src, channelTemp.absolutePath, channelName = channel)
            channelTemp.copyTo(File(src), overwrite = true)
        }
    }
}
