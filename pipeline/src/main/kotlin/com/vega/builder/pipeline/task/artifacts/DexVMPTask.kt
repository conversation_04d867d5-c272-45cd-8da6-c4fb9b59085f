package com.vega.builder.pipeline.task.artifacts

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.BaseUrl
import com.vega.builder.common.network.api.INetworkApi
import com.vega.builder.common.network.request
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.utils.retry
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.BuildConfig
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.context.withAab
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.ResponseBody
import org.koin.core.component.KoinComponent
import org.koin.core.component.get
import org.koin.core.component.inject
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Query
import retrofit2.http.Streaming
import retrofit2.http.Url
import java.io.File

@Deprecated("Place use DexVMPOfflineTask")
@TaskDefinition(DexVMPTask.TAG, stage = Stage.Artifacts, displayName = "Reinforcement")
class DexVMPTask : PipelineTask() {

    companion object : KoinComponent {
        const val TAG = "DexVMP"
        val DEX_VMP_CONFIG_APK = """
        {
        "version": "${get<BuildParams>()["DEV_VMP_VERSION"] ?: "stable"}",
        "encryptType": "DexVmp",
        "isaab":false,
        "isaar":false,
        "isuploadless":false,
        "encryptMethod":{
            "classList": [],
            "methodList":[
                {
                    "class": "${'$'}USE_ANNOTATIONS${'$'}",
                    "methodList": ["${'$'}USE_ANNOTATIONS${'$'}"]
                }
            ]
        }
    }
"""

        val DEX_VMP_CONFIG_AAB = """
    {
    "version": "${get<BuildParams>()["DEV_VMP_VERSION"] ?: "stable"}",
    "encryptType": "DexVmp",
    "isaab":true,
    "isaar":false,
    "isuploadless":false,
    "encryptMethod":{
        "classList": [],
        "methodList":[
            {
                "class": "${'$'}USE_ANNOTATIONS${'$'}",
                "methodList": ["${'$'}USE_ANNOTATIONS${'$'}"]
            }
        ]
    }
}
"""
    }

    val buildParams: BuildParams by inject()

    override suspend fun run() {
        if (buildParams.isEnable("OPEN_DEX_VMP", false)) {
            val apkJob = PipelineOutput.buildApkPaths.map(::File).map { file ->
                if (!file.exists()) {
                    if (!buildParams.withAab) {
                        throw PipelineThrowable(ErrorType.ApkNotFound)
                    } else {
                        null
                    }
                } else {
                    CoroutineScope(Dispatchers.IO).async {
                        val apkTemp = File(PipelineOutput.buildApkRootPath, "dex_vpm.apk")
                        startDevVMP(file, apkTemp, DEX_VMP_CONFIG_APK)
                        apkTemp.copyTo(file, overwrite = true)
                    }
                }
            }
            val aabJob =
                if (buildParams.withAab) {
                    val aabFile = File(PipelineOutput.buildBundlePath)
                    if (!aabFile.exists()) {
                        throw PipelineThrowable(ErrorType.AabNotFound)
                    }
                    CoroutineScope(Dispatchers.IO).async {
                        val aabTemp = File(PipelineOutput.buildBundleRootPath, "dex_vpm.aab")
                        startDevVMP(File(PipelineOutput.buildBundlePath), aabTemp, DEX_VMP_CONFIG_AAB)
                        aabTemp.copyTo(File(PipelineOutput.buildBundlePath), overwrite = true)
                    }
                } else {
                    null
                }
            listOfNotNull(aabJob, *apkJob.toTypedArray()).awaitAll()
        }
    }

    private suspend fun startDevVMP(
        src: File,
        dest: File,
        config: String,
    ) = retry(3, 1000) {
        logger().info("Starting reinforcement process")
        logger().info("config: $config")

        val uuidRequestBody =
            "2e6f8c7f-d303-4e64-b46c-27a0ee72912b".toRequestBody("text/plain; charset=utf-8".toMediaType())
        val apkFilePart = MultipartBody.Part.createFormData(
            "apkFile",
            src.name,
            src.asRequestBody("application/vnd.android.package-archive".toMediaTypeOrNull())
        )
        val configFilePart = MultipartBody.Part.createFormData(
            "configFile",
            "new_rule.txt",
            config.toRequestBody("text/plain".toMediaType())
        )

        val result = request(DexVmpService::createTask, uuidRequestBody, apkFilePart, configFilePart)
        logger().info("Reinforcement triggered, ${Gson().toJson(result)}")
        if (result.code != 0) {
            throw PipelineThrowable(ErrorType.DexVpmTriggerError)
        }

        val downloadPath = queryVmpStatus(result.msg)
        logger().info("Downloading reinforced artifact")

        retry(3, 1000) {
            val apkResult = request(INetworkApi::download, downloadPath)
            if (!dest.parentFile.exists()) {
                dest.parentFile.mkdirs()
            }
            dest.writeBytes(apkResult.bytes())
        }
        logger().info("Reinforced artifact download completed: ${dest.absolutePath}")
    }

    private suspend fun queryVmpStatus(taskId: String): String {
        var result = DexVMPRequest(code = 1, msg = "")
        do {
            delay(20000) // Check reinforcement status every 20s
            try {
                result = request(DexVmpService::queryStatus, "2e6f8c7f-d303-4e64-b46c-27a0ee72912b", taskId)
                logger().info("Refreshing reinforcement status: ${Gson().toJson(result)}")
            } catch (ignore: Exception) {
            }
        } while (result.code == 1)
        logger().info("Reinforcement completed")
        if (result.code == 0) {
            return result.msg
        } else {
            throw PipelineThrowable(ErrorType.DexVpmError)
        }
    }
}

data class DexVMPRequest(
    val code: Int,
    val msg: String,
)

@BaseUrl("https://${BuildConfig.hide_dex_vmp_url}/")
private interface DexVmpService {
    @Multipart
    @POST("interactive/createTask")
    suspend fun createTask(
        @Part("uuid") uuid: RequestBody,
        @Part apkFile: MultipartBody.Part,
        @Part configFile: MultipartBody.Part,
    ): DexVMPRequest

    @POST("interactive/taskStatus")
    suspend fun queryStatus(
        @Query("uuid") uuid: String,
        @Query("taskId") taskId: String,
    ): DexVMPRequest
}
