package com.vega.builder.pipeline.task.benchmark

import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.WorkspaceParams
import org.koin.core.component.inject
import java.io.File
import kotlin.getValue


@TaskDefinition("RunBenchmark", stage = Stage.Build, "RunBenchmark")
class RunBenchmark : PipelineTask() {
    val workspaceParams: WorkspaceParams by inject()
    val buildParams: BuildParams by inject()

    override suspend fun run() {
        val gradleProfiler = File(workspaceParams.workspace, "gradle-profiler").apply { mkdirs() }
        val bin = File(gradleProfiler, "tools").walk().find { it.isFile && it.name == "gradle-profiler" }
            ?: throw PipelineThrowable(ErrorType.GradleProfilerNotFoundError)
        val scenariosFile = File(gradleProfiler, "scenarios.txt")
        val benchmarkType =
            buildParams["BENCHMARK_TYPE"] ?: throw PipelineThrowable(ErrorType.GradleProfilerTargetNotFoundError)

        Command("chmod").directory(bin.parentFile).args("+x", bin.name).default().spawn().wait()
        Command("./gradle-profiler").directory(bin.parentFile)
            .arg("--benchmark")
            .args("--gradle-user-home", "~/.gradle")
            .args("--project-dir", workspaceParams.targetProjectDir)
            .args("--scenario-file", scenariosFile.absolutePath)
            .arg(benchmarkType)
            .default()
            .spawn()
            .wait()
    }
}