package com.vega.builder.pipeline.context

import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.pipeline.utils.GitUtils.extractRepoNameFromGitUrl
import kotlin.io.path.Path
import kotlin.io.path.pathString

/**
 * <AUTHOR>
 * @time 2025/1/22
 */
fun createWorkspaceParams(): WorkspaceParams {
    var workspace = getenvSafe("WORKSPACE", "")
    val mainUrl = getenvSafe("MAIN_GIT_URL", "")
    var targetCodePath = getenvSafe("TARGETCODEPATH", "")
    if (targetCodePath.isEmpty()) {
        targetCodePath = Path(workspace, extractRepoNameFromGitUrl(mainUrl)).pathString
    }

    return WorkspaceParams(
        workspace = workspace,
        targetProjectDir = targetCodePath,
    )
}

data class WorkspaceParams(
    val workspace: String,
    val targetProjectDir: String,
)

