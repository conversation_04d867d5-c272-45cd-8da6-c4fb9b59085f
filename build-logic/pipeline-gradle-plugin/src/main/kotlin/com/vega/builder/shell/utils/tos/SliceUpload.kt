package com.vega.builder.shell.utils.tos

import com.bytedance.storage.tos.TosClient
import com.bytedance.storage.tos.model.PartInfo
import com.bytedance.storage.tos.model.PartUploadInitRequest
import com.bytedance.storage.tos.model.PartUploadRequest
import java.io.File
import java.io.RandomAccessFile
import kotlin.time.measureTime
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentLinkedQueue
import kotlin.time.ExperimentalTime

class SliceUpload(private val tosClient: TosClient, private val key: String) {

    companion object {
        private const val RECOMMENDED_CHUNK_SIZE = 50 * 1024 * 1024L // 50 MB
        private const val MIN_CHUNK_SIZE = 5 * 1024 * 1024L // 5 MB
        private const val MAX_CONCURRENT_JOBS = 5 // 同时最大任务数
    }

    private val uploadId: String

    private val uploadedParts: ConcurrentLinkedQueue<PartInfo> = ConcurrentLinkedQueue()

    init {
        val partUploadInitRequest = PartUploadInitRequest().setKey(key)
        val initResult = tosClient.partUploadInit(partUploadInitRequest)
        uploadId = initResult.uploadID
    }


    @OptIn(ExperimentalTime::class)
    suspend fun upload(file: File) = measureTime {
        val fileSize = file.length()
        val numChunks = calculateNumberOfChunks(fileSize)
        if (numChunks > 1) {
            val channel = Channel<Int>(Channel.UNLIMITED) // 任务通道
            val jobs = List(MAX_CONCURRENT_JOBS) {
                CoroutineScope(Dispatchers.IO).launch {
                    for (chunkIndex in channel) {
                        val start = chunkIndex * RECOMMENDED_CHUNK_SIZE
                        val actualChunkSize =
                            if (chunkIndex == numChunks - 1) fileSize - start else RECOMMENDED_CHUNK_SIZE
                        retry(3, 30_000) {
                            uploadChunk(file, chunkIndex + 1, start, actualChunkSize)
                        }
                    }
                }
            }
            println("Uploading $file -> $key,Chunks number $numChunks")
            for (i in 0 until numChunks) {
                channel.send(i)
            }
            channel.close()
            jobs.joinAll()
            println("Part Upload Success[${file.name}]")
            val completedResult = tosClient.partUploadComplete(key, uploadId, uploadedParts.toList())
            println("Uploaded Success $completedResult")
        } else {
            println("Upload $file -> $key")
            retry(3, 30_000) {
                file.inputStream().use { input ->
                    tosClient.putObject(key, input.readBytes())
                }
            }
        }
    }

    private fun calculateNumberOfChunks(fileSize: Long): Int {
        if (fileSize <= RECOMMENDED_CHUNK_SIZE) return 1
        var numChunks = (fileSize / RECOMMENDED_CHUNK_SIZE).toInt()
        if (fileSize % RECOMMENDED_CHUNK_SIZE > 0) {
            if (fileSize % RECOMMENDED_CHUNK_SIZE < MIN_CHUNK_SIZE) {
                numChunks-- // Merge the last small chunk with the previous chunk
            } else {
                numChunks++
            }
        }
        return numChunks
    }

    private fun uploadChunk(file: File, index: Int, start: Long, size: Long) {
        RandomAccessFile(file, "r").use { raf ->
            val data = ByteArray(size.toInt())
            raf.seek(start)
            raf.readFully(data)
            val partUploadRequest =
                PartUploadRequest().setKey(key).setPartNumber(index).setUploadID(uploadId).setData(data)
            val partUploadResult = tosClient.partUpload(partUploadRequest)
            uploadedParts.add(partUploadResult.part)
            // Simulate uploading the chunk
            println("Uploading chunk[${index}] from ${start} size ${size}" )
            // Here you would write the code to upload the data
        }
    }

    private suspend inline fun <reified T> retry(
        times: Int,
        delayMillis: Long,
        block: () -> T
    ): T {
        var currentAttempt = 0
        var lastError: Throwable? = null
        while (currentAttempt < times) {
            try {
                return block()
            } catch (e: Throwable) {
                println("retry:$currentAttempt")
                e.printStackTrace()
                lastError = e
                currentAttempt++
                if (currentAttempt < times) {
                    delay(delayMillis)
                }
            }
        }

        throw lastError ?: IllegalStateException("Unexpected error in retry function")
    }
}