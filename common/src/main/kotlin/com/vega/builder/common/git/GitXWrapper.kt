package com.vega.builder.common.git

import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.utils.runCommand
import com.vega.builder.pipeline.BuildConfig
import java.io.File

object GitXWrapper {
    var gitx: String = ""

    fun downloadGitX(targetPath: File) {
        if (!targetPath.exists()) {
            targetPath.mkdirs()
        }
        gitx = "${targetPath.absolutePath}${File.separator}gitx"
        if (File(gitx).exists() && File("${targetPath.absolutePath}${File.separator}gitx-server").exists()) {
            return
        }
        if (!targetPath.exists()) {
            targetPath.mkdirs()
        }
        val machineName = System.getProperty("os.name")
        var gitxServerUrl: String
        var gitxUrl: String
        when {
            machineName.startsWith("Windows") -> {
                println("Windows")
                gitxServerUrl =
                    "https://${BuildConfig.hide_tos_url}/obj/aircodepubilc/gitx/windows/x86_64/gitx-server.exe.gz"
                gitxUrl = "https://${BuildConfig.hide_tos_url}/obj/aircodepubilc/gitx/windows/x86_64/gitx.exe.gz"
            }

            !machineName.contains("Mac", ignoreCase = true) -> {
                println("Linux")
                gitxServerUrl = "https://${BuildConfig.hide_tos_url}/obj/aircodepubilc/gitx/linux/x86_64/gitx-server.gz"
                gitxUrl = "https://${BuildConfig.hide_tos_url}/obj/aircodepubilc/gitx/linux/x86_64/gitx.gz"
            }

            else -> {
                val machineArch = System.getProperty("os.arch")
                gitxServerUrl = "https://${BuildConfig.hide_tos_url}/obj/aircodepubilc/gitx/macos/x86_64/gitx-server.gz"
                gitxUrl = "https://${BuildConfig.hide_tos_url}/obj/aircodepubilc/gitx/macos/x86_64/gitx.gz"
                if (machineArch == "arm64") {
                    println("Darwin arm64")
                    gitxServerUrl =
                        "https://${BuildConfig.hide_tos_url}/obj/aircodepubilc/gitx/macos/arm64/gitx-server.gz"
                    gitxUrl = "https://${BuildConfig.hide_tos_url}/obj/aircodepubilc/gitx/macos/arm64/gitx.gz"
                }
            }
        }

        println("Download url is: $gitxUrl")

//        val processBuilder = ProcessBuilder()
//        processBuilder.directory(targetPath)

        if (machineName.startsWith("Windows")) {
            "cmd /c curl $gitxServerUrl -o gitx-server.exe.gz".runCommand(targetPath)
            "cmd /c curl $gitxUrl -o gitx-server.exe.gz".runCommand(targetPath)
            "cmd /c gzip -d gitx-server.exe.gz".runCommand(targetPath)
            "cmd /c gzip -d gitx.exe.gz".runCommand(targetPath)
            "cmd /c chmod -d gitx.exe".runCommand(targetPath)
            "cmd /c chmod -d gitx-server.exe".runCommand(targetPath)
            "cmd /c curl https://${BuildConfig.hide_tos_url}/obj/gradle/gitx.cmd -o gitx.cmd".runCommand(targetPath)
        } else {
            Command("curl")
                .args(gitxServerUrl, "-o", "gitx-server.gz")
                .directory(targetPath)
                .default()
                .spawn()
                .wait()
            Command("curl")
                .args(gitxUrl, "-o", "gitx.gz")
                .directory(targetPath)
                .default()
                .spawn()
                .wait()
            Command("gzip")
                .args("-d", "gitx-server.gz")
                .default()
                .directory(targetPath)
                .spawn()
                .wait()
            Command("gzip")
                .args("-d", "gitx.gz")
                .directory(targetPath)
                .default()
                .spawn()
                .wait()
            Command("chmod")
                .args("+x", "gitx")
                .directory(targetPath)
                .default()
                .spawn()
                .wait()
            Command("chmod")
                .args("+x", "gitx-server")
                .directory(targetPath)
                .default()
                .spawn()
                .wait()
        }
    }
}
