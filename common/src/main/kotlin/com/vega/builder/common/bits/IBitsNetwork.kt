package com.vega.builder.common.bits

import com.vega.builder.common.bits.model.workflow.step.update.StepUpdateRequest
import com.vega.builder.common.network.BaseUrl
import com.vega.builder.pipeline.BuildConfig
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

@BaseUrl("https://bits.bytedance.net/openapi/")
interface IBitsNetwork {
    @POST("workflow/step/update")
    @Headers(BuildConfig.hide_bits_token)
    suspend fun workflowStepUpdate(@Body req: StepUpdateRequest): Map<Any, Any>
}
