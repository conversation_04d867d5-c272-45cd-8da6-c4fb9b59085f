import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import de.undercouch.gradle.tasks.download.Download
import java.lang.System.getenv

plugins {
    `kotlin-dsl`
    id("de.undercouch.download") version "5.6.0"
}
val isTTP = System.getenv().getOrDefault("TTP_BUILD_ENV", "false") != "false"
val downloadTask = tasks.register<Download>("downloadDeps") {
    if (isTTP) {
        src("https://bits-api.tiktok-sce.org/api/open/tos/ttclient-android/bytesign/bytesign-1.0.7.jar") // 替换为实际URL
    } else {
        src("https://tosv.byted.org/obj/bytesign-v2-android-standalone/bytesignjar-1.0.8.jar") // 替换为实际URL
    }

    dest(gradle.parent?.rootProject?.layout?.projectDirectory?.file("libs/bytesignjar-1.0.7.jar")?.asFile.apply { println(this?.absolutePath) })
    overwrite(true)
    onlyIfModified(true)
    retries(3)
    connectTimeout(30000)
    readTimeout(60000)
}
group = "pipeline.buildlogic"

afterEvaluate {
    tasks.named<Jar>("jar").get().dependsOn(downloadTask)
}
java {
    sourceCompatibility = JavaVersion.VERSION_11
    targetCompatibility = JavaVersion.VERSION_11
}
tasks.withType<KotlinCompile>().configureEach {
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }
}

dependencies {
    compileOnly(gradleApi())
    compileOnly(gradleKotlinDsl())
    compileOnly(kotlin("gradle-plugin"))
    compileOnly(libs.tos.core)
    compileOnly(libs.jgit)
    compileOnly(libs.bundles.retrofit)
    compileOnly(libs.kotlinx.coroutines)
    compileOnly("com.guardsquare:proguard-gradle:7.4.2")
    compileOnly("com.gradleup.shadow:shadow-gradle-plugin:9.0.0-beta17")
}

tasks {
    validatePlugins {
        enableStricterValidation = true
        failOnWarning = true
    }
}

gradlePlugin {
    plugins {
        register("androidComposeApplication") {
            id = "com.vega.builder.build-logic.shell"
            implementationClass = "PipelineStandalone"
        }
    }
}