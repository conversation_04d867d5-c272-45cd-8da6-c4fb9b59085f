package com.vega.builder.pipeline.utils

import com.vega.builder.common.utils.MD5Utils
import org.junit.jupiter.api.Test

/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/23
 */
class GitUtilsTest {
    @Test
    fun extractRepoNameFromGitUrl() {
        var vega = "******************:faceu-android/vega.git"
        var repoName = GitUtils.extractRepoNameFromGitUrl(vega)
        assert(repoName == "vega")
        vega = "https://code.byted.org/faceu-android/vega.git"
        repoName = GitUtils.extractRepoNameFromGitUrl(vega)
        assert(repoName == "vega")
    }

}