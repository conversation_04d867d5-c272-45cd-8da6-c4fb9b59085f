package com.vega.builder.common.network.api

import com.vega.builder.common.network.BaseUrl
import com.vega.builder.pipeline.BuildConfig
import okhttp3.ResponseBody
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.QueryMap
import retrofit2.http.Streaming

/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/21
 */
@BaseUrl("http://${BuildConfig.hide_starling_url}/gateway/openapi/")
interface II18nApi {

    @GET("project/namespace/text/download")
    @Streaming
    suspend fun downloadI18n(
        @QueryMap queryMap: Map<String, String> = emptyMap(),
        @Header("agw-auth") auth: String = "",
//        @Header("Accept") contentType: String = "application/json, text/plain, */*",
    ): ResponseBody

    @GET("project/namespace/sources")
    suspend fun queryI18nIds(
        @QueryMap queryMap: Map<String, String> = emptyMap(),
        @Header("agw-auth") auth: String = "",
        @Header("content-type") contentType: String = "application/json",
    ): I18nIdResp
}

data class I18nIdResp(
    val data: List<I18nIdData> = emptyList(),
)

data class I18nIdData(
    val keyText: String?,
    val content: String?,
    val createdAt: String?,
    val updatedAt: String?,
    val targetTexts: List<I18nTargetText> = emptyList(),
)

data class I18nTargetText(
    val id: Int,
    val sourceTextId: Int,
    val namespaceId: Int,
    val distributed: Int,
    val taskId: String?,
    val lang: String?,
    val keyText: String?,
    val content: String?,
    val tagName: String?,
    val createdAt: String?,
    val updatedAt: String?,
)