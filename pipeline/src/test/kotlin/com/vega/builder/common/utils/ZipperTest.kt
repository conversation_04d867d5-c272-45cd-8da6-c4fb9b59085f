package com.vega.builder.common.utils

import com.vega.builder.common.utils.compression.Compression.Companion.compressFiles
import com.vega.builder.common.utils.compression.TarGz
import java.io.File
import kotlin.io.path.createTempDirectory
import kotlin.io.path.createTempFile
import kotlin.test.Test

class ZipperTest {
    @Test
    fun `Zip File use tar-gz`() {
        val directory = createTempDirectory()
        val outputFile = createTempFile(directory = directory, suffix = TarGz.extension).toFile()

        compressFiles(
            TarGz,
            File("/Users/<USER>/develop/bytedance/lv-build-script-plugin/MultiRepoPublish").walk().toList(),
            File("/Users/<USER>/develop/bytedance/lv-build-script-plugin/MultiRepoPublish"),
            outputFile
        )
        println(outputFile)
    }
}