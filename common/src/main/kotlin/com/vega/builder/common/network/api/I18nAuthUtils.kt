package com.vega.builder.common.network.api

import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec


/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/21
 */
@Throws(Exception::class)
fun sha256HMAC(key: String?, data: ByteArray?): String? {
    val sha256Hmac: Mac = Mac.getInstance("HmacSHA256")
    val keySpec = SecretKeySpec(key!!.toByteArray(), "HmacSHA256")
    sha256Hmac.init(keySpec)
    val hash: ByteArray = sha256Hmac.doFinal(data)
    val hexChars = ByteArray(hash.size * 2)
    for (j in hash.indices) {
        val s = String.format("%02x", hash[j].toInt() and 0xFF).toByteArray()
        hexChars[j * 2] = s[0]
        hexChars[j * 2 + 1] = s[1]
    }
    return String(hexChars)
}

@Throws(Exception::class)
fun sign(ver: String?, ak: String?, sk: String?, data: ByteArray?): String {
    val expiration = 1800 //  有效时间, 单位是s，根据自己的业务的实际情况调整
    val timestamp = System.currentTimeMillis() / 1000L
    val signKeyInfo = String.format("%s/%s/%d/%d", ver, ak, timestamp, expiration)
    val signKey = sha256HMAC(sk, signKeyInfo.toByteArray())
    val signResult = sha256HMAC(signKey, data)
    return String.format("%s/%s", signKeyInfo, signResult)
}