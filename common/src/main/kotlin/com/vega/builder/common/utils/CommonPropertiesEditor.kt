package com.vega.builder.common.utils

import java.io.Closeable
import java.io.File

class CommonPropertiesEditor(
    val propertiesFile: File,
): Closeable {
    private val properties by lazy {
        PropertiesEditor(propertiesFile.toPath())
    }

    fun getProperty(key: String): String? = properties[key]

    fun parser(
        pName: String,
        value: String,
    ) {
        properties[pName] = value
    }

    fun edit(
        key: String,
        converter: (key: String, value: String) -> String,
    ): CommonPropertiesEditor {
        properties[key] = converter(key, properties[key] ?: "")
        return this
    }

    fun edit(
        key: String,
        value: String,
    ): String {
        val oldValue = properties[key]
        properties[key] = value
        return "$oldValue"
    }

    fun add(
        key: String,
        value: String,
    ): CommonPropertiesEditor {
        if (!properties.containsKey(key)) {
            properties[key] = value
        }
        return this
    }

    fun save() {
        properties.save()
    }

    override fun close() {
        save()
    }
}
