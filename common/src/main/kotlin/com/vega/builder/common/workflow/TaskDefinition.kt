package com.vega.builder.common.workflow

@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class TaskDefinition(
    val name: String,
    val stage: Stage,
    val displayName: String,
    val allowFailure: Boolean = false,
    val cachable: Boolean = false,
)

const val GitInitializeStageName = "Git Initialization"
const val EnvPrepareStageName = "Environment Preparation"
const val BuildStageName = "Build"
const val ArtifactsStageName = "Artifact Processing"
const val AfterBuild = "Post-Build"
const val CheckStage = "Check"

enum class Stage(
    val display: String,
) {
    GitInitialize(GitInitializeStageName),
    Build(BuildStageName),
    Prepare(EnvPrepareStageName),
    Artifacts(ArtifactsStageName),
    After(AfterBuild),
    Check(CheckStage)
}
