dependencyResolutionManagement {
    repositories {
        maven("https://maven.byted.org/repository/android_public/")
        maven("https://maven.byted.org/repository/releases")
        maven("https://maven.byted.org/repository/snapshots")
    }
    versionCatalogs {
        create("libs") {
            from(files("../gradle/libs.versions.toml"))
        }
    }
}

pluginManagement {
    repositories {
        maven("https://maven.byted.org/repository/android_public/")
        maven("https://maven.byted.org/repository/releases")
        maven("https://maven.byted.org/repository/snapshots")
    }
}

rootProject.name = "build-logic"
include(":pipeline-gradle-plugin")