package com.vega.builder.pipeline.task.artifacts

import com.vega.builder.common.logger.configureLogback
import com.vega.builder.common.throwable.PipelineThrowableHandler
import com.vega.builder.common.utils.setenvMock
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.di.configureKoin
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.koin.mp.KoinPlatform

class DexVMPOfflineTaskTest {
    @Test
    fun testDownloadDevVMPClang() {
        setenvMock("SYSTEM_MOCK", "true")
        setenvMock("WORKSPACE", System.getProperty("user.home"))
        PipelineThrowableHandler.register()

        configureLogback()
        configureKoin()
        runBlocking {
            KoinPlatform.getKoin().get<BuildParams>().loadParams()
        }
    }

}