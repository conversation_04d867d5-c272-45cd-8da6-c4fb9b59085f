package com.vega.builder.common.utils

import com.vega.builder.common.logger.logger
import java.io.File
import java.nio.file.Files
import java.nio.file.Path

class PropertiesEditor(
    private val path: Path,
) {
    companion object {
        private const val TAG = "PropertiesEditor"
    }

    private val properties = LinkedHashMap<String, String>()
    private val originalContent: MutableList<String> = mutableListOf()

    init {
        load(path)
    }

    private fun load(path: Path) {
        if (Files.exists(path)) {
            File(path.toUri()).useLines { lines ->
                lines.forEach { line ->
                    originalContent.add(line)
                    if (!line.trim().startsWith("#") && line.contains("=")) {
                        val (key, value) = line.split("=", limit = 2).map { it.trim() }
                        properties[key] = value
                    }
                }
            }
        }
    }

    operator fun get(key: String): String? = properties[key]

    operator fun set(
        key: String,
        value: String,
    ) {
        properties[key] = value
    }

    fun containsKey(s: String) = properties.containsKey(s)

    fun setProperty(
        key: String,
        value: String,
    ) {
        properties[key] = value
    }

    fun save() {
        logger().info("Saving properties:")
        val newContent =
            originalContent
                .map { line ->
                    if (line.contains("=") && !line.trim().startsWith("#")) {
                        val (key, value) = line.split("=", limit = 2).map { it.trim() }
                        if (key in properties && value != properties[key]) {
                            logger().info("change:$key[$value->${properties[key]}]")
                            "$key=${properties[key]}"
                        } else {
                            line
                        }
                    } else {
                        line
                    }
                }.toMutableList()

        // Append new properties that were not in the original file
        properties.keys
            .filter { key ->
                newContent.none { it.matches(Regex("^\\s*$key\\s*=.*$")) }
            }.forEach { key ->
                newContent.add("$key=${properties[key]}")
            }

        File(path.toUri()).writeText(newContent.joinToString("\n"))
    }
}
