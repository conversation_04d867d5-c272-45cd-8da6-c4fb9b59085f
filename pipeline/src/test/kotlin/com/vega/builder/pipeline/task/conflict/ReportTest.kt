package com.vega.builder.pipeline.task.conflict

import com.vega.builder.common.airplane.IAirplaneApi
import com.vega.builder.common.airplane.ReportConflictInfoRequest
import com.vega.builder.common.network.request
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ReportTest {

    @Test()
    fun reportTest() {
        runBlocking {
            val result = true
            val branch = "p/robot.merge/release_16.0.0_to_rc_develop_WmqHB5"
            val targetBranch = "p/robot.merge/release_16.0.0_to_rc_develop_WmqHB5"
            val projectId = 40279
            val mrId = 7244736
            val request = ReportConflictInfoRequest(
                conflictAutoFix = result,
                branch = branch,
                targetBranch = targetBranch,
                projectId = projectId,
                mrId = mrId,
            )
            val response = request(
                IAirplaneApi::reportConflictInfo, request
            )

            println(response.string())
        }

    }
}