# 指定在混淆时不生成大小写混合的类名
-dontusemixedcaseclassnames
# 指定不忽略非公共库类
-dontskipnonpubliclibraryclasses
# 指定要放入类文件的SourceFile属性中的常量字符串
-renamesourcefileattribute SourceFile
# 指定要保留的所有可选属性
-keepattributes SourceFile,LineNumberTable
-dontoptimize
# 指定在处理期间写出更多信息
-target 11
-verbose
# ========== 禁用所有混淆 ==========
-dontobfuscate                  # 关键：禁用名称混淆
-useuniqueclassmembernames      # 防止成员名被统一重命名
-keepparameternames             # 保留方法参数名

# ========== 基础保留规则 ==========
-keepattributes *              # 保留所有属性（注解、泛型等）
-keep,allowshrinking class **  # 允许缩减但保留类结构
-optimizations !class/unboxing/enum
-ignorewarnings
-keep public class com.vega.builder.pipeline.*{
    *;
}

# 保留所有 Kotlin 协程相关类和方法
-keep class kotlin.coroutines.** { *; }
-keep class kotlinx.coroutines.** { *; }

# 保留协程状态机相关元数据
-keepclassmembers class ** {
    @kotlin.coroutines.* *;
}

# 保留所有 suspend 函数
-keepclassmembers class * {
    *** suspend*(...);
}
# 保留 Kotlin 元数据注解
-keepclassmembers class ** {
    @kotlin.Metadata *;
}

# 保留所有 Kotlin 元数据和内部结构
-keepclassmembers class kotlin.** { *; }
-keepclassmembers class kotlinx.** { *; }

# 保留协程相关

# 保留所有可能通过反射访问的元素
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations
-keepattributes Signature, Exceptions, InnerClasses

# 禁用危险优化

# Kotlin 保留规则
-keep class kotlin.** { *; }
-keep class kotlinx.** { *; }
-keep class kotlinx.coroutines.** { *; }
-keepclassmembers class kotlinx.coroutines.** { *; }
-keepclassmembers class * {
    public *** invokeSuspend(java.lang.Object);
}

# 保留协程内部类
-keep class kotlin.coroutines.jvm.internal.** { *; }

# 保留挂起函数相关元数据
-keepclassmembers class * {
    public *** invokeSuspend(java.lang.Object);
}

# ========== Gson 序列化保留 ==========

# 保留所有类成员（防止 Gson 访问失败）
-keepclassmembers class * {
    *;
}


# 保留注解（Gson 使用注解配置序列化）
-keepattributes *Annotation*

# 保留枚举值
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留自定义序列化器
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

-keepclassmembers class * implements java.io.Serializable {
    <fields>;
    <methods>;
}

# 保留所有JNI相关类和方法
-keepclasseswithmembers class * {
    native <methods>;
}
# 保留通过JNI访问的类
-keep class io.** { *; }
# ================ LOGBACK PROGUARD RULES ================
-keep class ch.qos.logback.** { *; }
-keep class org.slf4j.** { *; }

# 保留 SPI 和服务加载机制
-keepnames class * implements org.slf4j.spi.SLF4JServiceProvider
-keep class ch.qos.logback.classic.spi.LogbackServiceProvider { *; }

# 保留配置文件处理
-keep class ch.qos.logback.core.joran.** { *; }
-keep class ch.qos.logback.core.status.** { *; }

# 保留注解处理器
-keepattributes *Annotation*

# 保留反射使用的类和方法
-keepclassmembers class * {
    @org.slf4j.event.EventConstants *;
}

# 保留自定义组件
-keep public class * extends ch.qos.logback.core.AppenderBase
-keep public class * extends ch.qos.logback.core.LayoutBase
-keep public class * extends ch.qos.logback.core.filter.Filter

# 保留序列化/反序列化需要的类
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保留资源文件 (logback.xml/groovy)
-keepclassmembers class * {
    public static ** getContext();
}



