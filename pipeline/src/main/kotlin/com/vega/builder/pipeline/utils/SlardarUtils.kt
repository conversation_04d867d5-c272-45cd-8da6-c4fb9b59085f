package com.vega.builder.pipeline.utils

import com.vega.builder.common.kommand.process.Command
import com.vega.builder.pipeline.task.artifacts.RowSlardarMappingUpload.Companion.buildIDRegex
import com.vega.builder.pipeline.task.artifacts.RowSlardarMappingUpload.Companion.isStrippedRegex
import java.util.Locale

object SlardarUtils {


    fun queryNativeFileInfo(path: String): NativeFileInfo? {
        try {
            var uuid = ""
            var isStripped = false
            val result = Command("file").args(path).output()
            if (result.status == 0) {
                val matchResult = buildIDRegex.find(result.stdout ?: "")
                if (matchResult != null && matchResult.groupValues.size >= 2) {
                    uuid = compatibleBuildID(matchResult.groupValues[1])
                }
                isStripped = !isStrippedRegex.containsMatchIn(result.stdout ?: "")
            }
            return if (uuid.isNotEmpty()) {
                NativeFileInfo(uuid, isStripped)
            } else {
                null
            }

        } catch (_: Exception) {
        }
        return null
    }

    private fun compatibleBuildID(buildId: String): String {
        var uuid = ""
        if (buildId.length < 16) {
            uuid = buildId
        } else {
            // index0~index15位互换
            uuid += buildId[6]
            uuid += buildId[7]
            uuid += buildId[4]
            uuid += buildId[5]
            uuid += buildId[2]
            uuid += buildId[3]
            uuid += buildId[0]
            uuid += buildId[1]
            uuid += buildId[10]
            uuid += buildId[11]
            uuid += buildId[8]
            uuid += buildId[9]
            uuid += buildId[14]
            uuid += buildId[15]
            uuid += buildId[12]
            uuid += buildId[13]
            if (buildId.length >= 32) {
                uuid += buildId.substring(16, 32)
                uuid += '0'
            }
        }
        return uuid.uppercase(Locale.getDefault())
    }

    data class NativeFileInfo(val buildId: String, val stripped: Boolean = false)
}