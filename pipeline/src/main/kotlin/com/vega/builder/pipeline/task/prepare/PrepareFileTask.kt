package com.vega.builder.pipeline.task.prepare

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.utils.CommonPropertiesEditor
import com.vega.builder.common.utils.ResourceUtils
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PathDeclare
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.TargetInfo
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.context.buildTarget
import com.vega.builder.pipeline.context.channel
import com.vega.builder.pipeline.context.isCoverage
import com.vega.builder.pipeline.context.isOutBuild
import com.vega.builder.pipeline.task.prepare.parses.xml.NetworkSecurityParse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.core.component.inject
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date

@TaskDefinition(PrepareFileTask.TAG, stage = Stage.Prepare, "Document Preparation")
class PrepareFileTask : PipelineTask() {
    companion object {
        const val TAG = "PrepareFileTask"
    }
    val buildParams: BuildParams by inject()

    val pipelineContext:PipelineContextImpl by inject()

    val workspaceParams: WorkspaceParams by inject()

    val pathDeclare: PathDeclare by inject()

    override suspend fun run() {
        NetworkSecurityParse().parse(pathDeclare.networkSecurityFile)
        buildParams.channel?.let { channel ->
            logger().info("Channel build: $channel")
            CommonPropertiesEditor(pathDeclare.ssPropertiesFile).use { editor ->
                editor.edit("meta_umeng_channel", channel)
                // 获取当前日期
                val buildDate = SimpleDateFormat("yyyyMMdd").format(Date())
                editor.edit("release_build", "${pipelineContext.commitId}_$buildDate")
            }
        }
        val ciMultiSourceConfig = File("${workspaceParams.targetProjectDir}/ci-multi-source-config.properties")
        if (buildParams.isEnable("IS_FORCE_ALL_SOURCE", false)) {
            ciMultiSourceConfig.appendText("\nconfig.inner_source=true\n")
            logger().info("Outsourcing, sub-warehouse introduces source code and compiles directly!")
        }
        val replaceVersionInfo = buildParams.getOrDefault("REPLACE_VERSION", "")
        if (replaceVersionInfo.isNotBlank()) {
            val info = Gson().fromJson<Map<String, String>>(replaceVersionInfo)
            for (entry in info) {
                ciMultiSourceConfig.appendText(
                    "\nconfig.replace_version.${
                        entry.key.replace(":", "_")
                    }=${entry.value}\n"
                )
            }
        }

        if (buildParams.isCoverage) {
            logger().info("Coverage build")

            val jacocoUtilsFileName =
                when (buildParams.buildTarget) {
                    TargetInfo.Cc -> {
                        "JacocoUtils_cc.java"
                    }
                    TargetInfo.Dreamina -> {
                        "JacocoUtils_lv.java"
                    }
                    else -> {
                        "JacocoUtils_lv.java"
                    }
                }
            val jacocoContent = ResourceUtils.readResource(jacocoUtilsFileName)

            val jacocoRootTarget =
                "${workspaceParams.targetProjectDir}${File.separator}${buildParams.buildTarget.modulePath}/src/main/java/com/vega/launcher/"
            val jacocoRootFile = File(jacocoRootTarget)
            if (!jacocoRootFile.exists()) {
                jacocoRootFile.mkdirs()
            }
            val jacocoFile = File(jacocoRootFile, "JacocoUtils.java")
            withContext(Dispatchers.IO) {
                jacocoFile.writeText(jacocoContent)
            }
            logger().info("Copy jacocoFile to ${jacocoFile.absolutePath}")
        }
    }
}
