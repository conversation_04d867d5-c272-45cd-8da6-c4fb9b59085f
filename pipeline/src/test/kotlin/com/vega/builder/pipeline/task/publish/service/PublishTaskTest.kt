package com.vega.builder.pipeline.task.publish.service

import com.vega.builder.common.logger.configureLogback
import com.vega.builder.common.throwable.PipelineThrowableHandler
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.tos.slicePutObject
import com.vega.builder.common.utils.printHello
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.di.configureKoin
import com.vega.builder.pipeline.task.publish.PublishUtils
import kotlinx.coroutines.runBlocking
import org.koin.mp.KoinPlatform
import java.io.File
import kotlin.test.Test

class PublishTaskTest {
    @Test
    fun `test publish`() {
        runBlocking {
            PipelineThrowableHandler.register()
            printHello()
            configureLogback()
            configureKoin()
            KoinPlatform.getKoin().get<BuildParams>().loadParams()
            val repoRootFile = File("/Users/<USER>/develop/bytedance")
            val version = "*******.0.0-alpha.01"
            val repoName = "videoeditor"
            val result = CompressionRepoService(repoRootFile, repoName).compressionRepo()
            println(result.absolutePath)
//            TosConfig.LvBuildArtifact.createTosClient()
//                .slicePutObject(
//                    PublishUtils.buildTosId(
//                        "com.lemon.faceu/videoeditor_cc_repo_info".replace(".", "_"),
//                        version
//                    ),
//                    result
//                )
        }
    }
}