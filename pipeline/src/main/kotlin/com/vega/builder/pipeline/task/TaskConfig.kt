package com.vega.builder.pipeline.task

import com.vega.builder.common.logger.logger
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.task.artifacts.ArtifactsUpload
import com.vega.builder.pipeline.task.artifacts.DexVMPOfflineTask
import com.vega.builder.pipeline.task.artifacts.SignAndChannelTask
import com.vega.builder.pipeline.task.artifacts.RowSlardarMappingUpload
import com.vega.builder.pipeline.task.artifacts.TTPArtifactsUpload
import com.vega.builder.pipeline.task.artifacts.TTPResultCollectTask
import com.vega.builder.pipeline.task.artifacts.TTPSlardarMappingUpload
import com.vega.builder.pipeline.task.benchmark.DownloadGradleProfilerEnv
import com.vega.builder.pipeline.task.benchmark.RunBenchmark
import com.vega.builder.pipeline.task.builder.BuilderTask
import com.vega.builder.pipeline.task.builder.TTPBuildWrapper
import com.vega.builder.pipeline.task.check.TTPVersionCheck
import com.vega.builder.pipeline.task.clone.GitInitializeTask
import com.vega.builder.pipeline.task.conflict.AutoResolveConflictTask
import com.vega.builder.pipeline.task.prepare.PrepareBuildPropertiesTask
import com.vega.builder.pipeline.task.prepare.PrepareFileTask
import com.vega.builder.pipeline.task.prepare.PrepareSubRepoTask
import com.vega.builder.pipeline.task.publish.PublishSourceTask
import com.vega.builder.pipeline.task.publish.PublishTask
import com.vega.builder.pipeline.task.publish.ttp.PublishTTPSourceTask
import com.vega.builder.pipeline.task.publish.ttp.SBomCustomGenTask
import com.vega.builder.pipeline.task.publish.ttp.SBomGradleGenTask
import com.vega.builder.pipeline.task.publish.ttp.SBomUploadTask
import com.vega.builder.pipeline.task.publish.ttp.TTPJobWrapperTask
import com.vega.builder.pipeline.task.repo.ClearRepo
import org.koin.mp.KoinPlatform

object TaskConfig

enum class TaskType(val value: String) {
    /**
     * build
     */
    Build("build"),
    TTPBuild("ttp_build"),
    TTPBuildWrapper("ttp_build_wrapper"),

    /**
     * publish
     */
    Publish("publish_sub_repo"),
    PublishSource("publish_sub_repo_source"),
    PublishTTPSource("publish_tpp_source"),
    PublishTTPBinary("publish_tpp_binary"),

    /**
     * Tools
     */
    AutoResolveConflict("auto_resolve_conflict"),
    ClearRepo("clear_repo"),
    Benchmark("benchmark"),

    /**
     * Check
     */
    CheckTTPVersion("check_ttp_version");

    companion object {
        fun fromValue(value: String): TaskType = TaskType.entries.find { it.value == value } ?: Build
    }
}

fun buildTaskList(): List<PipelineTask> = listOf(
    GitInitializeTask(),
    PrepareSubRepoTask(),
    PrepareBuildPropertiesTask(),
    PrepareFileTask(),
    BuilderTask(),
    DexVMPOfflineTask(),
    SignAndChannelTask(),
    ArtifactsUpload(),
    RowSlardarMappingUpload()
)

fun buildNewTTPTaskList(): List<PipelineTask> = listOf(
    GitInitializeTask(),
    PrepareBuildPropertiesTask(),
    PrepareFileTask(),
    BuilderTask(),
    DexVMPOfflineTask(),
    SignAndChannelTask(),
    TTPArtifactsUpload(),
    TTPSlardarMappingUpload(),
    SBomGradleGenTask(),
    SBomUploadTask()
)

fun buildTTPTaskList(): List<PipelineTask> = listOf(
//    GitInitializeTask(),
    PrepareBuildPropertiesTask(),
    PrepareFileTask(),
    BuilderTask(),
    DexVMPOfflineTask(),
    SignAndChannelTask(),
    RowSlardarMappingUpload(),
    SBomGradleGenTask(),
    SBomUploadTask(),
    TTPResultCollectTask()
)

/**
 * TTP环境任务存在双任务模式，在组件平台下，执行WrapperTask，在TTP环境下，执行实际任务
 */
fun publishTTPSourceTaskList(isTTP: Boolean): List<PipelineTask> = if (isTTP) {
    listOf(
        GitInitializeTask(),
        PublishTTPSourceTask(),
        SBomCustomGenTask(),
        SBomUploadTask()
    )
} else {
    listOf(
        TTPJobWrapperTask()
    )
}

fun publishTTPBinaryTaskList(isTTP: Boolean): List<PipelineTask> = if (isTTP) {
    listOf(
        GitInitializeTask(),
        PrepareBuildPropertiesTask(),
        PrepareFileTask(),
        PublishTask(),
        TTPSlardarMappingUpload(),
        SBomUploadTask()
    )
} else {
    listOf(
        TTPJobWrapperTask(),
        RowSlardarMappingUpload()
    )
}

fun publishTaskList(): List<PipelineTask> = listOf(
    GitInitializeTask(),
    PrepareBuildPropertiesTask(),
    PrepareFileTask(),
    PublishTask(),
    RowSlardarMappingUpload()
)

fun publishSourceTaskList(): List<PipelineTask> = listOf(
    PublishSourceTask()
)


fun autoResolveConflictTaskList(): List<PipelineTask> = listOf(
    GitInitializeTask(),
    AutoResolveConflictTask(),
)

fun clearRepoTaskList(): List<PipelineTask> = listOf(
    GitInitializeTask(),
    ClearRepo()
)

fun buildBenchmarkTaskList(): List<PipelineTask> = listOf(
    GitInitializeTask(),
    DownloadGradleProfilerEnv(),
    RunBenchmark()
)


suspend fun configureTask() {
    val buildParams = KoinPlatform.getKoin().get<BuildParams>()
    val pipelineContext: PipelineContextImpl = KoinPlatform.getKoin().get<PipelineContextImpl>()
    val taskList = when (TaskType.fromValue(buildParams["RUNNING_TYPE"].toString())) {
        TaskType.Build -> {
            TaskConfig.logger().info("Start Build")
            if (pipelineContext.isTTP) {
                buildTTPTaskList()
            } else {
                buildTaskList()
            }
        }

        TaskType.TTPBuild -> {
            TaskConfig.logger().info("Start ttp Build")
            if (pipelineContext.isTTP) {
                buildNewTTPTaskList()
            } else {
                listOf(TTPJobWrapperTask())
            }
        }

        TaskType.TTPBuildWrapper -> {
            listOf(
                TTPBuildWrapper(),
                RowSlardarMappingUpload()
            )
        }

        TaskType.Publish -> {
            TaskConfig.logger().info("Start Publish")
            publishTaskList()
        }

        TaskType.AutoResolveConflict -> {
            TaskConfig.logger().info("Start Auto Resolve Conflict")
            autoResolveConflictTaskList()
        }

        TaskType.ClearRepo -> {
            TaskConfig.logger().info("Start Clear Repo")
            clearRepoTaskList()
        }

        TaskType.Benchmark -> {
            TaskConfig.logger().info("Start Benchmark")
            buildBenchmarkTaskList()
        }

        TaskType.PublishTTPSource -> {
            TaskConfig.logger().info("Start TTP Source Publish")
            publishTTPSourceTaskList(pipelineContext.isTTP)
        }

        TaskType.PublishTTPBinary -> {
            TaskConfig.logger().info("Start TTP Binary Publish")
            publishTTPBinaryTaskList(pipelineContext.isTTP)
        }

        TaskType.PublishSource -> {
            TaskConfig.logger().info("Start SubRepo Source Publish")
            publishSourceTaskList()
        }

        TaskType.CheckTTPVersion -> {
            TaskConfig.logger().info("Start Check TTP version")

            listOf(GitInitializeTask(), TTPVersionCheck())
        }
    }
    for (task in taskList) {
        if (pipelineContext.isTTP) {
            task.executeWithTTP()
        } else {
            task.execute()
        }
    }
}