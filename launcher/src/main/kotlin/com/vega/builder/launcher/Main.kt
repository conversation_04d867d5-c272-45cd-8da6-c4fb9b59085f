package com.vega.builder.launcher

import com.github.ajalt.clikt.core.CliktCommand
import com.github.ajalt.clikt.core.main
import com.github.ajalt.clikt.parameters.options.flag
import com.github.ajalt.clikt.parameters.options.help
import com.github.ajalt.clikt.parameters.options.option
import com.github.ajalt.clikt.parameters.options.prompt
import com.github.ajalt.clikt.parameters.options.varargValues
import com.google.gson.Gson
import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.logger.configureLogback
import com.vega.builder.common.logger.logger
import com.vega.builder.common.tos.TosConfig
import java.io.File
import java.io.FileOutputStream
import java.io.InputStreamReader
import kotlin.system.exitProcess
import kotlinx.coroutines.runBlocking

fun main(args: Array<String>) {
    configureLogback()
    Launcher().main(args)
}

class Launcher : CliktCommand() {
    private val module: String by option("-m", "--module").prompt("Module to execute").help("Name of module to execute")
    private val args by option("-a", "--args").help("Arguments to pass through").varargValues()
    private val test by option("-t", "--test").flag(default = false)

    override fun run() = runBlocking {
        val tosClient = TosConfig.LvBuildScript.createTosClient()
        val result = tosClient.getObject("${module}/$module.info")?.objectContent
            ?: throw Exception("Module not found! Please check module name")
        val info = Gson().fromJson(InputStreamReader(result), ModuleInfo::class.java)

        val moduleCli = if (test) {
            tosClient.getObject("${module}/$module-test-${info.testVersion}")?.objectContent
                ?: throw Exception("Module download failed!")
        } else {
            tosClient.getObject("${module}/$module-${info.version}")?.objectContent
                ?: throw Exception("Module download failed!")
        }

        val sliFile = File(module)
        FileOutputStream(sliFile).use {
            moduleCli.copyTo(it)
        }

        Command("chmod").arg("+x").arg(sliFile.absolutePath).default().spawn().wait()

        val code = Command(sliFile.absolutePath)
            .apply {
                try {
                    if (module == "pipeline") {
                        logger().info("Executing $module")
                        args?.let {
                            logger().info("Args: ${it[0]} -> WORK_DIR")
                            logger().info("Args: ${it[1]} -> PUBLISH_PARAMS")
                            this.env("WORK_DIR", it[0])
                            this.env("PUBLISH_PARAMS", it[1])
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            .envs(System.getenv())
            .apply {
                <EMAIL>?.forEach {
                    this.arg(it)
                }
            }.default()
            .spawn()
            .wait()

        if (code != 0) {
            exitProcess(code)
        }
    }
}

data class ModuleInfo(
    var version: Int,
    var testVersion: Int
)
