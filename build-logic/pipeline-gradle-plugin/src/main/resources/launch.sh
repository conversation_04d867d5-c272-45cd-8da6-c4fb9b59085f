#!/usr/bin/env bash
ENV_UPPER=$(echo "$ENV" | tr '[:lower:]' '[:upper:]')
CLOUD_BUILD_UPPER=$(echo "$cloud_build" | tr '[:lower:]' '[:upper:]')
if [[ -n "${JOB_NAME}" || ("${ENV_UPPER}" == "JENKINS" && "${CLOUD_BUILD_UPPER}" == "true") || -n "${TASK_ID}" ]]; then
  # Check if JDK directory exists before exporting
    if [ -d "/opt/jdk-11.0.8" ]; then
      export JAVA_HOME=/opt/jdk-11.0.8
    else
      echo "Warning: JDK directory /opt/jdk-11.0.8 does not exist" >&2
    fi
fi
# Determine the Java command to use to start the JVM.
if [ -n "$JAVA_HOME" ] ; then
    if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
        # IBM's JDK on AIX uses strange locations for the executables
        JAVACMD="$JAVA_HOME/jre/sh/java"
    else
        JAVACMD="$JAVA_HOME/bin/java"
    fi
    if [ ! -x "$JAVACMD" ] ; then
        die "ERROR: JAVA_HOME is set to an invalid directory: $JAVA_HOME
Please set the JAVA_HOME variable in your environment to match the
location of your Java installation."
    fi
else
    JAVACMD="java"
    which java >/dev/null 2>&1 || die "ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.
Please set the JAVA_HOME variable in your environment to match the
location of your Java installation."
fi
echo "JAVACMD: $JAVACMD"
# 创建安全的临时文件（自动清理）
TMP_JAR=$(mktemp -t pipeline_XXXXXX.jar) || exit 1
echo "TMP_JAR: $TMP_JAR"
trap 'rm -f "$TMP_JAR"' EXIT  # 确保退出时删除临时文件

# 定位并提取JAR数据
ARCHIVE_START=$(awk '/^__ARCHIVE_BELOW__/ { print NR + 1; exit 0; }' "$0")
tail -n +$ARCHIVE_START "$0" > "$TMP_JAR"

# 执行临时JAR文件
exec "$JAVACMD" -jar "$TMP_JAR" "$@"

# 必须保留以下标记作为脚本结束
__ARCHIVE_BELOW__