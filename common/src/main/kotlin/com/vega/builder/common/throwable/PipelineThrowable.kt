package com.vega.builder.common.throwable

import kotlin.system.exitProcess

class PipelineThrowable(
    val errorType: ErrorType,
    original: Throwable? = null,
) : Exception(errorType.message, original)

class PipelineThrowableHandler : Thread.UncaughtExceptionHandler {
    override fun uncaughtException(
        t: Thread,
        e: Throwable,
    ) {

        println("Thread: ${t.name}")
        println("Exception: ${e.javaClass.name}: ${e.message}")
        println("Stack Trace:")
        e.printStackTrace()
        println("Thread status: ${t.state}")
        if (e is PipelineThrowable) {
            exitProcess(e.errorType.ordinal)
        } else {
            println("unknown error！")
            exitProcess(ErrorType.Unknown.code)
        }
    }

    companion object {
        fun register() {
            Thread.setDefaultUncaughtExceptionHandler(PipelineThrowableHandler())
        }
    }
}
