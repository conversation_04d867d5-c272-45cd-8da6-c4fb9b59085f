package com.vega.builder.common.network.api

import com.vega.builder.common.network.BaseUrl
import com.vega.builder.pipeline.BuildConfig
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path

@BaseUrl("https://${BuildConfig.hide_tt_maven}/")
interface ITiktokMavenApi {
    @GET("repository/scea-app-build-proxy/mobile-build-copy/{key}")
    suspend fun download(@Path("key", encoded = true) path: String): Response<ResponseBody>

}