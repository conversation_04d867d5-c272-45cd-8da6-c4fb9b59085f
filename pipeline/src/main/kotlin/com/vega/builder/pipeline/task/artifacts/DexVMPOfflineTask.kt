package com.vega.builder.pipeline.task.artifacts

import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.INetworkApi
import com.vega.builder.common.network.request
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.utils.ResourceUtils
import com.vega.builder.common.utils.compression.Compression
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.retry
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.BuildConfig
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.context.withAab
import com.vega.builder.pipeline.utils.SystemUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.io.File
import java.io.FileOutputStream
import java.nio.file.Files
import java.nio.file.attribute.PosixFilePermission
import kotlin.io.path.createTempDirectory
import kotlin.io.path.createTempFile
import kotlin.io.path.extension
import kotlin.io.path.nameWithoutExtension
import kotlin.io.path.outputStream
import kotlin.io.path.writeText

@TaskDefinition(DexVMPTask.TAG, stage = Stage.Artifacts, displayName = "Reinforcement(Local)")
class DexVMPOfflineTask : PipelineTask() {
    companion object : KoinComponent {
        const val cmakeVersion = "3.22.1"
        const val DEX_VMP_CLANG_PATH = "android-ndk-r21b"
        const val DEX_VMP_CLI = "dev_vmp_cli_1.0.27"
    }

    val buildParams: BuildParams by inject()
    val pipelineContext: PipelineContextImpl by inject()
    val workspaceParams: WorkspaceParams by inject()
    val androidHome = getenvSafe("ANDROID_HOME")
    val cmakePath = File(listOf(androidHome, "cmake", cmakeVersion).joinToString(File.separator))
    val buildTools35 =
        File(listOf(androidHome, "build-tools", "35.0.0").joinToString(File.separator))
    val zipalign = File(buildTools35, "zipalign")

    val secNdkPath = if (SystemUtils.isMac()) {
        val envNdkPath = getenvSafe("ANDROID_NDK_R21B_SEC")
        if (envNdkPath == null || !(File(envNdkPath).exists())) {
            File(workspaceParams.workspace, DEX_VMP_CLANG_PATH)
        } else {
            File(envNdkPath)
        }
    } else {
        if (File("/opt/ndk/android-ndk-r21b-sec").exists()) {
            File("/opt/ndk/android-ndk-r21b-sec")
        } else {
            // 下载逻辑
            File(workspaceParams.workspace, DEX_VMP_CLANG_PATH)
        }
    }


    override suspend fun run() {
        if (buildParams.isEnable("OPEN_DEX_VMP", false)) {
            downloadDevVMPEnv()
            val mappingMethodFile = createTempFile(prefix = "mapping_annotation", suffix = ".txt")
            mappingMethodFile.writeText(
                """
                class \${'$'}USE_ANNOTATIONS\$ {
                    \${'$'}USE_ANNOTATIONS\$;
                }
            """.trimIndent()
            )
            val apkJob = PipelineOutput.buildApkPaths.map(::File).map { file ->
                if (!file.exists()) {
                    if (!buildParams.withAab) {
                        throw PipelineThrowable(ErrorType.ApkNotFound)
                    } else {
                        null
                    }
                } else {
                    CoroutineScope(Dispatchers.IO).async {
                        val apkTemp = File(PipelineOutput.buildApkRootPath, "dex_vpm.apk")
                        startDevVMP(mappingMethodFile.toFile(), file, apkTemp)
                        apkTemp.copyTo(file, overwrite = true)
                    }
                }
            }
            val aabJob =
                if (buildParams.withAab) {
                    val aabFile = File(PipelineOutput.buildBundlePath)
                    if (!aabFile.exists()) {
                        throw PipelineThrowable(ErrorType.AabNotFound)
                    }
                    CoroutineScope(Dispatchers.IO).async {
                        val srcFile = File(PipelineOutput.buildBundlePath)

                        val aabTemp = File(PipelineOutput.buildBundleRootPath, "dex_vpm.aab")
                        startDevVMP(
                            mappingMethodFile.toFile(),
                            srcFile,
                            aabTemp,
                            true
                        )
                        aabTemp.copyTo(srcFile, overwrite = true)
                    }
                } else {
                    null
                }
            listOfNotNull(aabJob, *apkJob.toTypedArray()).awaitAll()
        }
    }

    private suspend fun startDevVMP(
        mappingAnnotationFile: File,
        src: File,
        dest: File,
        isAAB: Boolean = false,
    ) {
        val dexVmpWorkspace = createTempDirectory("dev_vmp_workspace")
        logger().info("dexVmpWorkspace: $dexVmpWorkspace")
        val srcTemp = createTempFile(directory = dexVmpWorkspace, suffix = ".${src.extension}")
        src.copyTo(srcTemp.toFile(), overwrite = true)
        val dexOutput =
            File(
                srcTemp.toFile().parentFile,
                "build/${srcTemp.nameWithoutExtension}-protect.${srcTemp.extension}"
            )
        retry(3, 1000) { count ->
            logger().info("Start Reinforcement: $count")
            Command(File(workspaceParams.workspace, DEX_VMP_CLI).absolutePath).directory(
                dexVmpWorkspace.toFile()
            )
                .env("ANDROID_NDK_HOME", secNdkPath.absolutePath)
                .env("CMAKE_PATH", cmakePath.absolutePath)
                .args(
                    "${srcTemp.toFile().absolutePath}",
                    "${mappingAnnotationFile.absolutePath}"
                ).apply {
                    if (isAAB) {
                        args("-aab", "-export")
                    }
                }.default().spawn().wait()

        }
        if (!dexOutput.exists()) {
            throw PipelineThrowable(ErrorType.DexVpmError)
        }
        if (!isAAB) {
            val alignTemp = File(
                dexVmpWorkspace.toFile(),
                "${dexOutput.nameWithoutExtension}-align.${dexOutput.extension}"
            )
            Command(zipalign.absolutePath)
                .directory(dexVmpWorkspace.toFile())
                .args("-v", "4", dexOutput.absolutePath, alignTemp.absolutePath)
                .default()
                .spawn()
                .wait()
            alignTemp.copyTo(dest, overwrite = true)
        } else {
            dexOutput.copyTo(dest, overwrite = true)
        }
        logger().info("Reinforcement Success")


    }

    suspend fun downloadSecNdk() {
        if (!secNdkPath.exists()) {
            logger().info("Downloading dev_vmp_clang")
            val devVmpClangTemp = if (SystemUtils.isMac()) {
                createTempFile(prefix = "dev_vmp_clang", suffix = ".zip")
            } else {
                createTempFile(prefix = "dev_vmp_clang", suffix = ".tar.gz")
            }
            val downloadUrl = if (pipelineContext.isTTP) {
                "https://bits-api.tiktok-sce.org/api/open/tos/program-protection/security_compile_Linux_ndk/ByteGuard_NDK21_Linux_21-BG2.7.7-tmp16_full.tar.gz"
            } else {
                if (SystemUtils.isMac()) {
                    "https://${BuildConfig.hide_voffline_rul}/download/tos/schedule/program-protection/android/repository/security_compile_Darwin_ndk/ByteGuard_NDK21_Darwin_21-BG2.7.7-tmp15_full.zip"
                } else {
                    "https://${BuildConfig.hide_voffline_rul}/download/tos/schedule/program-protection/security_compile_Linux_ndk/ByteGuard_NDK21_Linux_21-BG2.7.7_full.tar.gz"
                }
            }
            val downloadResult =
                request(INetworkApi::download, downloadUrl)
            downloadResult.byteStream().use { input ->
                devVmpClangTemp.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
            logger().info("Downloading dev_vmp_clang success")
            Compression.decompress(devVmpClangTemp.toFile(), secNdkPath.parentFile)
            logger().info("decompress dev_vmp_clang success")
        } else {
            logger().info("sec ndk exist[${secNdkPath.absolutePath}]")
        }
    }


    suspend fun downloadDevVMPEnv() {
        downloadSecNdk()
        if (!SystemUtils.isMac()) {
            File(
                secNdkPath,
                "toolchains/llvm/prebuilt/linux-x86_64/bin/UUID"
            ).writeText("2e6f8c7f-d303-4e64-b46c-27a0ee72912b")
        }
        val dexVMPCli = File(workspaceParams.workspace, DEX_VMP_CLI)
        if (!dexVMPCli.exists()) {
            logger().info("Downloading dev_vmp_cli.jar")
            val downloadResult =
                request(
                    INetworkApi::download, if (pipelineContext.isTTP) {
                        "https://bits-api.tiktok-sce.org/api/open/tos/program-protection/Tmp/dexVmp-1.0.27.jar"
                    } else {
                        "https://${BuildConfig.hide_tos_url}/obj/safe_protection_map/dexVmp-1.0.27.jar"
                    }
                )
            FileOutputStream(dexVMPCli).use { stream ->
                ResourceUtils.readResourceStream("launch.sh")?.copyTo(stream)
                    ?: throw Exception("No launcher script found")
                stream.write(System.lineSeparator().encodeToByteArray())
                downloadResult.byteStream().use { input ->
                    input.copyTo(stream)
                }
            }
            Files.setPosixFilePermissions(
                dexVMPCli.toPath(), setOf(
                    PosixFilePermission.OWNER_EXECUTE,
                    PosixFilePermission.OWNER_READ,
                    PosixFilePermission.OWNER_WRITE,
                    PosixFilePermission.GROUP_READ,
                    PosixFilePermission.GROUP_EXECUTE,
                    PosixFilePermission.OTHERS_READ,
                    PosixFilePermission.OTHERS_EXECUTE
                )
            )
        }

        if (!cmakePath.exists()) {
            Command("sdkmanager").args("cmake;${cmakeVersion}").default().spawn().wait()
        }
        if (!buildTools35.exists()) {
            Command("sdkmanager").args("build-tools;35.0.0").default().spawn().wait()
        }
    }

}



