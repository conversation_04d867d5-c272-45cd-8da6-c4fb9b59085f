package com.vega.builder.pipeline.task.conflict

import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.io.File

/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/21
 */
class I18nProcessorTest: BaseProcessorTest() {

    override val filePath: String
        get() = "/Users/<USER>/Work/AndroidProject/vega_project1/vega/CapCut/src/oversea/res/values-ar/do_not_modify_strings.xml"

    override var fileContent = """
            <<<<<<< HEAD
                <string name="m10n_newIncentive_disclaimerDetail">من خلال المتابعة، فإنك توافق على الشروط والأحكام {Terms_and_Conditions} الخاصة بنظام الطاقة وشروط خدمة CapCut {CapCut_Terms_of_Service} وسياسة خصوصيتها {CapCut_Privacy_Policy}.</string>
                <string name="m10n_newIncentive_exportToast"> لم يتم استخدام طاقتك البالغة %d. حدث خطأ ما. حاول مرة أخرى لاحقًا.</string>
                <string name="m10n_newIncentive_exportYpopup_sub">استخدم {num} من الطاقة للتصدير وكلها لك تمامًا!</string>
                <string name="m10n_newIncentive_exportYpopup_subD">استخدم %d من الطاقة للتصدير وكلها لك تمامَا!</string>
                <string name="m10n_newIncentive_exportYpopup_title">الطاقة جاهزة. لنصدّر!</string>
                <string name="m10n_newIncentive_exportpopup_btn">للوصول إلى {num}</string>
                <string name="m10n_newIncentive_exportpopup_btn1">شاهد الإعلان للحصول على الطاقة</string>
                <string name="m10n_newIncentive_exportpopup_btn2">الانضمام إلى العضوية</string>
                <string name="m10n_newIncentive_exportpopup_sub">أوشكت على التصدير! احصل على مزيد من الطاقة للمتابعة.</string>
                <string name="m10n_newIncentive_exportpopup_title">يلزمك قليل من الطاقة</string>
                <string name="m10n_newIncentive_popup_btn">هيّا لنفعلها ({sec} من الثواني)</string>
            =======
                <string name="m10n_newIncentive_disclaimerDetail">من خلال المتابعة، أنت توافق على {Terms_and_Conditions} الخاصة بنظام الطاقة و{CapCut_Terms_of_Service} و{CapCut_Privacy_Policy}.</string>
                <string name="m10n_newIncentive_exportToast"> لم يتم استخدام طاقتك البالغة %d. حدث خطأ ما. حاول مرة أخرى لاحقًا.</string>
                <string name="m10n_newIncentive_exportYpopup_sub">استخدم {num} من الطاقة للتصدير ويمكنك الحصول عليه!</string>
                <string name="m10n_newIncentive_exportYpopup_subD">استخدم %d من الطاقة للتصدير ويمكنك الحصول عليه!</string>
                <string name="m10n_newIncentive_exportYpopup_title">الطاقة جاهزة. لنصدّر!</string>
                <string name="m10n_newIncentive_exportpopup_btn">للوصول إلى {num}</string>
                <string name="m10n_newIncentive_exportpopup_btn1">شاهد الإعلان للحصول على الطاقة</string>
                <string name="m10n_newIncentive_exportpopup_btn2">انضم إلى العضوية</string>
                <string name="m10n_newIncentive_exportpopup_sub">أوشكت أن تصدّر! احصل على القليل من الطاقة الإضافية للمتابعة.</string>
                <string name="m10n_newIncentive_exportpopup_title">يلزمك القليل من الطاقة الإضافية</string>
                <string name="m10n_newIncentive_popup_btn">لنفعلها ({sec} من الثواني)</string>
            >>>>>>> 73a8507d33b9c811c0a0f7ac6bbcbfb7aaffdbc8
        """.trimIndent()

    override val resolveConflict = """
        <string name="m10n_newIncentive_disclaimerDetail">من خلال المتابعة، فإنك توافق على الشروط والأحكام {Terms_and_Conditions} الخاصة بنظام الطاقة وشروط خدمة CapCut {CapCut_Terms_of_Service} وسياسة خصوصيتها {CapCut_Privacy_Policy}.</string>
        <string name="m10n_newIncentive_exportToast"> لم يتم استخدام طاقتك البالغة %d. حدث خطأ ما. حاول مرة أخرى لاحقًا.</string>
        <string name="m10n_newIncentive_exportYpopup_sub">استخدم {num} من الطاقة للتصدير وكلها لك تمامًا!</string>
        <string name="m10n_newIncentive_exportYpopup_subD">استخدم %d من الطاقة للتصدير وكلها لك تمامَا!</string>
        <string name="m10n_newIncentive_exportYpopup_title">الطاقة جاهزة. لنصدّر!</string>
        <string name="m10n_newIncentive_exportpopup_btn">للوصول إلى {num}</string>
        <string name="m10n_newIncentive_exportpopup_btn1">شاهد الإعلان للحصول على الطاقة</string>
        <string name="m10n_newIncentive_exportpopup_btn2">الانضمام إلى العضوية</string>
        <string name="m10n_newIncentive_exportpopup_sub">أوشكت على التصدير! احصل على مزيد من الطاقة للمتابعة.</string>
        <string name="m10n_newIncentive_exportpopup_title">يلزمك قليل من الطاقة</string>
        <string name="m10n_newIncentive_popup_btn">هيّا لنفعلها ({sec} من الثواني)</string>
    """.trimIndent()

    @Test
    fun testProcessConflictBlock() {
        val mockFile = mockFile()
        val mockFileReader = mockFileReader()
        val processor = I18nProcessor(mockFile, mockFileReader)
        runBlocking {
            processor.process()
        }
        assertEquals(processor.getContent().trimIndent(), resolveConflict)
    }

    @Test
    fun extractI18nKey() {
        val content = """
            <string name="m10n_newIncentive_disclaimerDetail">من خلال المتابعة، فإنك توافق على الشروط والأحكام {Terms_and_Conditions} الخاصة بنظام الطاقة وشروط خدمة CapCut {CapCut_Terms_of_Service} وسياسة خصوصيتها {CapCut_Privacy_Policy}.</string>
        """.trimIndent()
        val key = I18nProcessor.extractI18nKey(content)
        assertEquals(key, "m10n_newIncentive_disclaimerDetail")
    }
}