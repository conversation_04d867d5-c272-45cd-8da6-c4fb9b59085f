package com.vega.builder.common.utils

import java.io.File
import java.util.LinkedList
import java.util.Locale
import java.util.regex.Pattern

abstract class OperatingSystem internal constructor() {
    val name: String = System.getProperty("os.name")
    val version: String = System.getProperty("os.version")
    private val toStringValue: String = this.name + " " + this.version + " " + System.getProperty("os.arch")

    override fun toString(): String {
        return toStringValue
    }

    open val isWindows: Boolean
        get() = false

    open val isUnix: Boolean
        get() = false

    open val isMacOsX: Boolean
        get() = false

    open val isLinux: Boolean
        get() = false

    abstract val nativePrefix: String

    abstract fun getScriptName(scriptPath: String): String

    abstract fun getExecutableName(executablePath: String): String

    abstract val executableSuffix: String

    abstract fun getSharedLibraryName(libraryName: String): String

    abstract val sharedLibrarySuffix: String

    abstract fun getStaticLibraryName(libraryName: String): String

    abstract val staticLibrarySuffix: String

    abstract val linkLibrarySuffix: String

    abstract fun getLinkLibraryName(libraryPath: String): String

    abstract val familyName: String

    /**
     * Locates the given executable in the system path. Returns null if not found.
     */
    fun findInPath(name: String): File? {
        val exeName = getExecutableName(name)
        if (exeName.contains(File.separator)) {
            val candidate = File(exeName)
            if (candidate.isFile()) {
                return candidate
            }
            return null
        }
        for (dir in this.path) {
            val candidate: File = File(dir, exeName)
            if (candidate.isFile()) {
                return candidate
            }
        }

        return null
    }

    fun findAllInPath(name: String): MutableList<File?> {
        val all: MutableList<File?> = LinkedList<File?>()

        for (dir in this.path) {
            val candidate: File = File(dir, name)
            if (candidate.isFile()) {
                all.add(candidate)
            }
        }

        return all
    }

    val path: MutableList<File?>
        get() {
            val path = System.getenv(this.pathVar)
            if (path == null) {
                return mutableListOf<File?>()
            }
            val entries: MutableList<File?> = ArrayList<File?>()
            for (entry in path.split(Pattern.quote(File.pathSeparator).toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray()) {
                entries.add(File(entry))
            }
            return entries
        }

    open val pathVar: String
        get() = "PATH"

    class Windows : OperatingSystem() {
        override val nativePrefix: String

        init {
            nativePrefix = resolveNativePrefix()
        }

        override val isWindows: Boolean = true

        override val familyName: String
            get() = "windows"


        override fun getScriptName(scriptPath: String): String {
            return withExtension(scriptPath, ".bat")

        }

        override val executableSuffix: String
            get() = ".exe"


        override fun getExecutableName(executablePath: String): String {
            return withExtension(executablePath, ".exe")
        }


        override val sharedLibrarySuffix: String
            get() = ".dll"

        override fun getSharedLibraryName(libraryPath: String): String {
            return withExtension(libraryPath, ".dll")
        }

        override val linkLibrarySuffix: String
            get() = ".lib"


        override fun getLinkLibraryName(libraryPath: String): String {
            return withExtension(libraryPath, ".lib")
        }

        override val staticLibrarySuffix: String
            get() = ".lib"

        override fun getStaticLibraryName(libraryName: String): String {
            return withExtension(libraryName, ".lib")
        }


        private fun resolveNativePrefix(): String {
            var arch = System.getProperty("os.arch")
            if ("i386" == arch) {
                arch = "x86"
            }
            return "win32-" + arch
        }

        override val pathVar: String
            get() = "Path"

    }

    open class Unix : OperatingSystem() {
        override val nativePrefix: String = resolveNativePrefix()


        override fun getScriptName(scriptPath: String): String {
            return scriptPath
        }

        override val familyName: String
            get() = "unknown"


        override val executableSuffix: String
            get() = ""

        override fun getExecutableName(executablePath: String): String {
            return executablePath
        }

        override fun getSharedLibraryName(libraryName: String): String {
            return getLibraryName(libraryName, linkLibrarySuffix)
        }

        override val sharedLibrarySuffix: String
            get() = ".so"

        override val linkLibrarySuffix: String
            get() = sharedLibrarySuffix

        private fun getLibraryName(libraryName: String, suffix: String): String {
            if (libraryName.endsWith(suffix)) {
                return libraryName
            }
            val pos = libraryName.lastIndexOf('/')
            if (pos >= 0) {
                return libraryName.substring(0, pos + 1) + "lib" + libraryName.substring(pos + 1) + suffix
            } else {
                return "lib" + libraryName + suffix
            }
        }


        override fun getLinkLibraryName(libraryPath: String): String {
            return getSharedLibraryName(libraryPath)
        }

        override val staticLibrarySuffix: String
            get() = ".a"


        override fun getStaticLibraryName(libraryName: String): String {
            return getLibraryName(libraryName, ".a")
        }

        override val isUnix: Boolean
            get() = true


        private fun resolveNativePrefix(): String {
            val arch = this.arch
            var osPrefix = this.osPrefix
            osPrefix += "-" + arch
            return osPrefix
        }

        protected open val arch: String
            get() {
                var arch = System.getProperty("os.arch")
                if ("x86" == arch) {
                    arch = "i386"
                }
                if ("x86_64" == arch) {
                    arch = "amd64"
                }
                if ("powerpc" == arch) {
                    arch = "ppc"
                }
                return arch
            }

        protected open val osPrefix: String
            get() {
                var osPrefix = this.name.lowercase(Locale.getDefault())
                val space = osPrefix.indexOf(" ")
                if (space != -1) {
                    osPrefix = osPrefix.substring(0, space)
                }
                return osPrefix
            }
    }

    class MacOs : Unix() {
        override val isMacOsX: Boolean
            get() = true


        override val familyName: String
            get() = "os x"


        override val sharedLibrarySuffix: String
            get() = ".dylib"


        override val nativePrefix: String
            get() = "darwin"

    }

    class Linux : Unix() {
        override val isLinux: Boolean
            get() = true

        override val familyName: String
            get() = "linux"
    }

    class FreeBSD : Unix()

    class Solaris : Unix() {

        override val familyName: String
            get() = "solaris"

        override val osPrefix: String
            get() = "sunos"


        override val arch: String
            get() {
                val arch = System.getProperty("os.arch")
                if (arch == "i386" || arch == "x86") {
                    return "x86"
                }
                return super.arch
            }

    }

    companion object {


        val WINDOWS: Windows = Windows()
        val MAC_OS: MacOs = MacOs()
        val SOLARIS: Solaris = Solaris()
        val LINUX: Linux = Linux()
        val FREE_BSD: FreeBSD = FreeBSD()
        val UNIX: Unix = Unix()
        val currentOs: OperatingSystem by lazy { forName(System.getProperty("os.name")) }
        fun current(): OperatingSystem {
            return currentOs
        }

        // for testing current()

        fun forName(os: String): OperatingSystem {
            val osName = os.lowercase(Locale.getDefault())
            if (osName.contains("windows")) {
                return WINDOWS
            } else if (osName.contains("mac os x") || osName.contains("darwin") || osName.contains("osx")) {
                return MAC_OS
            } else if (osName.contains("sunos") || osName.contains("solaris")) {
                return SOLARIS
            } else if (osName.contains("linux")) {
                return LINUX
            } else if (osName.contains("freebsd")) {
                return FREE_BSD
            } else {
                // Not strictly true
                return UNIX
            }
        }


    }
}