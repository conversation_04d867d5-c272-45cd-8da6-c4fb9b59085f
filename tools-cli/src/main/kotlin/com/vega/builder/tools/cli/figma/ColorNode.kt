package com.vega.builder.tools.cli.figma


// 颜色节点密封类（核心结构）
sealed class ColorNode(val name: String) {
    abstract fun toCode(rName: String? = null): String
}

// 叶子节点（最终颜色）
class ColorLeaf(
    name: String,
    val fullPath: String,
    val colorHex: Color,
    val fromBaseLine: Boolean = false,
) : ColorNode(name) {
    override fun toCode(rName: String?): String {
        return "Color(hex.toLong(16))"
    }
}

// 中间节点（颜色分组）
class ColorGroup(name: String) : ColorNode(name) {
    val children = mutableMapOf<String, ColorNode>()

    fun addChild(name: String, node: ColorNode) {
        children[name] = node
    }

    operator fun get(name: String): ColorNode? = children[name]

    override fun toCode(rName: String?): String {
        return children.entries.joinToString(
            prefix = "@Immutable\nclass ${rName ?: name}(\n",
            separator = ",\n",
            postfix = "\n)\n"
        ) { (k, v) ->
            "    val ${k.replaceFirstChar { it.toLowerCase() }} : ${
                if (v is ColorLeaf) {
                    "Color"
                } else {
                    k
                }
            }"
        }
    }

}