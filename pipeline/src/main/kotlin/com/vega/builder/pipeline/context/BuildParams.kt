package com.vega.builder.pipeline.context

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.vega.builder.common.airplane.IAirplaneApi
import com.vega.builder.common.logger.formatGreen
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.request
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.utils.ResourceUtils
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.getenvSafe
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.io.File

/**
 * extra uses pipelineParamsKey to filter when assigning values to environment variables, and does not read all environment variables.
 */
class BuildParams : Map<String, String>, KoinComponent {
    private val extra = mutableMapOf<String, String>()
    private val workspaceParams: WorkspaceParams by inject()
    private val buildContext: PipelineContextImpl by inject()

    private var init: Boolean = false
    override val entries: Set<Map.Entry<String, String>>
        get() {
            checkParamsInit()
            return extra.entries
        }
    override val keys: Set<String>
        get() {
            checkParamsInit()
            return extra.keys
        }
    override val size: Int
        get() {
            checkParamsInit()
            return extra.size
        }
    override val values: Collection<String>
        get() {
            checkParamsInit()
            return extra.values
        }

    override fun containsKey(key: String): Boolean {
        checkParamsInit()
        return extra.containsKey(key)

    }

    override fun containsValue(value: String): Boolean {
        checkParamsInit()
        return extra.containsValue(value)
    }

    override operator fun get(key: String): String? {
        checkParamsInit()
        return extra[key]
    }

    override fun isEmpty(): Boolean {
        checkParamsInit()
        return extra.isEmpty()
    }

    fun isEnable(
        key: String,
        default: Boolean,
    ): Boolean {
        checkParamsInit()
        val value = extra[key]
        return if (value != null && listOf("true", "false").contains(value)) {
            value.toBoolean()
        } else {
            default
        }
    }

    private fun checkParamsInit() {
        if (!init) {
            throw PipelineThrowable(ErrorType.InnerError)
        }
    }

    /**
     * 参数来源存在多个，下面优先级从低到高
     * 1. 环境变量
     * 2. 环境变量中的BUILD_PARAMS
     * 3. 模版
     * 4. 组件平台触发的Job使用，ext_params可能的参数为
     *     1. 模版名称
     *     2. 直接定义的Json格式的数据
     *     3. ext_params中定义的ext_template的模版名称
     */
    suspend fun loadParams() {
        // 环境变量读取，模版设置的一些内容
        loadFromEnvironmentParams()
        // 环境变量中的BUILD_PARAMS读取定义的一些数据
        loadFromBuildParams()
        // 模版读取
        loadFromDefaultTemplate()
        // 组件平台触发的Job使用，extra可能的参数为
        // 1. 模版名称
        // 2. 直接定义的Json格式的数据
        loadExtraParams()

        printParams("Final Build params:", extra)

        init = true
    }

    fun loadFromEnvironmentParams() {
        printParams("Read Build env:", getenvSafe().filterValues { it.isNotBlank() })
        extra.putAll(getenvSafe().filterValues { it.isNotBlank() }
            .filterKeys { it in pipelineParamsKey }) // first read from env
        printParams("read Build params from env:", extra)
    }

    fun loadFromBuildParams() {
        val extraBuildParams = getenvSafe("BUILD_PARAMS", "")
        if (extraBuildParams.isNotBlank()) {
            Gson().fromJson<Map<String, String>>(extraBuildParams, object : TypeToken<Map<String, String>>() {}.type)
                .apply {
                    extra.putAll(this)
                    printParams("read Build params from BUILD_PARAMS", this)
                }
        }
    }

    suspend fun loadFromDefaultTemplate() {
        val templateName = getenvSafe("DEFAULT_TEMPLATE", "")
        if (templateName.isNotBlank() && !buildContext.isTTP) {
            val result = request(IAirplaneApi::queryTemplate, templateName)
            if (result.code != 0 || result.data != null) {
                printParams("read Build params from Paper Airplane", result.data ?: emptyMap())
                extra.putAll(result.data ?: emptyMap())
            } else {
                throw PipelineThrowable(ErrorType.LoadBuildTemplateError)
            }
        } else if (templateName.isNotBlank() && buildContext.isTTP) {
            val buildTemplate = loadTemplateInResource(templateName)
            printParams("read Build params from CC Repo TTP Config", buildTemplate)
            extra.putAll(buildTemplate.mapValues { it.value.toString() })
        }
    }

    fun loadTemplateInResource(templateName: String): Map<String, Any> {
        if (templateName.isNotBlank()) {
            runCatching {
                val buildTemplate = Gson().fromJson<Map<String, Any>>(
                    ResourceUtils.readResource(listOf("template", "${templateName}.json").joinToString(File.separator))
                )
                return buildTemplate
            }
        }
        return emptyMap()
    }

    /**
     * 读取ext_params 作为环境变量
     * 1. 如果ext_params是一个模版名称，那么读取模版内容
     * 2. 如果ext_params是一个Json格式的字符串，那么直接读取Json内容
     * 3. 如果ext_params存在custom_build_params，则设置custom_build_params
     * 3. 如果ext_params存在ext_template，则先设置ext_params，之后设置ext_template
     */
    fun loadExtraParams() {
        val extParamsContent = getenvSafe("ext_params", "")
        if (extParamsContent.isBlank()) {
            return
        }
        if (!extParamsContent.startsWith("{")) {
            val buildTemplate = loadTemplateInResource(extParamsContent)
            if (buildTemplate.isNotEmpty()) {
                printParams("Read Build params select by ext_params", buildTemplate)
                extra.putAll(buildTemplate.mapValues { it.value.toString() })
            }
        }
        if (extParamsContent.isNotBlank()) {

            runCatching {
                val extraParams = Gson().fromJson<Map<String, Any>>(
                    extParamsContent
                )
                printParams("Read Build params select by ext_params", extraParams)
                extra.putAll(extraParams.mapValues { it.value.toString() })
                if (extraParams.containsKey("ext_template")) {
                    val extTemplateName = extraParams["ext_template"]?.toString() ?: ""
                    val buildTemplate = loadTemplateInResource(extTemplateName)
                    printParams("Read Build params select by ext_template in ext_params", buildTemplate)
                    extra.putAll(buildTemplate.mapValues { it.value.toString() })
                }
            }
        }
    }

    /**
     * Check Remove flavor
     */
    val removeFlavor: Boolean by lazy {
        File("${workspaceParams.targetProjectDir}${File.separator}noflavor").exists()
    }
}

val BuildParams.isDebug: Boolean
    get() = this["IS_DEBUG"]?.toBoolean() == true

/**
 * Build Target
 */
val BuildParams.buildTarget: TargetInfo
    get() {
        val isOversea = isEnable("IS_OVERSEA", false)
        val value = this["BUILD_TARGET"] ?: "lv"
        val target = TargetInfo.valueOf(value, isOversea)
        if (removeFlavor) {
            target.flavor = ""
        }
        return target
    }

/**
 * Is Coverage build
 */
val BuildParams.isCoverage
    get() = isEnable("IS_COVERAGE", false)


val BuildParams.channel
    get() = this["CHANNEL"]


val BuildParams.isChannelPackage
    get() = isEnable("IS_CHANNEL", false)

val BuildParams.withAab
    get() = isEnable("WITH_AAB_BUILD", false)

val BuildParams.isOutBuild
    get() = isEnable("IS_OUT_BUILD", false)

val BuildParams.version: String
    get() {
        return if (buildTarget != TargetInfo.Dreamina && buildTarget != TargetInfo.Commercepro) {
            this["APP_VERSION_CODE"] ?: if (buildTarget.isOversea) {
                this["OVERSEA_APP_VERSION_CODE_DEFAULT"]
            } else {
                this["APP_VERSION_CODE_DEFAULT"]
            } ?: ""
        } else {
            ""
        }
    }

/**
 * gradle‘debug log mode
 */
val BuildParams.isGradleLoggerDebugMode
    get() = isEnable("IS_GRADLE_LOGGER_DEBUG_MODE", false)


private fun BuildParams.printParams(
    msg: String,
    params: Map<String, Any>,
) {
    if (params.isNotEmpty()) {
        logger().info(msg.formatGreen())
        val maxKeyLength = params.keys.maxOfOrNull { it.length }
        for (entry in params.entries) {
            logger().info("${entry.key.padEnd(maxKeyLength ?: entry.key.length, ' ')}: ${entry.value}")
        }
    } else {
        logger().debug("Can't $msg")
    }
}

/**
 * Use for filter envs values
 */
val pipelineParamsKey
    get() =
        listOf(
            "IS_BUILD_64",
            "IS_OVERSEA",
            "IS_DEBUG",
            "APP_VERSION_NAME",
            "APP_VERSION_CODE",
            "BUILD_TARGET",
            "BUILD_ID",
            "DEFAULT_TEMPLATE",
            "BUILD_PARAMS",
//            "pipeline_running_env",// test
            "upload_build_dir_regex",//in mr params,use for upload any build result
            "ext_params",//in publish params

            /**
             * MR组件发布专用参数
             */
            "PUBLISH_PARAMS",
            "WORK_DIR",
        )
