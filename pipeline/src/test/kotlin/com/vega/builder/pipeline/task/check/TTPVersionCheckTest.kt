package com.vega.builder.pipeline.task.check

import com.vega.builder.common.utils.setenvMock
import com.vega.builder.pipeline.main
import org.junit.jupiter.api.Test

class TTPVersionCheckTest {
    fun mockEnv() {
        setenvMock("SYSTEM_MOCK", "true")
        setenvMock("WORKSPACE", System.getProperty("user.home"))
        setenvMock("MAIN_GIT_URL", "******************:faceu-android/cc.git")
        setenvMock("MAIN_GIT_BRANCH", "rc/develop")
        setenvMock("MAIN_GIT_COMMIT", "")
        setenvMock("MAIN_CODE_TARGERT_PATH", "")
        setenvMock(
            "BUILD_PARAMS", """
            {
                "RUNNING_TYPE": "check_ttp_version"
            }
        """.trimIndent()
        )
    }
    @Test
    fun testBuildTask() {
        mockEnv()
        main()
    }
}