rootProject.name = "build-cli"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")
pluginManagement {
    includeBuild("build-logic")

    repositories {

        maven("https://maven.byted.org/repository/android_public/")
        maven("https://maven.byted.org/repository/releases")
        maven("https://maven.byted.org/repository/snapshots")
    }
}

dependencyResolutionManagement {

    repositories {
        maven("https://maven.byted.org/repository/android_public/")
        maven("https://maven.byted.org/repository/releases")
        maven("https://maven.byted.org/repository/snapshots")
        mavenLocal()
    }
}

include(":pipeline")
include(":common")
include(":source-upload-cli")
include(":launcher")
include(":tools-cli")
include(":kotlin-psi")
// include(":script")
