plugins {
    // this is necessary to avoid the plugins to be loaded multiple times
    // in each subproject's classloader
    alias(libs.plugins.kotlin.jvm) apply false
    alias(libs.plugins.kotlin.ksp) apply false
    alias(libs.plugins.kotlinx.serialization) apply false
}

buildscript {
    dependencies {
        classpath(libs.tos.core)
        classpath(libs.jgit)
        classpath(libs.bundles.retrofit)
        classpath(libs.kotlinx.coroutines)
        classpath("com.guardsquare:proguard-gradle:7.4.2")
        classpath("com.gradleup.shadow:shadow-gradle-plugin:9.0.0-beta17")
    }
}
