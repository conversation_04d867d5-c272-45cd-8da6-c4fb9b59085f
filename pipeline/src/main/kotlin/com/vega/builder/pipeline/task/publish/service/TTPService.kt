package com.vega.builder.pipeline.task.publish.service

import com.vega.builder.common.logger.formatGreen
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.IBuildServiceApi
import com.vega.builder.common.network.api.IBuildServiceApi.BaseResponse
import com.vega.builder.common.network.api.TTPJobHistory
import com.vega.builder.common.network.api.TTPJobInfo
import com.vega.builder.common.network.api.TTPJobTriggerReq
import com.vega.builder.common.network.request
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.utils.PollingResult
import com.vega.builder.common.utils.polling

object TTPService {

    suspend fun checkJobStatus(repoId: String, version: String) {
        val result = polling(initialDelay = 20, interval = 60) {
            val stateResult = request<IBuildServiceApi, BaseResponse<String>> {
                ttpJobBuildResult(repoId = repoId, version = version)
            }
            if (stateResult.isSuccess()) {
                when (stateResult.data) {
                    "success" -> {
                        PollingResult.Success(Unit)
                    }

                    "failed" -> {
                        logger().error("TTP job failed: ${stateResult.data}")
                        PollingResult.Failure(PipelineThrowable(ErrorType.TtpJobFailed))
                    }

                    else -> {
                        logger().info("Wait for job: $repoId-$version")
                        PollingResult.Waiting
                    }
                }
            } else {
                PollingResult.Failure(PipelineThrowable(ErrorType.TtpJobFailed))
            }
        }
        if (result is PollingResult.Failure) {
            throw result.reason
        }
        logger().info("ttp job success")
    }

    suspend fun searchTTPJobInfo(repoId: String, version: String): TTPJobHistory? {
        val result = request(IBuildServiceApi::ttpJobBuildInfo, repoId, version)
        return if (result.isSuccess() && result.data != null) {
            result.data
        } else {
            null
        }
    }


    /**
     * V2接口，支持触发TTP 二进制在服务端发布
     */
    suspend fun triggerJob(
        req: TTPJobTriggerReq,
        skipCheckResult: Map<Int, Boolean>
    ) {
        val result = request(
            IBuildServiceApi::ttpJobBuildTrigger, req
        )
        if (result.isSuccess()) {
            logger().info("Trigger TTP job success")
            logger().info("Jump to view detail[https://bits.bytedance.net/devops/1486903298/ttp/component/event?event_id=${result.data}]".formatGreen())
            for (entry in skipCheckResult) {
                if (!entry.value) {
                    checkJobStatus(repoId = entry.key.toString(), version = req.baseInfo.version)
                }
            }
        } else {
            logger().error("ttp job failed:，${result.msg}")
            throw PipelineThrowable(ErrorType.TtpJobFailed)
        }
    }
}