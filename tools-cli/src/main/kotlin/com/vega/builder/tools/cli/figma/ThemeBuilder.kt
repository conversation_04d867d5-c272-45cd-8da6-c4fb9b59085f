package com.vega.builder.tools.cli.figma

import com.google.gson.Gson
import java.io.File
import com.vega.builder.common.utils.fromJson
import kotlin.text.removePrefix

// Theme builder class
class ThemeBuilder(
    val colorSchemaName: String,
    val baselineSchemaName: String,
    val themePackage: String,
) {
    private val themeRoots = mutableMapOf<String, ColorGroup>()
    private val colorSchemaClass = StringBuilder()
    private val colorSchema = StringBuilder()
    private val colorValue = StringBuilder()

    fun processTheme(colorFile: String): ThemeBuilder {
        val colorFile = File(colorFile)
        val themeMap = Gson().fromJson<Map<String, MutableList<Common>>>(colorFile.readText())
        if (!themeMap.containsKey(baselineSchemaName)) {
            throw IllegalArgumentException("Baseline theme not found in the map.")
        }
        val baseline = themeMap[baselineSchemaName]!!
        themeMap.forEach { (themeName, commons) ->
            if (themeName != baselineSchemaName) {
                val (added, removed) = findSetDifferences(
                    baseline.map { it.variableName },
                    commons.map { it.variableName })
                if (added.isNotEmpty() || removed.isNotEmpty()) {
                    println("Theme $themeName:")
                    if (added.isNotEmpty()) {
                        println("  Following values will use baseline variables, please note: \n${added.joinToString("\n")}")
                        commons.addAll(baseline.filter { it.variableName in added }
                            .map { it.copy(fromBaseLine = true) })
                    }
                    if (removed.isNotEmpty()) {
                        println("  Following values are missing in declaration, please note: \n${removed.joinToString("\n")}")
                        commons.removeAll { it.variableName in removed }
                    }
                }
            }
        }
        // Build themes
        themeMap.forEach { (themeName, commons) ->
            val root = ColorGroup(themeName)
            commons.forEach { common ->
                processPath(
                    groupName = themeName,
                    root = root,
                    path = parseVariableName(common.variableName),
                    color = common.colorOrigin,
                    fromBaseLine = common.fromBaseLine
                )
            }
            themeRoots[themeName] = root
        }
        return this
    }

    fun findSetDifferences(baseline: List<String>, other: List<String>): Pair<Set<String>, Set<String>> {
        val baselineSet = baseline.toSet()
        val otherSet = other.toSet()
        return (baselineSet - otherSet) to (otherSet - baselineSet)
    }

    private fun parseVariableName(variableName: String): List<String> {
        return processVariableSegments(variableName)
            .filter { it.isNotEmpty() }
    }

    fun processVariableSegments(input: String): List<String> {
        val processedSegments = input.removePrefix("Color").replace("Transparency/", "Transparency").split('/')
            .map { segment ->
                segment.replace(Regex("(\\d+)%")) {
                    "p${it.groupValues[1]}"
                }.split(Regex("[^A-Za-z0-9]"))
                    .joinToString("") { it.replaceFirstChar { it.uppercase() } }
            }

        val merged = mutableListOf<String>()
        processedSegments.forEach { segment ->
            if (segment.matches(Regex("^\\d+$")) && merged.isNotEmpty()) {
                merged[merged.lastIndex] += segment
            } else {
                merged.add(segment)
            }
        }

        return merged
    }

    private fun processPath(
        root: ColorGroup,
        path: List<String>,
        color: Color,
        groupName: String,
        fromBaseLine: Boolean
    ) {
        var current = root
        path.forEachIndexed { index, part ->
            if (index == path.lastIndex) {
                current.addChild(
                    part,
                    ColorLeaf(part, (listOf(groupName) + path).joinToString("_").toUpperCase(), color, fromBaseLine)
                )
            } else {
                val next = current[part] as? ColorGroup ?: ColorGroup(part).also {
                    current.addChild(part, it)
                }
                current = next
            }
        }
    }

    fun genComposeColorSchemeCode(targetPath: String) {
        val group = themeRoots[baselineSchemaName]
        val file = File(targetPath)
        if (!file.parentFile.exists()) {
            file.parentFile.mkdirs()
        }
        group?.let {
            genColorSchemaClass(group)
            println("Generating types based on Baseline: $baselineSchemaName")
        }
        themeRoots.forEach {
            genColorSchemaAndValue(it.value)
        }
        file.writeText(
            """
            package $themePackage

            import androidx.compose.ui.graphics.Color
            import androidx.compose.runtime.Immutable

        """.trimIndent()
        )
        file.appendText(colorSchemaClass.toString())
        file.appendText(colorValue.toString())
        file.appendText(colorSchema.toString())
    }

    private fun genColorSchemaClass(group: ColorGroup, depth: Int = 0) {
        if (depth == 0) {
            colorSchemaClass.append(group.toCode(colorSchemaName))
        } else {
            colorSchemaClass.append(group.toCode())
        }
        group.children.forEach { (name, node) ->
            if (node is ColorGroup) {
                genColorSchemaClass(node, depth + 1)
            }
        }
    }

    private fun genColorSchemaAndValue(node: ColorNode, depth: Int = 0) {
        if (depth == 0) {
            colorSchema.append("\nval ${node.name.replaceFirstChar { it.lowercase() }}:$colorSchemaName \n")
            colorSchema.append("    get() = ")
        }
        if (node is ColorGroup) {
            if (depth == 0) {
                colorSchema.append(colorSchemaName)
            } else {
                colorSchema.append(node.name)
            }
            colorSchema.append("(\n")
            node.children.forEach { (name, node) ->
                colorSchema.append((0..depth).joinToString("") { "    " })
                colorSchema.append(name.replaceFirstChar { it.toLowerCase() })
                colorSchema.append(" = ")
                genColorSchemaAndValue(node, depth + 1)
            }
            colorSchema.append((0..depth).joinToString("") { "    " })
            colorSchema.append(")")
            if (depth != 0) {
                colorSchema.append(",\n")
            }
        } else if (node is ColorLeaf) {
            val text =
                if (node.fromBaseLine) {
                    "// This color comes from Baseline theme"
                } else {
                    ""
                }
            colorSchema.append(node.fullPath)
            colorSchema.append("${text},\n")
            colorValue.appendLine("val ${node.fullPath}:Color = ${node.colorHex} $text")
        }
    }
}
