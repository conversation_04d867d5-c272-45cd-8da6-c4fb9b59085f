package com.vega.builder.shell.utils.tos

import com.bytedance.storage.tos.Tos
import com.bytedance.storage.tos.TosClient
import com.bytedance.storage.tos.TosProperty
import com.bytedance.storage.tos.auth.BucketAccessKeyCredentials
import com.vega.builder.shell.utils.kommand.process.Command
import com.vega.builder.shell.utils.tos.ITosClient.IGetObjectResult
import com.vega.builder.shell.utils.tos.ITosClient.IPutObjectResult
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.suspendCancellableCoroutine
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.Response
import java.io.File
import java.io.IOException
import java.io.InputStream
import java.util.Properties
import kotlin.coroutines.resume
import kotlin.io.path.createTempDirectory
import kotlin.io.path.createTempFile
import kotlin.io.path.exists
import kotlin.io.path.inputStream
import kotlin.io.path.outputStream
import kotlin.io.path.pathString

interface ITosClient {
    fun getObject(key: String): IGetObjectResult
    fun putObject(key: String, file: File): IPutObjectResult

    fun putObject(key: String, inputStream: InputStream): IPutObjectResult


    class IPutObjectResult(
        val success: Boolean,
        val md5: String?
    )

    class IGetObjectResult(
        val success: Boolean,
        val contentStream: InputStream?
    )

}

class TTPTosClient(val property: Properties) : ITosClient {
    companion object {
        const val TOS_BASE_URL = "https://bits-api.tiktok-sce.org/api/open/tos/ttclient-android/cicd/"
    }

    override fun getObject(key: String): IGetObjectResult {
        val tempDir = createTempDirectory()
        val tempFile = createTempFile(tempDir, "${key.replace("/","_")}.${System.currentTimeMillis()}", ".tmp")
        val result =
            Command("curl").args("-v").arg("$TOS_BASE_URL$key")
                .args("--output", tempFile.pathString)
                .default()
                .spawn()
                .waitWithOutput()
        println(result.stdout)
        println(result.stderr)
        println("Get result: $result")
        return if (result.status == 0 && tempFile.exists()) {
            IGetObjectResult(true, tempFile.inputStream())
        } else {
            IGetObjectResult(false, null)
        }
    }

    override fun putObject(
        key: String,
        file: File
    ): IPutObjectResult {
        if (!file.exists()) {
            println("put result: $file not exists")
            return IPutObjectResult(false, null)
        }

        val client = OkHttpClient.Builder()
            .followRedirects(true) // 等效于 curl 的 --location
            .build()

        // 2. 构建 Multipart 请求体
        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart(
                "file",
                file.name,
                file.asRequestBody("multipart/form-data".toMediaType())
            )
            .build()
        // 3. 构建 PUT 请求
        val request = Request.Builder()
            .url("$TOS_BASE_URL$key")
            .put(requestBody)
            .header("Content-Type", "multipart/form-data")
            .build()

        val response = try {
            val response = client.newCall(request).execute()
            println("Response Code: ${response.code}")
            println("Response Body: ${response.body?.string()}")
            response
        } catch (e: IOException) {
            println("Request failed: ${e.message}")
            throw e
        }
        return if (response.code >= 200 && response.code < 300) {
            IPutObjectResult(true, null)
        } else {
            IPutObjectResult(false, null)
        }
//        println("put result: ${file.length()} bytes")
//        val result = Command("curl").args("-v", "--location", "--request", "PUT")
//            .arg("$TOS_BASE_URL$key")
//            .args("--header","'Content-Type: multipart/form-data'")
//            .args("--form", "\"file=@${file.absolutePath}\"").default().spawn().waitWithOutput()
//        println("put result: $result")
//        println(result.stdout)
//        println(result.stderr)
//        return IPutObjectResult(result.status == 0, null)
    }

    override fun putObject(
        key: String,
        inputStream: InputStream
    ): IPutObjectResult {
        val tempDir = createTempDirectory()
        createTempFile(tempDir, "${key.replace("/","_")}.${System.currentTimeMillis()}", ".tmp")
        val tempFile = createTempFile("${key.replace("/","_")}.${System.currentTimeMillis()}")
        inputStream.copyTo(tempFile.outputStream())
        return putObject(key, tempFile.toFile())
    }
}

class NoTTPTosClient(val property: Properties) : ITosClient {

    val endpoint: String by lazy {
        (property["TOS_ENDPOINT"]?.toString() ?: "")
    }

    val ak: String by lazy {
        (property["TOS_AK"]?.toString() ?: "")
    }
    val bucket: String by lazy {
        (property["TOS_BUCKET"]?.toString() ?: "")
    }

    val tosClient: TosClient by lazy {
        Tos.defaultTosClient(
            TosProperty() // 并发安全
                .setCredentials(
                    BucketAccessKeyCredentials(bucket, ak),
                ).setBucket("lv-android-build-script")
                .setTimeout(200)
                .setEndpoints(endpoint)
//                .setRemotePSM("toutiao.tos.tosapi"),
        )
    }

    override fun getObject(key: String): IGetObjectResult {
        val result = tosClient.getObject(key)
        return IGetObjectResult(result.objectContent != null, result.objectContent)
    }

    override fun putObject(key: String, file: File): IPutObjectResult {
        return runBlocking {
            try {
                SliceUpload(tosClient, key).upload(file)
                return@runBlocking IPutObjectResult(true, null)
            } catch (e: Exception) {
                return@runBlocking IPutObjectResult(false, null)
            }
        }
    }

    override fun putObject(
        key: String,
        inputStream: InputStream
    ): IPutObjectResult {
        try {
            tosClient.putObject(key, inputStream.readBytes())
            return IPutObjectResult(true, null)
        } catch (e: Exception) {
            return IPutObjectResult(false, null)
        }
    }
}

class TosUtils(val property: Properties) {

    val isTTP: Boolean by lazy {
        System.getenv().getOrDefault("TTP_BUILD_ENV", "false") != "false"
    }

    val tosClient: ITosClient by lazy {
        if (isTTP) {
            TTPTosClient(property)
        } else {
            NoTTPTosClient(property)
        }
    }


    fun getObject(key: String): IGetObjectResult? {
        return tosClient.getObject(key)
    }


    fun putObject(key: String, file: File): IPutObjectResult = tosClient.putObject(key, file)

    fun putObject(key: String, inputStream: InputStream): IPutObjectResult {
        return tosClient.putObject(key, inputStream)
    }


}