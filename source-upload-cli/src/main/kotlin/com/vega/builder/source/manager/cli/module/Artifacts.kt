package com.vega.builder.source.manager.cli.module

import com.google.gson.annotations.SerializedName

data class ArtifactsInfo(
    @SerializedName("artifacts")
    val artifacts: List<Artifact>,
    @SerializedName("bytebus_info_url")
    val bytebusInfoUrl: String
)

data class Artifact(
    @SerializedName("artifactId")
    val artifactId: String,
    @SerializedName("groupId")
    val groupId: String,
    @SerializedName("moduleName")
    val moduleName: String,
    @SerializedName("mrIid")
    val mrIid: Int,
    @SerializedName("projectId")
    val projectId: Int,
    @SerializedName("repo_snapshot")
    val repoSnapshot: String,
    @SerializedName("repository")
    val repository: String,
    @SerializedName("upgrade")
    val upgrade: Boolean,
    @SerializedName("uploadSource")
    val uploadSource: <PERSON><PERSON><PERSON>,
    @SerializedName("version")
    val version: String
)


