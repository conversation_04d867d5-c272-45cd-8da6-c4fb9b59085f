package com.vega.builder.shell

import StandaloneExtensions
import com.bytedance.storage.tos.Tos
import com.bytedance.storage.tos.TosClient
import com.bytedance.storage.tos.TosException
import com.bytedance.storage.tos.TosProperty
import com.bytedance.storage.tos.auth.BucketAccessKeyCredentials
import com.vega.builder.shell.utils.tos.TosUtils
import java.io.FileInputStream
import java.io.InputStreamReader
import java.util.*
import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.tasks.TaskProvider
import org.jetbrains.kotlin.com.google.gson.Gson
import java.io.ByteArrayInputStream
import java.io.File

class UploadTos(project: Project, val extensions: StandaloneExtensions) {
    private val property by lazy {
        Properties().apply {
            val configFile = project.rootProject.file("local.properties")
            if (configFile.exists()) {
                load(FileInputStream(configFile))
            }
        }
    }


    fun upload(file: String) {
        if (property["UPLOAD_TOS"] == "yes") {
            val tosClient = TosUtils(property)
            val infoIps = tosClient.getObject("${extensions.baseName}/${extensions.baseName}.info")?.contentStream
            val info = if (infoIps != null) {
                Gson().fromJson(InputStreamReader(infoIps), ModuleInfo::class.java)
            } else {
                null
            }
            println("publish：${Gson().toJson(info)}")
            val newInfo: ModuleInfo
            val fileName = if (property["env"] == "test") {
                newInfo = info?.copy(testVersion = info.nextTestVersion()) ?: ModuleInfo(0, 0)
                "${extensions.baseName}/${extensions.baseName}-test-${info?.nextTestVersion() ?: 0}"
            } else {
                newInfo = info?.copy(version = info.nextVersion()) ?: ModuleInfo(0, 0)
                "${extensions.baseName}/${extensions.baseName}-${info?.nextVersion() ?: 0}"
            }
            val lastFilename = if (property["env"] == "test") {
                "${extensions.baseName}/${extensions.baseName}-test"
            } else {
                "${extensions.baseName}/${extensions.baseName}"
            }
            try {
                tosClient.putObject(lastFilename, File(file))
                val result = tosClient.putObject(fileName, File(file))
                tosClient.putObject(
                    "${extensions.baseName}/${extensions.baseName}.info",
                    ByteArrayInputStream(Gson().toJson(newInfo).toByteArray())
                )
                println("putObject[${fileName}] succeed, object's etag is " + result.md5)
            } catch (e: TosException) {
                println("Caught a TosException")
                println("Error Message:" + e.message)
                println("Error Cause:" + e.cause)
                println("Error Request URL:" + e.requestUrl)
            }
        } else {
            throw Exception("Publish is not enabled")
        }
    }

    data class ModuleInfo(var version: Int, var testVersion: Int) {
        fun nextVersion(): Int {
            return version + 1
        }

        fun nextTestVersion(): Int {
            return testVersion + 1
        }
    }

}

fun Project.createUploadTosTasks(extensions: StandaloneExtensions): TaskProvider<Task> {
    return tasks.register("uploadTos") {
        doLast {
            val outFile = layout.projectDirectory.file("output/${extensions.baseName}").asFile
            UploadTos(this@createUploadTosTasks, extensions).upload(outFile.absolutePath)
        }
    }
}

