import com.github.jengelman.gradle.plugins.shadow.ShadowPlugin
import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar
import com.vega.builder.shell.createMakeShellTask
import com.vega.builder.shell.createProguardTask
import com.vega.builder.shell.createUploadTosTasks
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.file.DuplicatesStrategy
import org.gradle.kotlin.dsl.named

class PipelineStandalone : Plugin<Project> {

    override fun apply(target: Project) {
        with(target) {
            val extensions = target.extensions.create("PipelineStandalone", StandaloneExtensions::class.java)
            with(pluginManager) {
                apply(ShadowPlugin::class.java)
            }
            val jar = tasks.named<ShadowJar>("shadowJar") {
                isZip64 = true
                exclude("META-INF/*.SF","META-INF/*.DSA","META-INF/*.RSA")
                duplicatesStrategy = DuplicatesStrategy.EXCLUDE
                manifest {
                    attributes["Main-Class"] = extensions.mainClass
                }
                archiveBaseName.set(extensions.baseName)
            }
            val makeShallTask = createMakeShellTask(extensions)
            val uploadTosTask = createUploadTosTasks(extensions)
            afterEvaluate {
                val proguardTask = createProguardTask(extensions)
                proguardTask.dependsOn(jar)
                makeShallTask.get().dependsOn(proguardTask)
                uploadTosTask.get().dependsOn(makeShallTask)
            }

        }
    }
}