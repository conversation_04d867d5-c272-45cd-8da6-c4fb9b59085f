package com.vega.builder.pipeline.task.artifacts

import com.vega.builder.common.logger.logger
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.context.WorkspaceParams
import org.koin.core.component.inject
import java.io.File

@TaskDefinition(TTPResultCollectTask.TAG, stage = Stage.Artifacts, displayName = "TTP Result Collect")
class TTPResultCollectTask : PipelineTask() {
    companion object {
        const val TAG = "TTPResultCollectTask"
    }

    val workspaceParams: WorkspaceParams by inject()

    override suspend fun run() {
        val resultBaseFile = File(
            listOf<String>(
                workspaceParams.targetProjectDir,
                "build",
                "ttp_result"
            ).joinToString(File.separator)
        ).apply { if (!exists()) mkdirs() }
        PipelineOutput.buildOutputs().forEach { output ->
            val srcFile = File(output.second)
            var apkIndex = 0
            val fileName = if (srcFile.name.endsWith("CapCut-64.apk")) {
                "CapCut-64-ttp.apk"
            } else if (srcFile.name.endsWith("CapCut-universal.apk")) {
                "CapCut-universal-ttp.apk"
            } else if (srcFile.name.endsWith("release.aab")) {
                "CapCut-ttp.aab"
            } else if (srcFile.name.endsWith(".apk")) {
                apkIndex += 1
                "CapCut-ttp-$apkIndex.apk"
            } else {
                srcFile.name
            }
            if (srcFile.exists()) {
                val distFile = File(resultBaseFile, fileName)
                logger().info("file ${srcFile.absolutePath} copy to ${distFile.absolutePath}")
                srcFile.copyTo(distFile, true)
            } else {
                logger().info("file not found: ${srcFile.absolutePath}")
            }

        }
    }

}