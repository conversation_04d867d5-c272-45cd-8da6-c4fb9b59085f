package com.vega.builder.pipeline.task.repo

import com.vega.builder.common.logger.logger
import com.vega.builder.common.utils.runCommandWithResult
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.WorkspaceParams
import org.eclipse.jgit.api.Git
import org.eclipse.jgit.api.ListBranchCommand.ListMode
import org.eclipse.jgit.lib.ObjectIdRef
import org.eclipse.jgit.lib.Ref
import org.eclipse.jgit.storage.file.FileRepositoryBuilder
import org.eclipse.jgit.transport.URIish
import org.koin.core.component.inject
import org.slf4j.Logger
import java.io.Closeable
import java.io.File
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date


@TaskDefinition("ClearRepo", stage = Stage.Prepare, displayName = "Clean Git Repo")
class ClearRepo : PipelineTask(), Closeable {
    companion object {
        const val BACKUP_REMOTE = "backup-remote"
        const val ORIGIN_REMOTE = "origin"
        const val BACKUP_URI = "******************:faceu-android/vega-backup.git"
    }

    val workspaceParams: WorkspaceParams by inject()

    private val logger: Logger = logger()

    private val repository by lazy {
        FileRepositoryBuilder().setGitDir(File("${workspaceParams.targetProjectDir}/.git"))
            .readEnvironment()
            .findGitDir()
            .build()
    }


    private val git by lazy {
        Git(repository)
    }

    fun addBackupRemote() {
        // Open Git Repo
        // request remote config
        val remotes = git.remoteList().call()
        val backRemote = remotes.firstOrNull { it.name == BACKUP_REMOTE }
        if (backRemote == null) {
            git.remoteAdd().setName(BACKUP_REMOTE).setUri(URIish(BACKUP_URI)).call()
            logger.info("Add Remote $BACKUP_REMOTE[$BACKUP_URI]")
        } else {
            logger.info("Remote has $BACKUP_REMOTE[$BACKUP_URI]")
        }
        repository.close()
    }

    fun getOriginBranch(): List<Ref> {

        val result = git.branchList().setListMode(ListMode.REMOTE).call()
        return result.filterIsInstance<ObjectIdRef>().filter {
            it.name.startsWith("refs/remotes/origin/")
        }
    }

    fun pushBackupRepo(branches: List<String>): Boolean {
        var result =
            "git fetch $ORIGIN_REMOTE ${branches.joinToString(" ") { "$it:$it" }}".runCommandWithResult(
                File(
                    workspaceParams.targetProjectDir
                )
            )
        if (!result) {
            logger.info("fetch remote $ORIGIN_REMOTE [\n${branches.joinToString("\n")}\n] failed")
            return false
        }
        result =
            "git push $BACKUP_REMOTE ${branches.joinToString(" ")}".runCommandWithResult(File(workspaceParams.targetProjectDir))
        if (!result) {
            logger.info("push remote $BACKUP_REMOTE [\n${branches.joinToString("\n")}\n] failed")
        }
        return result
    }


    override suspend fun run() {
        addBackupRemote()
        // filter branches that should delete
        val oneYearAgo = Date.from(Instant.now().minus(365, ChronoUnit.DAYS))

        val exclusionPrefixes =
            listOf("release", "develop", "dreamina", "bits_patch", "hotfix", "rc/develop", "oversea", "master", "patch")
        val prefixesWithLimitTime = hashMapOf<String, Date>(
            "p/robot.translation" to Date.from(Instant.now().minus(15, ChronoUnit.DAYS)),
            "p/robot.merge" to Date.from(Instant.now().minus(60, ChronoUnit.DAYS)),
            "p/robot/merge" to Date.from(Instant.now().minus(60, ChronoUnit.DAYS)),
            "p/robot.updateVe" to Date.from(Instant.now().minus(15, ChronoUnit.DAYS)),
            "p/robot.lynx.template" to Date.from(Instant.now().minus(15, ChronoUnit.DAYS)),
            "p/batch_mr" to Date.from(Instant.now().minus(5, ChronoUnit.DAYS)),
            "optimus/integration" to Date.from(Instant.now().minus(180, ChronoUnit.DAYS)),
            "integration/" to Date.from(Instant.now().minus(180, ChronoUnit.DAYS)),
        )
        val deleteBranch = getOriginBranch().filter { branch ->
            val branchName = branch.name.substringAfter("refs/remotes/origin/")
            if (exclusionPrefixes.any { branchName.startsWith(it) }) return@filter false
            val lastCommit = repository.parseCommit(branch.objectId)
            val lastCommitDate = Date(lastCommit.commitTime.toLong() * 1000)
            val expirationDate =
                prefixesWithLimitTime.entries.find { branchName.startsWith(it.key) }?.value ?: oneYearAgo
            lastCommitDate.before(expirationDate)
        }
        // push to back up repo
        // delete push success branch
        deleteBranch.map { it.name.substringAfter("refs/remotes/origin/") }.chunked(10).forEach { chunkBranches ->
            if (pushBackupRepo(chunkBranches)) {
                val result =
                    "git push origin --delete ${chunkBranches.joinToString(" ")}".runCommandWithResult(
                        File(
                            workspaceParams.targetProjectDir
                        )
                    )
                if (result) {
                    logger.info("Delete branch ${chunkBranches.joinToString(" ")} success")
                } else {
                    logger.error("Delete branch ${chunkBranches.joinToString(" ")} failed")
                }
            }
        }
    }

    override fun close() {
        repository.close()
    }
}