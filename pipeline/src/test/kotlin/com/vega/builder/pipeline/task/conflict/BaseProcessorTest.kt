package com.vega.builder.pipeline.task.conflict

import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import java.io.File

/**
 *
 *
 * <AUTHOR>
 * @time 2025/2/27
 */

class TestConflictFileReader(filePath: String, val fileContent: String) :
    ConflictFileReader(filePath) {

    val fileContentList = fileContent.split("\n").toMutableList()

    override fun readByLine(action: (index: Int, lineContent: String) -> Unit) {
        fileContentList.forEachIndexed { index, line ->
            action(index, line)
        }
    }

    override fun replaceLines(
        conflictBlock: List<ConflictBlock>,
        resolveContent: List<List<String>>
    ) {
        conflictBlock.reversed().forEachIndexed { index, block ->
            fileContentList.subList(block.startLine, block.endLine + 1).clear()
            fileContentList.addAll(block.startLine, resolveContent.reversed()[index])
        }
    }

    override fun getContent(): String {
        return fileContentList.joinToString("\n")
    }

    override fun verifyFile(): Boolean {
        var hasConflict = false
        this.readByLine { _, line ->
            if (line.startsWith("<<<<<<<")) {
                hasConflict = true
                return@readByLine
            } else if (line.startsWith(">>>>>>>")) {
                hasConflict = true
                return@readByLine
            } else if (line.startsWith("=======")) {
                hasConflict = true
                return@readByLine
            }
        }
        return hasConflict
    }
}

abstract class BaseProcessorTest {
    abstract val resolveConflict: String
    abstract var fileContent: String
    abstract val filePath: String

    open fun mockFileReader(): ConflictFileReader {
        return TestConflictFileReader(filePath, fileContent)
    }

    open fun mockFile(): File {
        val file = mockk<File>()
        mockkStatic("kotlin.io.FilesKt__FileReadWriteKt")
        every { file.readText() } returns fileContent
        every { file.readLines() } returns fileContent.split("\n")
        every { file.getAbsolutePath() } returns filePath
        every { file.writeText(any(), any()) } returns Unit
        return file
    }
}