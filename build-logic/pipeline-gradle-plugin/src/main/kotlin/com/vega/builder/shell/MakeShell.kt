package com.vega.builder.shell

import StandaloneExtensions
import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar
import java.io.FileOutputStream
import java.io.InputStream
import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.tasks.TaskProvider
import org.gradle.api.tasks.bundling.Jar
import proguard.gradle.ProGuardTask

private object ResourceUtils {
    fun readResource(filePath: String): InputStream? {
        // 获取当前类的类加载器
        val classLoader = this::class.java.classLoader
        // 使用类加载器获取资源作为输入流
        val inputStream: InputStream? = classLoader.getResourceAsStream(filePath)
        return inputStream
    }
}

fun Project.createMakeShellTask(extensions: StandaloneExtensions): TaskProvider<Task> {
    return tasks.register("makeShell") {
        doLast {
            val outFile = layout.projectDirectory.file("output/${extensions.baseName}").asFile
            if (!outFile.parentFile.exists()) {
                outFile.parentFile.mkdirs()
            }
            FileOutputStream(outFile).use { stream ->
                ResourceUtils.readResource("launch.sh")?.copyTo(stream) ?: throw Exception("No launcher script found")
                stream.write(System.lineSeparator().encodeToByteArray())
                if (extensions.enableProguard) {
                    val proguardResultFile =
                        layout.projectDirectory.file("output/${extensions.baseName}-${proguardResultFileName}").asFile
                    proguardResultFile.apply { println("Jar:${this.absolutePath}") }.inputStream().copyTo(stream)
//                       .apply { println("Jar:${this.absolutePath}") }
//                       .inputStream().copyTo(stream)
                } else {
                    tasks.withType(ShadowJar::class.java).first()
                        .archiveFile
                        .get()
                        .asFile
                        .apply { println("Jar:${this.absolutePath}") }
                        .inputStream().copyTo(stream)
                }
            }
        }
    }
}