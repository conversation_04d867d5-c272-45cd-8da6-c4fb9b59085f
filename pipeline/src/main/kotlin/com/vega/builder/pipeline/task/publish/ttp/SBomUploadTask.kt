package com.vega.builder.pipeline.task.publish.ttp

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.logger.logger
import com.vega.builder.common.utils.runCommand
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PathDeclare
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.WorkspaceParams
import org.koin.core.component.inject
import java.io.File
import kotlin.getValue
import kotlin.io.path.createTempDirectory
import kotlin.io.path.createTempFile
import kotlin.io.path.writeText

@TaskDefinition("SBomTask", stage = Stage.After, "sbom generate and upload")
class SBomUploadTask : PipelineTask() {
    val pipelineContext by inject<PipelineContextImpl>()
    val workspaceParams by inject<WorkspaceParams>()
    val buildParams by inject<BuildParams>()
    val pathDeclare: PathDeclare by inject()
    override suspend fun run() {

        val publishReportRepoRoot = createTempDirectory("publish").toFile().apply { if (!exists()) mkdirs() }
        val publishReportRepoFile = File(publishReportRepoRoot, "publish")

        "git clone --depth 1 ******************:tech_client/publish.git -b develop".runCommand(
            publishReportRepoRoot, false
        )

        val sbomMetadataPath = findSbomJsonFile()
        if (sbomMetadataPath?.exists() == true) {
            logger().info("sbom.json: ${sbomMetadataPath.absolutePath}")
            sbomMetadataPath.readText().let {
                logger().info("sbom.json: $it")
            }
            Command("python3")
                .args("python/entry/ttp_sbom_upload.py")
                .directory(publishReportRepoFile)
//                .directory(File("/Users/<USER>/develop/bytedance/publish"))
                .env("SBOM_JSON_PATH", sbomMetadataPath.absolutePath)
                .default().spawn().wait()
            sbomMetadataPath.readText().let {
                logger().info("sbom.json: $it")
            }
        } else {
            logger().error("sbomMetadataPath not found ${sbomMetadataPath?.absolutePath}")
        }
    }

    fun findSbomJsonFile(): File? {
        if (pathDeclare.customSbomFile.exists() && pathDeclare.customSbomFile.readText().isNotBlank()) {
            logger().info("found custom sbom.json")
            return pathDeclare.customSbomFile
        }
        val publishSbomFile = File(
            listOf(
                workspaceParams.targetProjectDir,
                "build",
                "reports",
                "sbom_meta.json"
            ).joinToString(separator = File.separator)
        )
        if (publishSbomFile.exists()) {
            logger().info("found publish sbom.json")
            return publishSbomFile
        }

        val ccSbomFile = File(
            listOf(
                workspaceParams.targetProjectDir,
                "build",
                "reports",
                "cc-sbom.xml"
            ).joinToString(separator = File.separator)
        )
        if (ccSbomFile.exists()) {
            val ccSbomMetaDataFile = createTempFile("sbom", ".json").apply {
                writeText(
                    Gson().toJson(
                        listOf(
                            SbomMetadata(name = "CapCut", version = "1.0.0", sbomPath = ccSbomFile.absolutePath)
                        )
                    )
                )
            }
            logger().info("found  build cc-sbom.xml")
            return ccSbomMetaDataFile.toFile()
        }

        return null
    }
}

data class SbomMetadata(
    @SerializedName("type") val type: String = "Custom",
    @SerializedName("source") val source: String = "",
    @SerializedName("name") val name: String,
    @SerializedName("version") val version: String,
    @SerializedName("sbom_path") val sbomPath: String,
    @SerializedName("parfait_path") val parfaitPath: String = ""
)
