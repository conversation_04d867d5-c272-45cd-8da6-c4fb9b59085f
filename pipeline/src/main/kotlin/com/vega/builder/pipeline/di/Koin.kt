package com.vega.builder.pipeline.di

import com.vega.builder.common.config.IConfigService
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.ConfigServiceImpl
import com.vega.builder.pipeline.context.PathDeclare
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.createGitParams
import com.vega.builder.pipeline.context.createPipelineContext
import com.vega.builder.pipeline.context.createWorkspaceParams
import org.koin.core.context.startKoin
import org.koin.core.module.dsl.factoryOf
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module
import org.koin.logger.slf4jLogger


fun configureKoin() {
    startKoin {
        slf4jLogger()

        modules(module {
            factoryOf(::ConfigServiceImpl) bind IConfigService::class
            single {
                println("createPipelineContext")
                createPipelineContext()
            } bind PipelineContextImpl::class
            singleOf(::BuildParams)
            single{
                createWorkspaceParams()
            }
            single{
                createGitParams(get())
            }
            singleOf(::PathDeclare)
        })
    }
}