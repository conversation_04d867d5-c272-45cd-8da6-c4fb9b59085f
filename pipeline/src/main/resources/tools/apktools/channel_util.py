import sys

import apktools
import v3_apktools
import zipfile
import os
import configparser
import json
import shutil
import argparse


class SignTask:
    def __init__(self, input_apk, output_apk, channel):
        self.input_apk = input_apk
        """:type before_sign_apk : str"""
        self.output_apk = output_apk
        self.channel = channel
        self.extra_info = {'meta_umeng_channel': self.channel}

    def write_channel(self):
        """:rtype str"""
        release_build = self.get_apk_info(self.input_apk)
        self.extra_info['release_build'] = release_build

        signed_apk_parent_path = os.path.abspath(
            os.path.join(self.input_apk, os.path.pardir))
        signed_workspace = os.path.join(signed_apk_parent_path, "temp")
        if os.path.exists(signed_workspace):
            shutil.rmtree(signed_workspace)
        os.makedirs(signed_workspace)

        wrote_channel_apk_path = os.path.join(
            signed_workspace, 'signed_channel.apk')

        apktools.write_apk_info(self.input_apk, wrote_channel_apk_path,
                                {apktools.APK_CHANNEL_BLOCK_ID: json.dumps(self.extra_info)})
        os.rename(self.input_apk, self.input_apk + ".tem")

        v3_apktools.complete_v3_sign(wrote_channel_apk_path, self.output_apk)

        # check apk channel
        channel_block_value = apktools.get_value_by_id(
            self.output_apk, apktools.APK_CHANNEL_BLOCK_ID)
        channel_block_value_dict = json.loads(channel_block_value)
        assert self.channel == channel_block_value_dict[
            "meta_umeng_channel"], 'quick package channel % error' % self.channel

    @staticmethod
    def get_apk_info(apk_path):
        zin = zipfile.ZipFile(apk_path, 'r')
        fp = zin.open('assets/ss.properties')
        config = configparser.SafeConfigParser()
        config.readfp(FakeSecHead(fp))
        section = 'default'
        if config.has_option(section, 'release_build'):
            release_build = config.get(section, 'release_build').split('_')[0]
            release_build_without_split = config.get(section, 'release_build')
            print('release_build = %s' % str(release_build))
            print('release_build_without_split = %s' %
                  str(release_build_without_split))
        else:
            release_build = ""
        return release_build

class FakeSecHead(zipfile.ZipExtFile):

    def __init__(self, fp):
        self.fp = fp
        self.sechead = True

    def readline(self):
        if self.sechead:
            self.sechead = False
            return '[default]\n'
        else:
            return self.fp.readline().decode('utf-8')


def test_v2_sign():
    input_apk = '/Users/<USER>/develop/bytedance/DexVMP_script/VideoCut-prod-release.apk'
    output_apk = '/Users/<USER>/develop/bytedance/DexVMP_script/VideoCut-prod-release-signed.apk'
    sign_task = SignTask(input_apk, output_apk, "update_lzd")
    print(sign_task.write_channel())


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Process some integers.')
    parser.add_argument('--input', dest='input_apk', action='store', nargs="?", type=str,
                        default=None,
                        help='Signed Apk Path')
    parser.add_argument('--output', dest='output_apk', action='store', nargs="?", type=str,
                        default=None,
                        help='Wrote channel Apk Path')
    parser.add_argument('--channel', dest='channel', action='store', nargs="?", type=str,
                        default=None,
                        help='apk channel')
    args = parser.parse_args()
    if args.input_apk is not None and args.output_apk is not None and args.channel and not None:
        print(args.input_apk)
        print(args.output_apk)
        print(args.channel)
        sign_task = SignTask(args.input_apk, args.output_apk, args.channel)
        sign_task.write_channel()
    else:
        sys.exit(1)
