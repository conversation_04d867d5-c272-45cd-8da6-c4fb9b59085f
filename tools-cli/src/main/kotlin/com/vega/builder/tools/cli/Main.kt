package com.vega.builder.tools.cli

import com.github.ajalt.clikt.core.CliktCommand
import com.github.ajalt.clikt.core.main
import com.github.ajalt.clikt.parameters.groups.OptionGroup
import com.github.ajalt.clikt.parameters.groups.groupChoice
import com.github.ajalt.clikt.parameters.options.option
import com.github.ajalt.clikt.parameters.options.prompt
import com.vega.builder.common.logger.configureLogback
import com.vega.builder.tools.cli.dagger.DaggerManager
import com.vega.builder.tools.cli.figma.ThemeBuilder

import com.vega.builder.tools.cli.webp.PngConvertWebp
import org.jetbrains.kotlin.gradle.internal.kapt.incremental.OutputBinDiffTools
import java.io.File


fun main(args: Array<String>) {
    configureLogback()
    Tools().main(args)
}


sealed class ToolsConfig(name: String) : OptionGroup(name)

class FigmaTools : ToolsConfig("Figma Tools") {
    val colorFile by option("--color-file", help = "Color file path").prompt()
    val output by option("-o", "--output", help = "Output directory").prompt()
    val colorSchemaName by option("-cs", "--color-schema", help = "Color Schema class name").prompt()
    val baselineSchemaName by option("--base-schema-name", help = "Mode name for generated Color Schema class").prompt()
    val themePackage by option("--theme-package", help = "Theme package name").prompt()
}

const val tips = """
    -f figma --color-file /Users/<USER>/develop/bytedance/build-cli/tools-cli/color.json -o /Users/<USER>/develop/bytedance/build-cli/tools-cli/Theme.kt --color-schema ColorSchema --base-schema-name PippitLight --theme-package com.vega.component.Theme
    -f dagger -p /Users
"""

class DaggerTools : ToolsConfig("Dagger to Koin Converter") {
    val projectPath by option("--dagger-project-path", help = "Project path").prompt()
}

class WebpTools : ToolsConfig("WebP Converter") {
    val projectPath by option("--web-project-path", help = "Project path").prompt()
}


class KotlinToolsOutputBinDiffTools : ToolsConfig("Output bin diff") {
    val binA by option("--bin-a").prompt(default = "")
    val binB by option("--bin-b").prompt(default = "")
}

class KotlinToolsOutputBinParse : ToolsConfig("Output praseness") {
    val binPath by option("--bin-path").prompt(default = "")
}

class Tools : CliktCommand() {
    val function by option("-f", "--function").groupChoice(
        "figma" to FigmaTools(),
        "dagger" to DaggerTools(),
        "webp" to WebpTools(),
        "kotlin-tools-output-bin-diff" to KotlinToolsOutputBinDiffTools(),
        "kotlin-tools-output-bin-parse" to KotlinToolsOutputBinParse(),
    )

    override fun run() {
        when (val it = function) {
            is FigmaTools -> {
                ThemeBuilder(it.colorSchemaName, it.baselineSchemaName, it.themePackage).processTheme(it.colorFile)
                    .genComposeColorSchemeCode(it.output)
            }

            is DaggerTools -> {
                DaggerManager.collectAndGenerateKoinConfig(it.projectPath)
            }

            is WebpTools -> {
                PngConvertWebp.convert(File(it.projectPath))
            }

            is KotlinToolsOutputBinDiffTools -> {
                val binA = it.binA
                val binB = it.binB
                if (binA.isNotBlank() && binB.isNotBlank()) {
                    OutputBinDiffTools.diff(File(binA), File(binB))
                } else {
                    echo("Not loading")
                }
            }

            is KotlinToolsOutputBinParse -> TODO()
            null -> echo("Not loading")
        }
    }
}