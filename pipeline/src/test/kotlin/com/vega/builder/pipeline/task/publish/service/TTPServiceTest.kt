package com.vega.builder.pipeline.task.publish.service

import com.google.gson.Gson
import com.vega.builder.common.network.api.TTPJobBaseInfo
import com.vega.builder.common.network.api.TTPJobTriggerInfo
import com.vega.builder.common.network.api.TTPJobTriggerReq
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class TTPServiceTest {
    @Test
    fun `check trigger Job`() {
        runBlocking {
            val version = "14.9.0.10086"

//            CoroutineScope(Dispatchers.IO).launch {
//                while (true) {
//                    delay(60_000)
//                    request(IBuildServiceApi::updateTTPState)
//                }
//            }
//            TTPService.triggerJob(
//                TTPJobTriggerReq(
//                    baseInfo = TTPJobBaseInfo(
//                        operator = "lizhengda.da",
//                        version = version,
//                        projectId = 798270,
//                        mrIid = 1932
//                    ),
//                    repoList = mapOf(
//                        0 to TTPJobTriggerInfo(
//                            repoId = 51594, nextRepoId = 51566,
//                            branch = "rc/develop",
//                            taskParams = "publish_ttp_source_videoeditor"
//                        ),
//                        1 to TTPJobTriggerInfo(
//                            repoId = 51566, nextRepoId = null,
//                            branch = "rc/develop",
//                            taskParams = Gson().toJson(
//                                mapOf<String, String>(
//                                    "REPLACE_VERSION" to Gson().toJson(mapOf("com.lemon.faceu:videoeditor_cc_repo_info" to version)),
//                                    "ext_template" to "publish_ttp_binary_videoeditor"
//                                )
//                            )
//                        )
//                    )
//                ),
//                mapOf(
//                    51594 to false,
//                    51566 to false,
//                )
//            )
            val buildVersion = "apk-build-${System.currentTimeMillis()}"
            TTPService.triggerJob(
                TTPJobTriggerReq(
                    baseInfo = TTPJobBaseInfo(
                        operator = "lizhengda.da",
                        version = buildVersion,
                        projectId = -1,
                        mrIid = -1,
                        from = ""
                    ),
                    repoList = mapOf(
                        0 to TTPJobTriggerInfo(
                            repoId = 52081,
                            branch = "overseas/release/14.9.0",
                            taskParams = Gson().toJson(mapOf<String,String>().toMutableMap().apply {
                                remove("RUNNING_TYPE")
                                this["RUNNING_TYPE"] = "ttp_build"
                            }),
//                            createNewBranch = false
                        ),
                    )
                ),
                skipCheckResult = mapOf(
                    52081 to false
                )
            )
        }
    }
}