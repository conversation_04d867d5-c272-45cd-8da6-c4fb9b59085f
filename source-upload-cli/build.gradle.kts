plugins {
    kotlin("jvm")
    alias(libs.plugins.kotlinx.serialization)
    alias(libs.plugins.build.shell)

}
version = "1.0.0"

dependencies {
    implementation(kotlin("stdlib-common"))
    implementation(kotlin("reflect"))

    implementation(project(":common"))

    implementation(libs.bundles.kotlinx.official.libs)
    implementation(libs.okio)
    implementation(libs.bundles.retrofit)
    implementation(libs.jgit)
    implementation(libs.gson)
    implementation(libs.bundles.log)
    implementation(libs.tos.core)
    implementation(libs.source.manager.core)
}
kotlin {
    jvmToolchain(11)
}

standalone {
    mainClass = "com.vega.builder.source.manager.cli.MainKt"
    baseName = "source-manager-cli"
}

