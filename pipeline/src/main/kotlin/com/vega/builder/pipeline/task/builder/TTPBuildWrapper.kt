package com.vega.builder.pipeline.task.builder

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.ITiktokMavenApi
import com.vega.builder.common.network.api.TTPJobBaseInfo
import com.vega.builder.common.network.api.TTPJobTriggerInfo
import com.vega.builder.common.network.api.TTPJobTriggerReq
import com.vega.builder.common.network.request
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.tos.slicePutObject
import com.vega.builder.common.utils.BitsUtils
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.md5
import com.vega.builder.common.utils.raceOfSuccess
import com.vega.builder.common.utils.retry
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.BuildConfig
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.GitParams
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.task.artifacts.ArtifactsUploadResult
import com.vega.builder.pipeline.task.artifacts.SYMBOL_UPLOAD_KEY_PREFIX
import com.vega.builder.pipeline.task.artifacts.SymbolArtifactInfo
import com.vega.builder.pipeline.task.artifacts.TTPArtifactsUpload
import com.vega.builder.pipeline.task.publish.service.TTPArtifactsService
import com.vega.builder.pipeline.task.publish.service.TTPService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.selects.select
import org.koin.core.component.inject
import java.io.File
import kotlin.io.path.createTempFile

@TaskDefinition(name = "Build Result", stage = Stage.Build, displayName = "TTP Build Wrapper")
class TTPBuildWrapper : PipelineTask() {
    val gitParams by inject<GitParams>()
    val buildParams by inject<BuildParams>()


    override suspend fun run() {
        if (gitParams.mainBranch == null) {
            throw IllegalArgumentException("mainBranch can not be null")
        }
        val successVersion = buildParams["TTP_BUILD_SUCCESS_VERSION"] ?: tryThreeTimesBuild()
        val jobInfo = TTPService.searchTTPJobInfo("52081", version = successVersion)
        val jobId = jobInfo?.jobInfo?.jobId ?: return
        TTPArtifactsService.processArtifacts(jobId)
        TTPArtifactsService.processSymbolArtifacts(jobId)
    }

    private suspend fun triggerBuild(buildVersion: String): String {
        logger().info("trigger ttp job version $buildVersion")
        TTPService.triggerJob(
            TTPJobTriggerReq(
                baseInfo = TTPJobBaseInfo(
                    operator = getenvSafe("USER_EMAIL_PREFIX", "lizhengda.da"),
                    version = buildVersion,
                    projectId = -1,
                    mrIid = -1,
                    from = getenvSafe("WORKFLOW_JOB_ID", "none"),
                ),
                repoList = mapOf(
                    0 to TTPJobTriggerInfo(
                        repoId = 52081,
                        branch = gitParams.mainBranch!!,
                        taskParams = Gson().toJson(buildParams.toMutableMap().apply {
                            remove("BUILD_PARAMS")
                            this["RUNNING_TYPE"] = "ttp_build"
                        }),
                        createNewBranch = false
                    ),
                )
            ),
            skipCheckResult = mapOf(
                52081 to false
            )
        )
        return buildVersion
    }

    // 新增辅助函数：触发三次并行构建，返回最先成功的构建版本
    private suspend fun tryThreeTimesBuild(): String {
        val buildVersion = "apk-build-${System.currentTimeMillis()}"
        return if (buildParams.isEnable("racing_execution_ttp", false)) {
            raceOfSuccess(
                *(0 until 3).map { index ->
                    suspend { triggerBuild("${buildVersion}-$index") }
                }.toTypedArray()
            )
        } else {
            triggerBuild(buildVersion)
        }
    }





}