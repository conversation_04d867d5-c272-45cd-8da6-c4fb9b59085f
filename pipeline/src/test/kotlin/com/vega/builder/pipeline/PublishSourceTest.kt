package com.vega.builder.pipeline

import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.utils.setenvMock
import org.junit.jupiter.api.Test

/**
 *
 *
 * <AUTHOR>
 * @time 2025/2/13
 */
class PublishSourceTest {

    fun mockEnv() {
        setenvMock("SYSTEM_MOCK", "true")
//        setenvMock("ext_params", """
//            {"REPLACE_VERSION":"{\"com.lemon.faceu:videoeditor_cc_repo_info\":\"14.7.1-alpha.214-test-SNAPSHOT\"}"}
//        """.trimIndent())
        setenvMock("WORK_DIR", "/Users/<USER>/develop/bytedance/cc")
        setenvMock("PUBLISH_PARAMS", """
            {
                "artifacts": [
                    {
                        "upload_source": false,
                        "mrIid": 22846,
                        "moduleName": ":videoeditor_repo_info",
                        "version": "*********",
                        "repository": "http://maven.byted.org/repository/bytedance_android/",
                        "projectId": 134421,
                        "repo": "http://maven.byted.org/repository/bytedance_android/",
                        "upgrade": true,
                        "uploadSource": false,
                        "module_name": ":videoeditor_repo_info",
                        "groupId": "com.lemon.faceu",
                        "artifactId": "videoeditor_cc_repo_info"
                    }
                ],
                "bytebus_info_url": "https://voffline.byted.org/download/tos/schedule/toutiao.ios.arch/android_batch_upgrade/798270/1919/1753336099557.json"
            }
        """.trimIndent())
        setenvMock(
            "BUILD_PARAMS", """
            {
                "RUNNING_TYPE": "publish_sub_repo_source"
            }
        """.trimIndent()
        )
    }

    @Test
    fun testBuildTask() {
        mockEnv()
        main()
    }
}