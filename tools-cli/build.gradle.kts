plugins {
    kotlin("jvm")
    alias(libs.plugins.kotlinx.serialization)
    alias(libs.plugins.build.shell)

}
version = "1.0.0"

dependencies {
    implementation(fileTree(mapOf("dir" to rootProject.file("libs").absolutePath, "include" to listOf("*.jar"))))
    implementation(kotlin("stdlib-common"))
    implementation(libs.bundles.kotlinx.official.libs)
    implementation(libs.okio)
    implementation(libs.bundles.retrofit)
    implementation(libs.jgit)
    implementation(libs.gson)
    implementation(libs.bundles.log)
    implementation("org.jetbrains.kotlin:kotlin-compiler-embeddable:1.7.21")
    implementation(project(":common"))
    implementation(project(":kotlin-psi"))
    implementation(kotlin("reflect"))
    testImplementation(kotlin("test"))
    testImplementation("io.mockk:mockk:1.12.0")
    implementation(libs.clikt) //https://ajalt.github.io/clikt/whyclikt/
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.5.2")
}
kotlin {
    jvmToolchain(11)
}

standalone {
    mainClass = "com.vega.builder.tools.cli.MainKt"
    baseName = "tools-cli"
}

