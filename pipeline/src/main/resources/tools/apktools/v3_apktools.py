# -*- coding: utf-8 -*-
import os
import struct
import json
import subprocess

ZIP_EOCD_REC_MIN_SIZE = 22
UINT16_MAX_VALUE = 0xFFFF
ZIP_EOCD_REC_SIG = 0x06054b50
ZIP_EOCD_COMMENT_LENGTH_FIELD_OFFSET = 20
APK_SIG_BLOCK_MIN_SIZE = 32
APK_SIG_BLOCK_MAGIC_HI = 0x3234206b636f6c42  # LITTLE_ENDIAN, High
APK_SIG_BLOCK_MAGIC_LO = 0x20676953204b5041  # LITTLE_ENDIAN, Low
APK_SIGNATURE_SCHEME_V2_BLOCK_ID = 0x7109871a  # sign block id
APK_CHANNEL_BLOCK_ID = 0x71777777  # channel block id
APK_TRACE_BLOCK_ID = 0x71777778  # trace block id
APK_CONFIG_ID = 0x71777776
COMPLETION_ID = 0x71777775
V3_SIGN_BLOCK = 0xf05368c0  # v3 签名ID
V3_SIGN_BLOCK_TEST = 0x42726577  # 这个ID暂时不清楚做什么需要看源码


def execute_cmd(cmd):
    p = subprocess.Popen(cmd, stderr=subprocess.PIPE, stdout=subprocess.PIPE)
    output = p.communicate()
    stdout = output[0]
    stderr = output[1]
    print(stderr)
    print(stdout)
    result_code = 0
    if stderr is not None and stderr.__len__() > 0:
        result_code = -1
    return result_code, stdout, stderr


def get_value_by_id(apk_file_path, id):
    comment_length = get_comment_length(apk_file_path)
    offset = find_central_dir_start_offset(apk_file_path, comment_length)
    apk_sig_block_offset, apk_sig_block_size = find_apk_signing_block(apk_file_path, offset)
    id_value_dict = find_id_value(apk_file_path, apk_sig_block_offset, apk_sig_block_size)
    if id_value_dict.__contains__(id):
        value_offset = id_value_dict[id][0]
        size = id_value_dict[id][1] - id_value_dict[id][0]
        file_obj = open(apk_file_path, 'rb')
        file_obj.seek(value_offset)
        value = file_obj.read(size)
        return value
    else:
        return ""


def check_v3_sign(source_file_path):
    """检测是否包含v3签名"""
    source_comment_length = get_comment_length(source_file_path)
    source_central_dir_offset = find_central_dir_start_offset(source_file_path, source_comment_length)
    source_sig_block_offset, source_sig_block_size = find_apk_signing_block(source_file_path, source_central_dir_offset)
    source_id_value_dict = find_id_value(source_file_path, source_sig_block_offset, source_sig_block_size)

    print('=' * 60)
    for source_id in source_id_value_dict.keys():
        print('%x' % source_id)
    print('=' * 60)
    return source_id_value_dict.__contains__(V3_SIGN_BLOCK)


def delete_v3_sign(source_file_path, dest_file_path):
    """删除v3签名区域"""

    source_comment_length = get_comment_length(source_file_path)
    source_central_dir_offset = find_central_dir_start_offset(source_file_path, source_comment_length)
    source_sig_block_offset, source_sig_block_size = find_apk_signing_block(source_file_path, source_central_dir_offset)
    source_id_value_dict = find_id_value(source_file_path, source_sig_block_offset, source_sig_block_size)

    if not source_id_value_dict.__contains__(V3_SIGN_BLOCK):
        raise RuntimeError("v3 sign not exist")

    source_file_obj = open(source_file_path, 'rb')
    dest_file_obj = open(dest_file_path, 'wb+')
    dest_file_obj.write(source_file_obj.read(source_sig_block_offset))

    source_file_obj.seek(0)

    id_value = dict()
    for key, value in list(source_id_value_dict.items()):
        if not id_value.__contains__(key):
            print('add source apk key : %x ' % key)
            start_position = value[0]
            end_position = value[1]
            size = end_position - start_position
            source_file_obj.seek(start_position)
            id_value[key] = source_file_obj.read(size)

    id_value.pop(V3_SIGN_BLOCK, None)
    id_value.pop(V3_SIGN_BLOCK_TEST, None)

    total_length = 24
    for key, value in list(id_value.items()):
        item_value_length = len(value)
        print(type(value))
        print('item # %x size %d ' % (key, item_value_length))
        total_length += 12 + item_value_length
    print('total size %d   %x' % (total_length, total_length))

    dest_file_obj.write(struct.pack('<Q', total_length))

    for key, value in list(id_value.items()):
        item_value_length = len(value)
        item_length = item_value_length + 4
        dest_file_obj.write(struct.pack('<Q', item_length))
        dest_file_obj.write(struct.pack('<I', key))
        dest_file_obj.write(value)

    dest_file_obj.write(struct.pack('<Q', total_length))
    print(dest_file_obj.tell())
    dest_file_obj.write(struct.pack('<Q', APK_SIG_BLOCK_MAGIC_LO))
    print(dest_file_obj.tell())
    dest_file_obj.write(struct.pack('<Q', APK_SIG_BLOCK_MAGIC_HI))
    print(dest_file_obj.tell())

    dest_central_dir_offset = dest_file_obj.tell()

    # write central dir
    source_file_obj.seek(source_central_dir_offset)
    dest_file_obj.write(source_file_obj.read(os.path.getsize(source_file_path) - source_central_dir_offset))

    # modify dest file central_dir offset
    dest_file_obj.flush()
    dest_file_obj.seek(os.path.getsize(dest_file_path) - source_comment_length - 6)
    dest_file_obj.write(struct.pack('<I', dest_central_dir_offset))

    dest_file_obj.flush()

    dest_file_obj.close()
    source_file_obj.close()


def complete_v3_sign(source_file_path, dest_file_path):
    """删除v3签名区域"""

    source_comment_length = get_comment_length(source_file_path)
    source_central_dir_offset = find_central_dir_start_offset(source_file_path, source_comment_length)
    source_sig_block_offset, source_sig_block_size = find_apk_signing_block(source_file_path, source_central_dir_offset)
    source_id_value_dict = find_id_value(source_file_path, source_sig_block_offset, source_sig_block_size)

    if not source_id_value_dict.__contains__(V3_SIGN_BLOCK):
        raise RuntimeError("v3 sign not exist")

    if source_id_value_dict.__contains__(COMPLETION_ID):
        raise RuntimeError('completion block exist')

    source_file_obj = open(source_file_path, 'rb')
    dest_file_obj = open(dest_file_path, 'wb+')
    dest_file_obj.write(source_file_obj.read(source_sig_block_offset))

    source_file_obj.seek(0)

    id_value = dict()
    for key, value in list(source_id_value_dict.items()):
        if not id_value.__contains__(key):
            print('add source apk key : %x ' % key)
            start_position = value[0]
            end_position = value[1]
            size = end_position - start_position
            source_file_obj.seek(start_position)
            id_value[key] = source_file_obj.read(size)

    total_length = 24
    for key, value in list(id_value.items()):
        item_value_length = len(value)
        print(type(value))
        print('item # %x size %d ' % (key, item_value_length))
        total_length += 12 + item_value_length
    print('total size %d   %x' % (total_length, total_length))

    completion_block_size = 4096 - (total_length + 8) % 4096 - 12

    print('completion block size : %s' % completion_block_size)
    completion_block_str = ''
    for i in range(completion_block_size):
        completion_block_str += '1'
    id_value[COMPLETION_ID] = completion_block_str
    total_length += completion_block_size + 12

    dest_file_obj.write(struct.pack('<Q', total_length))

    for key, value in list(id_value.items()):
        item_value_length = len(value)
        item_length = item_value_length + 4
        dest_file_obj.write(struct.pack('<Q', item_length))
        dest_file_obj.write(struct.pack('<I', key))
        if isinstance(value, str):
            dest_file_obj.write(value.encode('utf-8'))
        else:
            dest_file_obj.write(value)

    dest_file_obj.write(struct.pack('<Q', total_length))
    print(dest_file_obj.tell())
    dest_file_obj.write(struct.pack('<Q', APK_SIG_BLOCK_MAGIC_LO))
    print(dest_file_obj.tell())
    dest_file_obj.write(struct.pack('<Q', APK_SIG_BLOCK_MAGIC_HI))
    print(dest_file_obj.tell())

    dest_central_dir_offset = dest_file_obj.tell()

    # write central dir
    source_file_obj.seek(source_central_dir_offset)
    dest_file_obj.write(source_file_obj.read(os.path.getsize(source_file_path) - source_central_dir_offset))

    # modify dest file central_dir offset
    dest_file_obj.flush()
    dest_file_obj.seek(os.path.getsize(dest_file_path) - source_comment_length - 6)
    dest_file_obj.write(struct.pack('<I', dest_central_dir_offset))

    dest_file_obj.flush()

    dest_file_obj.close()
    source_file_obj.close()


def find_id_value(file_path, apk_sig_block_offset, apk_sig_block_length):
    """
    OFFSET DATA  TYPE       DESCRIPTION
    * @+0  bytes uint64:    size in bytes (excluding this field)
    * @+8  bytes payload
    * @-24 bytes uint64:    size in bytes (same as the one above)
    * @-16 bytes uint128:   magic
    """
    file_obj = open(file_path, 'rb')
    pairs_start_offset = apk_sig_block_offset + 8
    pairs_end_offset = apk_sig_block_offset + apk_sig_block_length - 24
    # print 'pairs start : %s end : %s' % (pairs_start_offset, pairs_end_offset)
    id_value_dict = {}  # id : [value_start, value_end]
    entry_count = 0
    file_obj.seek(pairs_start_offset)
    remaining = pairs_end_offset - file_obj.tell()
    while remaining > 0:
        entry_count += 1
        assert remaining >= 8, 'Insufficient data to read size of APK Signing Block entry # %s' % entry_count
        pair_length = struct.unpack('<Q', file_obj.read(8))[0]
        assert 4 <= pair_length <= 0x7fffffff, "APK Signing Block entry # %s size out of range: %s" % (
        entry_count, pair_length)
        next_entry_position = file_obj.tell() + pair_length
        assert next_entry_position <= pairs_end_offset, \
            'APK Signing Block entry # %s size out of range: %s, available: %s' % \
            (entry_count, pair_length, pairs_end_offset - file_obj.tell())
        pair_id = struct.unpack('<I', file_obj.read(4))[0]
        id_value_dict[pair_id] = (file_obj.tell(), next_entry_position)
        file_obj.seek(next_entry_position)
        remaining = pairs_end_offset - file_obj.tell()
    return id_value_dict


def find_apk_signing_block(file_path, central_dir_offset):
    """
    :param file_path:
    :param central_dir_offset:
    :return:
    OFFSET DATA  TYPE       DESCRIPTION
    * @+0  bytes uint64:    size in bytes (excluding this field)
    * @+8  bytes payload
    * @-24 bytes uint64:    size in bytes (same as the one above)
    * @-16 bytes uint128:   magic
    """
    assert central_dir_offset >= APK_SIG_BLOCK_MIN_SIZE, \
        "APK too small for APK Signing Block. ZIP Central Directory offset: %s" % central_dir_offset
    archive_size = os.path.getsize(file_path)
    file_obj = open(file_path, 'rb')

    file_obj.seek(central_dir_offset - 16)
    # apk 二进制为小段存储
    magic_low = struct.unpack('<Q', file_obj.read(8))[0]
    magic_high = struct.unpack('<Q', file_obj.read(8))[0]
    assert magic_high == APK_SIG_BLOCK_MAGIC_HI and magic_low == APK_SIG_BLOCK_MAGIC_LO, \
        "No APK Signing Block before ZIP Central Directory"

    file_obj.seek(central_dir_offset - 24)
    apk_sig_block_size_in_footer = struct.unpack('<Q', file_obj.read(8))[0]
    # print 'apk sig block size : %s' % apk_sig_block_size_in_footer

    total_size = apk_sig_block_size_in_footer + 8
    apk_sig_block_offset = central_dir_offset - total_size

    file_obj.seek(apk_sig_block_offset)
    apk_sig_block_size_in_header = struct.unpack('<Q', file_obj.read(8))[0]

    assert apk_sig_block_size_in_footer == apk_sig_block_size_in_header, \
        "APK Signing Block sizes in header and footer do not match: %s vs %s" % \
        (apk_sig_block_size_in_header, apk_sig_block_size_in_footer)
    # print "apk siging block offset : %s length : %s" % (central_dir_offset - total_size, total_size)
    file_obj.close()
    return central_dir_offset - total_size, total_size


def find_central_dir_start_offset(file_path, comment_length):
    """
    End of central directory record (EOCD)
    Offset    Bytes     Description[23]
    0           4       End of central directory signature = 0x06054b50
    4           2       Number of this disk
    6           2       Disk where central directory starts
    8           2       Number of central directory records on this disk
    10          2       Total number of central directory records
    12          4       Size of central directory (bytes)
    16          4       Offset of start of central directory, relative to start of archive
    20          2       Comment length (n)
    22          n       Comment
    For a zip with no archive comment, the
    end-of-central-directory record will be 22 bytes long, so
    we expect to find the EOCD marker 22 bytes from the end.
    """
    archive_size = os.path.getsize(file_path)
    file_obj = open(file_path, 'rb')
    file_obj.seek(archive_size - comment_length - 6)
    offset = struct.unpack('<I', file_obj.read(4))[0]
    # print 'central dir start offset : %s' % offset
    file_obj.close()
    return offset


def get_comment_length(file_path):
    """
    End of central directory record (EOCD)
    Offset    Bytes     Description[23]
    0           4       End of central directory signature = 0x06054b50
    4           2       Number of this disk
    6           2       Disk where central directory starts
    8           2       Number of central directory records on this disk
    10          2       Total number of central directory records
    12          4       Size of central directory (bytes)
    16          4       Offset of start of central directory, relative to start of archive
    20          2       Comment length (n)
    22          n       Comment
    For a zip with no archive comment, the
    end-of-central-directory record will be 22 bytes long, so
    we expect to find the EOCD marker 22 bytes from the end.
    """

    file_obj = open(file_path, 'rb')
    archive_size = os.path.getsize(file_path)
    # print archive_size
    assert archive_size >= ZIP_EOCD_REC_MIN_SIZE, "APK too small for ZIP End of Central Directory (EOCD) record"
    max_comment_length = min(archive_size - ZIP_EOCD_REC_MIN_SIZE, UINT16_MAX_VALUE)
    eocd_with_empty_comment_position = archive_size - ZIP_EOCD_REC_MIN_SIZE
    for expected_comment_length in range(0, max_comment_length):
        eocd_position = eocd_with_empty_comment_position - expected_comment_length
        file_obj.seek(eocd_position)
        if struct.unpack('<I', file_obj.read(4))[0] == ZIP_EOCD_REC_SIG:
            file_obj.seek(eocd_position + ZIP_EOCD_COMMENT_LENGTH_FIELD_OFFSET)
            actual_comment_length = struct.unpack('h', file_obj.read(2))[0]
            if actual_comment_length == expected_comment_length:
                file_obj.close()
                # print 'comment length = %s' % expected_comment_length
                return expected_comment_length
    file_obj.close()
    assert 'get comment length error'
