package org.jetbrains.kotlin.gradle.internal.kapt.incremental

import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.io.File
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.io.Serializable
import kotlin.collections.set

object OutputBinDiffTools {
    fun diff(
        binA: File,
        binB: File,
    ) {
        val binAData = ClasspathEntryData.ClasspathEntrySerializer.loadFrom(binA)
        val binBData = ClasspathEntryData.ClasspathEntrySerializer.loadFrom(binB)

        val (changed, missingInMap2, missingInMap1) = compareByteArrayMaps(binAData.classAbiHash, binBData.classAbiHash)
        println("Changed entries:")
        changed.forEach { (key, value) ->
            println("$key: ${value.first.contentToString()} vs ${value.second.contentToString()}")
        }

        println("\nMissing in map2: $missingInMap2")
        println("Missing in map1: $missingInMap1")

    }

    fun compareByteArrayMaps(map1: Map<String, ByteArray>, map2: Map<String, ByteArray>): Triple<Map<String, Pair<ByteArray, ByteArray>>, Set<String>, Set<String>> {
        // 1. 找出相同key但值不同的条目
        val changedEntries = map1.entries
            .filter { (key, value1) ->
                map2[key]?.let { value2 ->
                    !value1.contentEquals(value2)
                } ?: false
            }
            .associate { (key, value1) ->
                key to (value1 to map2.getValue(key))
            }

        // 2. 找出map1中存在但map2中缺失的key
        val missingInMap2 = (map1.keys - map2.keys).toSet()

        // 3. 找出map2中存在但map1中缺失的key
        val missingInMap1 = (map2.keys - map1.keys).toSet()

        return Triple(changedEntries, missingInMap2, missingInMap1)
    }
}


class ClasspathEntryData : Serializable {

    object ClasspathEntrySerializer {
        fun loadFrom(file: File): ClasspathEntryData {
            ObjectInputStream(BufferedInputStream(file.inputStream())).use {
                return it.readObject() as ClasspathEntryData
            }
        }
    }

    @Transient
    var classAbiHash = mutableMapOf<String, ByteArray>()

    @Transient
    var classDependencies = mutableMapOf<String, ClassDependencies>()

    private fun writeObject(output: ObjectOutputStream) {
        // Sort only classDependencies, as all keys in this map are keys of classAbiHash map.
        val sortedClassDependencies =
            classDependencies.toSortedMap()
                .mapValues { ClassDependencies(it.value.abiTypes.sorted(), it.value.privateTypes.sorted()) }

        val names = LinkedHashMap<String, Int>()
        sortedClassDependencies.forEach {
            if (it.key !in names) {
                names[it.key] = names.size
            }
            it.value.abiTypes.forEach { type ->
                if (type !in names) names[type] = names.size
            }
            it.value.privateTypes.forEach { type ->
                if (type !in names) names[type] = names.size
            }
        }

        output.writeInt(names.size)
        names.forEach { (key, value) ->
            output.writeInt(value)
            output.writeUTF(key)
        }

        output.writeInt(classAbiHash.size)
        sortedClassDependencies.forEach { (key, _) ->
            output.writeInt(names[key]!!)
            classAbiHash[key]!!.let {
                output.writeInt(it.size)
                output.write(it)
            }
        }

        output.writeInt(sortedClassDependencies.size)
        sortedClassDependencies.forEach {
            output.writeInt(names[it.key]!!)

            output.writeInt(it.value.abiTypes.size)
            it.value.abiTypes.forEach {
                output.writeInt(names[it]!!)
            }

            output.writeInt(it.value.privateTypes.size)
            it.value.privateTypes.forEach {
                output.writeInt(names[it]!!)
            }
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun readObject(input: ObjectInputStream) {
        val namesSize = input.readInt()
        val names = HashMap<Int, String>(namesSize)
        repeat(namesSize) {
            val classId = input.readInt()
            val classInternalName = input.readUTF()
            names[classId] = classInternalName
        }

        val abiHashesSize = input.readInt()
        classAbiHash = HashMap(abiHashesSize)
        repeat(abiHashesSize) {
            val internalName = names[input.readInt()]!!
            val byteArraySize = input.readInt()
            val hash = ByteArray(byteArraySize)
            repeat(byteArraySize) {
                hash[it] = input.readByte()
            }
            classAbiHash[internalName] = hash
        }

        val dependenciesSize = input.readInt()
        classDependencies = HashMap(dependenciesSize)

        repeat(dependenciesSize) {
            val internalName = names[input.readInt()]!!

            val abiTypesSize = input.readInt()
            val abiTypeNames = HashSet<String>(abiTypesSize)
            repeat(abiTypesSize) {
                abiTypeNames.add(names[input.readInt()]!!)
            }

            val privateTypesSize = input.readInt()
            val privateTypeNames = HashSet<String>(privateTypesSize)
            repeat(privateTypesSize) {
                privateTypeNames.add(names[input.readInt()]!!)
            }

            classDependencies[internalName] = ClassDependencies(abiTypeNames, privateTypeNames)
        }
    }

    fun saveTo(file: File) {
        ObjectOutputStream(BufferedOutputStream(file.outputStream())).use {
            it.writeObject(this)
        }
    }
}

class ClassDependencies(val abiTypes: Collection<String>, val privateTypes: Collection<String>)