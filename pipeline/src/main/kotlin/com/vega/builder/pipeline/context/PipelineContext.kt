package com.vega.builder.pipeline.context

import com.vega.builder.common.logger.formatGreen
import com.vega.builder.common.logger.logger
import com.vega.builder.common.utils.getenvSafe
import org.eclipse.jgit.api.Git
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date


class PipelineContextImpl(
    val workspace: String,
    val buildId: String = EMPTY_BUILD_ID,
) : KoinComponent {
    companion object {
        const val EMPTY_BUILD_ID = "empty_build_id"
    }

    private val workspaceParams: WorkspaceParams by inject()
    private val buildParams: BuildParams by inject()

    /**
     * 是否为TTP环境运行
     */
    val isTTP: Boolean by lazy {
        getenvSafe("TTP_BUILD_ENV", "false") != "false"
    }

    /**
     * 是否由组件平台触发
     */
    val isComponentBuild: Boolean by lazy {
        getenvSafe("history_id", "false") != "false"
    }

    val appId: String
        get() {
            return when (buildParams.buildTarget) {
                TargetInfo.Dreamina -> "com.lemon.lv.rotated"
                else -> buildParams.buildTarget.packageName
            }
        }

    val commitId: String by lazy {
        commitFullId.substring(0, 7)
    }

    val commitFullId: String by lazy {
        val git = Git.open(File(workspaceParams.targetProjectDir))
        git.repository.resolve("HEAD").name
    }

// eg.：lv_update_144001600_20240725_13_05_05_58edbf2_VideoCut-prod-release.apk

    val finalTargetName: String by lazy {
        listOfNotNull(
            buildParams.buildTarget.value,
            buildParams.channel,
            buildParams.version,
            SimpleDateFormat("yyyyMMdd_HHmmss").format(Date()),
            commitId,
            if (buildParams.isDebug) "debug" else "release",
            buildParams["NAME_SUFFIX"]
        ).filter { it.isNotBlank() }.joinToString("_")
    }


    /**
     * default apk name
     */
    val defaultApkName: String
        get() {
            val target = buildParams.buildTarget
            val moduleName = target.moduleName.split(":").last { it.isNotBlank() }
            return if (buildParams.withAab) {
                "$moduleName-64"
            } else if (buildParams.removeFlavor) {
                "$moduleName-${if (buildParams.isDebug) "debug" else "release"}"
            } else {
                "$moduleName-${target.flavor}-${if (buildParams.isDebug) "debug" else "release"}"
            }
        }


    val apkNames: List<String>
        get() {
            val apks = mutableListOf("${defaultApkName}.apk")
            if (buildParams.withAab) {
                val target = buildParams.buildTarget
                val moduleName = target.moduleName.split(":").last { it.isNotBlank() }
                apks.add("$moduleName-universal.apk")
            }
            return apks
        }

}

fun createPipelineContext(): PipelineContextImpl {
    val workspace = getenvSafe("WORKSPACE", "")
    PipelineContextImpl.logger().info("workspace: $workspace".formatGreen())

    val buildId = getenvSafe("BUILD_ID", "\$BUILD_ID")
    if (buildId.isNotEmpty() && buildId != "\$BUILD_ID") {
        return PipelineContextImpl(workspace, buildId)
    }
    return PipelineContextImpl(workspace)
}