package com.vega.builder.pipeline.utils
fun main() {
    val regex = """set\s*\(\s*\w+\s+["']?([0-9]+(?:\.[0-9]+)*(?:\.[0-9a-zA-Z]+)*)["']?\s+CACHE\s+INTERNAL""".toRegex()

    val testCases = listOf(
        "set(lv_videoeditor ******** CACHE INTERNAL \"\")",
        "set(cc_videoeditor ******** CACHE INTERNAL \"\")",
        "set(QT_WIN_DEFAULT_VERSION \"6.2.2.536\" CACHE INTERNAL \"\")",
        "set(QT_MAC_DEFAULT_VERSION \"6.2.2.442\" CACHE INTERNAL \"\")",
        "set(lv_openplugin_src ******* CACHE INTERNAL \"\")",
        "# This is a comment",
        "set(lv_videoeditor_win \"\" CACHE INTERNAL \"\")",
    )

    testCases.forEach { testCase ->
        val matchResult = regex.find(testCase)
        val version = if (matchResult != null) {
            matchResult.groupValues[1]
        } else {
            ""
        }
        println("Input: $testCase")
        println("Extracted version: '$version'")
        println("---")
    }
}
