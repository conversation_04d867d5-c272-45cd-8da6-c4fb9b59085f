package com.vega.builder.pipeline.context

enum class TargetInfo(
    val value: String,
    val isOversea: Boolean,
    val aid: Int,
    val gitlabId: Int,
    var flavor: String,
    val moduleName: String,
    val modulePath: String,
    val packageName: String,
) {
    Lv("lv", false, 1775, 40279, "prod", "VideoCut", "VideoCut", "com.lemon.lv"),
    Cc("cc", true, 3006, 825927, "oversea", "CapCut", "CapCut", "com.lemon.lvoverseas"),
    <PERSON><PERSON>("dreamina", false, 581595, 40279, "prod", ":apps:dreamina", "apps/dreamina", "com.bytedance.dreamina"),
    DreaminaOversea("dreaminaoversea", true, 791253, 40279, "oversea", ":apps:dreamina", "apps/dreamina", "com.lemon.dreamina"),
    Commercepro(
        "commercepro",
        false,
        8700,
        40279,
        "prod",
        ":apps:commercepro",
        "apps/commercepro",
        "com.commercepro.and"
    ),
    TinyCut("tinycut", false, 8702, 40279, "prod", ":apps:tinycut", "apps/tinycut", "com.lemon.tinycut");

    companion object {

        /**
         * value [lv/Dreamina]、
         * from BUILD_TARGET
         */
        fun valueOf(
            value: String,
            oversea: Boolean,
        ): TargetInfo {
            val result = when {
                value == "lv" && !oversea -> return Lv
                value == "lv" && oversea -> return Cc
                value == "cc_us" && oversea -> return Cc
                value == Dreamina.value -> return Dreamina
                value == DreaminaOversea.value -> return DreaminaOversea
                value == Commercepro.value -> return Commercepro
                value == TinyCut.value -> return TinyCut
                else -> Lv
            }
            return result
        }
    }
}
