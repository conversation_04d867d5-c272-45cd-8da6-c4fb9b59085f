项目结构

- common 基础库，用于运行脚本工程的基础代码
- pipeline 构建相关的任务，用于构建构建任务，产出为独立Jar
- build-logic 本项目的构建逻辑，用于编译出独立的可执行的java程序

## 添加可执行的文件的方式
1. 在项目中应用插件  alias(libs.plugins.build.shell)
2. 配置可执行文件的相关信息
```kotlin
plugins {
    alias(libs.plugins.build.shell)
    // ... 项目其他的插件
}
standalone {
    mainClass = "com.vega.builder.pipeline.MainKt"
    baseName = "pipeline"
}
```

提交代码会自动触发发布
https://bits.bytedance.net/devops/1499128834/pipeline/pipeline/history?appId=177502&configId=8817&devops_space_old_id=177502&devops_space_type=client&pageNum=1&pageSize=10

bits pipeline中job之间传递参数插件 https://bits.bytedance.net/devops_open/market/plugin/detail?id=56317&iid=5
