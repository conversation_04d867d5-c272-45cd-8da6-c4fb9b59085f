package com.vega.builder.source.manager.cli.module

import com.google.gson.annotations.SerializedName

data class BytebusComponentInfo(
    @SerializedName("bits_log_url")
    val bitsLogUrl: String,
    @SerializedName("buildResult")
    val buildResult: Int,
    @SerializedName("callBackUrl")
    val callBackUrl: String,
    @SerializedName("componentList")
    val componentList: List<Component>,
    @SerializedName("dependency_lock")
    val dependencyLock: String,
    @SerializedName("jobName")
    val jobName: String,
    @SerializedName("mainGit")
    val mainGit: String,
    @SerializedName("mainGitBranch")
    val mainGitBranch: String,
    @SerializedName("mainGitSourceBranch")
    val mainGitSourceBranch: String,
    @SerializedName("mainGitTargetBranch")
    val mainGitTargetBranch: String,
    @SerializedName("mainMrIid")
    val mainMrIid: Int,
    @SerializedName("mainProjectId")
    val mainProjectId: Int,
    @SerializedName("release_before_merge")
    val releaseBeforeMerge: Boolean,
    @SerializedName("type")
    val type: String,
    @SerializedName("userName")
    val userName: String
)

data class Component(
    @SerializedName("androidExtInfo")
    val androidExtInfo: String,
    @SerializedName("appId")
    val appId: Int,
    @SerializedName("artifactId")
    val artifactId: String,
    @SerializedName("branch")
    val branch: String,
    @SerializedName("changeLog")
    val changeLog: String,
    @SerializedName("committedDate")
    val committedDate: Long,
    @SerializedName("extra_info")
    val extraInfo: ExtraInfo,
    @SerializedName("git")
    val git: String,
    @SerializedName("groupId")
    val groupId: String,
    @SerializedName("historyId")
    val historyId: Long,
    @SerializedName("moduleName")
    val moduleName: String,
    @SerializedName("mrIid")
    val mrIid: Int,
    @SerializedName("origin_commit_id")
    val originCommitId: String,
    @SerializedName("projectId")
    val projectId: Int,
    @SerializedName("repoId")
    val repoId: Int,
    @SerializedName("repository")
    val repository: String,
    @SerializedName("repositorySnapshot")
    val repositorySnapshot: String,
    @SerializedName("sourceBranch")
    val sourceBranch: String,
    @SerializedName("targetBranch")
    val targetBranch: String,
    @SerializedName("upgrade")
    val upgrade: Boolean,
    @SerializedName("uploadSource")
    val uploadSource: Boolean,
    @SerializedName("useSource")
    val useSource: Any,
    @SerializedName("version")
    val version: String,
    @SerializedName("workflow_job_id")
    val workflowJobId: Int
)

data class ExtraInfo(
    @SerializedName("upgrade_key")
    val upgradeKey: String
)


