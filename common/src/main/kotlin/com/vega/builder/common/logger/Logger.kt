package com.vega.builder.common.logger

import ch.qos.logback.classic.LoggerContext
import ch.qos.logback.core.util.StatusPrinter
import org.slf4j.Logger
import org.slf4j.LoggerFactory

object DefaultLogger {}

fun logger(): Logger = DefaultLogger.logger()

inline fun <reified T> T.logger(): Logger = LoggerFactory.getLogger(T::class.java)

private const val ANSI_RESET = "\u001B[0m"
private const val ANSI_RED = "\u001B[31m"
private const val ANSI_GREEN = "\u001B[32m"
private const val ANSI_YELLOW = "\u001B[33m"
private const val ANSI_BLUE = "\u001B[34m"

fun String.formatRed(): String = "$ANSI_RED$this$ANSI_RESET"

fun String.formatGreen(): String = "$ANSI_GREEN$this$ANSI_RESET"

fun String.formatYellow(): String = "$ANSI_YELLOW$this$ANSI_RESET"

fun String.formatBlue(): String = "$ANSI_BLUE$this$ANSI_RESET"

fun configureLogback() {
// 创建一个 Logger 上下文
    val loggerContext = LoggerFactory.getILoggerFactory() as LoggerContext
    StatusPrinter.print(loggerContext)
//    // 创建一个控制台输出器
//    val consoleAppender = ConsoleAppender<ILoggingEvent>()
//    consoleAppender.context = loggerContext
//
//    // 创建一个模式布局编码器
//    val encoder = PatternLayoutEncoder()
//    encoder.context = loggerContext
//    encoder.pattern = "%highlight(%d{HH:mm:ss} %level %logger{10} - %msg)\n"
//    encoder.start()
//
//    // 将编码器设置到输出器
//    consoleAppender.encoder = encoder
//    consoleAppender.start()
//
//    // 获取根 Logger 并配置
//    val rootLogger = LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME) as ch.qos.logback.classic.Logger
//    rootLogger.level = Level.INFO
//    rootLogger.iteratorForAppenders().forEach {
//        rootLogger.detachAppender(it)
//    }
//    val logger = loggerContext.getLogger(DefaultTransport::class.java)
//    logger.level = Level.WARN
//    logger.addAppender(consoleAppender)
//    rootLogger.addAppender(consoleAppender)
}
