package com.vega.builder.tools.cli.dagger

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.File

object DaggerManager {


    private fun buildDependencyGraphAndCalculateScopes(
        modules: List<ModelInfo>,
        binds: List<BindInfo>,
        providers: List<ProvidersInfo>
    ) {
        // 创建快速查找结构
        val modelMap = modules.associateBy { it.name.fullName() }.toMutableMap()
        val providerMap = providers.associateBy { it.returnType.fullName() }.toMutableMap()

        // 第一步：处理 Bind 的作用域覆盖
        binds.filter { it.daggerScope == DaggerScope.Activity }.forEach { bind ->
            val implKey = bind.className.fullName()

            // 更新 Model 的作用域
            modelMap[implKey]?.let { model ->
                modules.find { it.name.fullName() == implKey }?.let {
                    it.daggerScope = DaggerScope.Activity
                }
            }

            // 更新 Provider 的作用域
            providerMap[implKey]?.let { provider ->
                providers.find { it.returnType.fullName() == implKey }?.let {
                    it.daggerScope = DaggerScope.Activity
                }
            }
        }

        // 第二步：构建合并后的数据视图
        val mergedMap = mutableMapOf<String, DaggerScope>().apply {
            // 合并 Model 作用域
            modules.forEach { put(it.name.fullName(), it.daggerScope) }
            // 合并 Provider 作用域
            providers.forEach { put(it.returnType.fullName(), it.daggerScope) }
            // 合并 Bind 作用域（覆盖现有值）
            binds.forEach { put(it.className.fullName(), it.daggerScope) }
            binds.forEach { put(it.interfaceName.fullName(), it.daggerScope) }
        }

        // 第三步：递归检测函数
        fun hasActivityInChain(type: TypeInfo, visited: MutableSet<String> = mutableSetOf()): Boolean {
            val typeKey = type.fullName()
            if (typeKey in visited) return false
            visited.add(typeKey)

            // 直接检测当前类型作用域
            if (mergedMap[typeKey] == DaggerScope.Activity) return true

            // 检测参数依赖
            return when {
                modelMap.containsKey(typeKey) -> modelMap[typeKey]!!.params.any {
                    hasActivityInChain(it.type, visited)
                }

                providerMap.containsKey(typeKey) -> providerMap[typeKey]!!.paramsInfoList.any {
                    hasActivityInChain(it.type, visited)
                }

                else -> false
            }
        }

        // 第四步：计算结果
        modules.forEach { module ->
            if (module.daggerScope == DaggerScope.Activity) {
                module.hasScopeChile = true
                return@forEach
            }
            module.hasScopeChile = module.params.any { hasActivityInChain(it.type) }
        }

        providers.forEach { provider ->
            if (provider.daggerScope == DaggerScope.Activity) {
                provider.hasScopeChile = true
                return@forEach
            }
            provider.hasScopeChile = provider.paramsInfoList.any { hasActivityInChain(it.type) }
        }
    }

    fun collectAndGenerateKoinConfig(projectPath: String) {
        val cache = File("/Users/<USER>/develop/bytedance/build-cli/tools-cli/output/model-cache.json")
        val result = if (cache.exists()) {
            Gson().fromJson<List<ModuleInfo>>(cache.readText(), object : TypeToken<List<ModuleInfo>>() {}.type)
        } else {
            DaggerCollector.collect("/Users/<USER>/develop/bytedance/vega").apply {
                cache.writeText(Gson().toJson(this))
            }
        }
        val bindList = result.map {
            it.kotlinFiles.map { it.bindInfoList }.flatten()
        }.flatten()
        val allBindInfoList =
            bindList + result.map { it.kotlinFiles.map { it.viewModelBindInfoList }.flatten() }.flatten()
        val allModel = result.map { it.kotlinFiles }.flatten().map { it.modelInfoList }.flatten()
        val allProvider = result.map { it.kotlinFiles }.flatten().map { it.providerList }.flatten()
        buildDependencyGraphAndCalculateScopes(allModel, allBindInfoList, allProvider)
        for (moduleInfo in result) {
            KoinConfigGenerated.generate(moduleInfo, bindList)
        }
        val allClassFile = result.map { it.kotlinFiles }.flatten()

        allClassFile.groupBy { it.flavor }.forEach { (flavor, files) ->
            println(files.map { it.classInfoList }.flatten().sortedBy { it.name.fullName() }
                .filter { it.hasDaggerPropertyInject }.joinToString("\n") {
                    "${it.name.fullName()},${flavor},false,false"
                })
        }
    }
}