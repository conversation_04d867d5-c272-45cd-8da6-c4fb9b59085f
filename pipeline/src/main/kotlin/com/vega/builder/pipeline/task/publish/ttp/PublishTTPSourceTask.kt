package com.vega.builder.pipeline.task.publish.ttp

import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.IJFrogApi
import com.vega.builder.common.network.api.IBytedanceAndroidApi
import com.vega.builder.common.network.request
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.task.publish.PublishSourceTask.Companion.SOURCE_FILE_EXTENSION
import com.vega.builder.pipeline.task.publish.PublishUtils
import com.vega.builder.pipeline.task.publish.service.CompressionRepoService
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.ResponseBody
import org.koin.core.component.inject
import retrofit2.Response
import java.io.File


/**
 * 压缩，上传到JFrog，国内Wrapper下载，上传到国内的TOS
 */
@TaskDefinition("PublishTask", stage = Stage.Build, "TTP Source Publish")
class PublishTTPSourceTask : PipelineTask() {
    val workspaceParams by inject<WorkspaceParams>()
    val buildParams by inject<BuildParams>()


    override suspend fun run() {
        val repoFile = File(workspaceParams.targetProjectDir)

        val version = getenvSafe("version", "")
        val mavenId = buildParams["MAVEN_ID"]
        if (version.isEmpty()) {
            logger().error("version is empty")
            throw PipelineThrowable(ErrorType.TTP_JOB_PUBLISH_SOURCE_VERSION_EMPTY_FAILED)
        }
        if (mavenId.isNullOrBlank()) {
            logger().error("mavenId is empty")
            throw PipelineThrowable(ErrorType.TTP_JOB_PUBLISH_SOURCE_MAVEN_EMPTY_FAILED)
        }
        val sourcePackageFile = CompressionRepoService(repoFile.parentFile, repoFile.name).compressionRepo()
        val uploadKey = PublishUtils.buildTosId(mavenId, version)

        val result = request(
            IBytedanceAndroidApi::upload,
            "${TosConfig.LvBuildArtifact.bucket}/${uploadKey}",
            sourcePackageFile.asRequestBody("application/octet-stream".toMediaTypeOrNull())
        )
        if (result.isSuccessful) {
            logger().info("Upload $uploadKey[${sourcePackageFile.absolutePath}] to jfrog success")
            logger().info("result: ${result.code()},body: ${result.body()?.string() ?: "no body"}")
        } else {
            logger().error("upload $uploadKey[${sourcePackageFile.absolutePath}] to jfrog failed")
            logger().error("result: ${result.code()},body: ${result.body()?.string() ?: "no body"}")
            throw PipelineThrowable(ErrorType.TTP_JOB_PUBLISH_SOURCE_UPLOAD_FAILED)
        }
    }
}

