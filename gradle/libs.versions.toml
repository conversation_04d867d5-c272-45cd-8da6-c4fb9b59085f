[versions]
ksp = "2.0.0-1.0.21"
kotlin = "2.0.10"
kotlinx-io = "0.5.1"
kotlinx-coroutines = "1.9.0-RC"
kotlinx-serialization-json = "1.7.1"
kotlinx-datetime = "0.6.0"
kotlinx-cli = "0.3.6"
okio = "3.9.0"
retrofit = "2.11.0"
okhttp = "4.12.0"
jgit = "6.7.0.202309050840-r"
gson = "2.11.0"
slf4j = "1.7.32"
logback = "1.4.4"
tos-core = "2.0.0-tiktok"
clikt = "5.0.1"
koin = "4.1.0-Beta5"
koin-annotations = "2.0.0-RC5"
source-manager-core="0.0.1-alpha.210-cc-ttp"
kotlin-compiler-embeddable="2.0.10"

[libraries]
kotlinx-io = { module = "org.jetbrains.kotlinx:kotlinx-io-core", version.ref = "kotlinx-io" }
kotlinx-coroutines = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinx-coroutines" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinx-serialization-json" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinx-datetime" }
kotlinx-cli = { module = "org.jetbrains.kotlinx:kotlinx-cli", version.ref = "kotlinx-cli" }
okio = { module = "com.squareup.okio:okio", version.ref = "okio" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit-converter-kotlinx-serialization = { module = "com.squareup.retrofit2:converter-kotlinx-serialization", version.ref = "retrofit" }
retrofit-converter-converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
okhttp-logger = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "okhttp" }
jgit = { module = "org.eclipse.jgit:org.eclipse.jgit", version.ref = "jgit" }
jgit-ssh = { module = "org.eclipse.jgit:org.eclipse.jgit.ssh.jsch", version.ref = "jgit" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
slf4j = { module = "org.slf4j:slf4j-api", version.ref = "slf4j" }
logback = { module = "ch.qos.logback:logback-classic", version.ref = "logback" }
tos-core = { module = "com.bytedance.storage:tos-core", version.ref = "tos-core" }
clikt = { module = "com.github.ajalt.clikt:clikt", version.ref = "clikt" }

koin-core = { module = "io.insert-koin:koin-core", version.ref = "koin" }
koin-logger-slf4j = { module = "io.insert-koin:koin-logger-slf4j", version.ref = "koin" }
koin-annotations = { module = "io.insert-koin:koin-annotations", version.ref = "koin-annotations" }
source-manager-core={module="com.lv.builder:source-manager-core",version.ref="source-manager-core"}
kotlin-compiler-embeddable={module="org.jetbrains.kotlin:kotlin-compiler-embeddable",version.ref="kotlin-compiler-embeddable"}

[bundles]
kotlinx-official-libs = [
    "kotlinx-io",
    "kotlinx-coroutines",
    "kotlinx-serialization-json",
    "kotlinx-datetime",
    "kotlinx-cli"
]
retrofit = [
    "retrofit",
    "okhttp",
    "okhttp-logger",
    "retrofit-converter-converter-gson",
    "retrofit-converter-kotlinx-serialization"
]
log = [
    "slf4j",
    "logback"
]
jgit = [
    "jgit",
    "jgit-ssh"
]

koin = [
    "koin-core",
    "koin-logger-slf4j",
    "koin-annotations"
]


[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlinx-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
kotlin-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
build-shell = { id = "com.vega.builder.build-logic.shell", version = "unspecified" }
