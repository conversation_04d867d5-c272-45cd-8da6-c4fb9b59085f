package com.vega.builder.pipeline.task.publish.service

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.ITiktokMavenApi
import com.vega.builder.common.network.request
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.tos.slicePutObject
import com.vega.builder.common.utils.BitsUtils
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.md5
import com.vega.builder.common.utils.retry
import com.vega.builder.pipeline.BuildConfig
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.task.artifacts.ArtifactsUploadResult
import com.vega.builder.pipeline.task.artifacts.SYMBOL_UPLOAD_KEY_PREFIX
import com.vega.builder.pipeline.task.artifacts.SymbolArtifactInfo
import com.vega.builder.pipeline.task.artifacts.TTPArtifactsUpload
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import java.io.File
import kotlin.io.path.createTempFile

object TTPArtifactsService {
    val taskId by lazy {
        getenvSafe("TASK_ID") ?: System.currentTimeMillis()
    }
    suspend fun processArtifacts(jobId: String) {
        val uploadBaseKey = "${TTPArtifactsUpload.UPLOAD_KEY_PREFIX}${jobId}"
        val manifest = retry(3, 50) {
            val result =
                request(ITiktokMavenApi::download, "${uploadBaseKey}/manifest.json")
            if (result.isSuccessful) {
                val manifestContent = result.body()?.string() ?: throw Exception("Failed to download artifact manifest")
                Gson().fromJson<List<ArtifactsUploadResult>>(manifestContent)
            } else {
                throw Exception("Failed to download artifact manifest")
            }
        }
        val finalArtifactsResult = manifest.map {
            CoroutineScope(Dispatchers.IO).async {
                downloadAndUploadToTos(it)
            }
        }.awaitAll()
        BitsUtils.setEnv("artifacts_output", Gson().toJson(finalArtifactsResult))
    }

    private suspend fun downloadAndUploadToTos(
        artifactsUploadResult: ArtifactsUploadResult
    ): ArtifactsUploadResult {
        val file = retry(3, 50) {
            val tempFile = kotlin.io.path.createTempFile().toFile()

            val result =
                request(ITiktokMavenApi::download, artifactsUploadResult.url)
            if (result.isSuccessful) {
                result.body()?.byteStream()?.use { inputStream ->
                    tempFile.outputStream().use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                if (tempFile.md5() != artifactsUploadResult.md5) {
                    throw Exception("Failed to download artifact file error ${artifactsUploadResult.url}")
                }
            } else {
                throw Exception("Failed to download artifact file error ${artifactsUploadResult.url}")
            }
            tempFile
        }

        val resultFileName = "ttp_${artifactsUploadResult.name}"
        val remoteName = listOfNotNull("pipeline_result", taskId, resultFileName).joinToString("/")
        val tosClient = TosConfig.LvBuildResult.createTosClient()
        if (tosClient.slicePutObject(remoteName, file)) {
            return artifactsUploadResult.copy(
                name = resultFileName,
                url = "https://${BuildConfig.hide_voffline_rul}/download/tos/schedule/${TosConfig.LvBuildResult.bucket}/${remoteName}"
            )
        } else {
            throw Exception("Upload file[${file.name}] error!")
        }
    }


    suspend fun processSymbolArtifacts(jobId: String) {
        val uploadBaseKey = "${SYMBOL_UPLOAD_KEY_PREFIX}${jobId}"
        val manifest = retry(3, 50) {
            val result =
                request(ITiktokMavenApi::download, "${uploadBaseKey}/manifest.json")
            if (result.isSuccessful) {
                val manifestContent = result.body()?.string() ?: throw Exception("Failed to download artifact manifest")
                Gson().fromJson<List<SymbolArtifactInfo>>(manifestContent)
            } else {
                throw Exception("Failed to download artifact manifest")
            }
        }
        val finalArtifactsResult = manifest.mapNotNull { artifactInfo ->
            if (artifactInfo.success) {
                CoroutineScope(Dispatchers.IO).async {
                    val tempFile = createTempFile(prefix = artifactInfo.name, suffix = ".so").toFile()
                    retry(3, 50) {
                        val result =
                            request(ITiktokMavenApi::download, artifactInfo.path)
                        if (result.isSuccessful) {
                            result.body()?.byteStream()?.use { inputStream ->
                                tempFile.outputStream().use { outputStream ->
                                    inputStream.copyTo(outputStream)
                                }
                            }
                            if (tempFile.md5() != artifactInfo.md5) {
                                throw Exception("Failed to download Symbol file md5 not matched ${artifactInfo.path} ")
                            }
                        } else {
                            throw Exception("Failed to download Symbol file network error ${artifactInfo.path} ")
                        }
                    }
                    tempFile.absolutePath
                }
            } else {
                logger().error("Artifact ${artifactInfo.path} upload error.")
                null
            }
        }.awaitAll()
        if (finalArtifactsResult.isNotEmpty()) {
            val nativePathInfoFile = File(PipelineOutput.buildNativePathInfo)
            if (!nativePathInfoFile.parentFile.exists()) {
                nativePathInfoFile.parentFile.mkdirs()
            }
            nativePathInfoFile.writeText(Gson().toJson(finalArtifactsResult).apply {
                logger().info("native path info written to ${nativePathInfoFile.path},content: $this")
            })
        }
    }
}