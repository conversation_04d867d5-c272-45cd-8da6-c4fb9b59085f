package com.vega.launcher;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;

import com.bytedance.common.utility.concurrent.ThreadPlus;
import com.bytedance.test.codecoverage.CodeCoverageMonitor;
import com.ss.android.common.applog.AppLog;
import com.vega.infrastructure.base.ModuleCommon;
import com.vega.log.BLog;

import java.util.Map;

import static java.lang.Thread.sleep;

public class JacocoUtils {
    private static volatile long sLastGetExternalStorageStateTime = -1;
    private static volatile String sLastExternalStorageState = null;

    private static final String ANDROID_DATA_PACKAGE_PATH = "/Android/data/";
    private static final String JACOCO_TAG = "JacocoUtils_lv";

    private static CodeCoverageMonitor coverageMonitor;
    private static boolean ReleaseMode = true;

    /**
     * Whether the upload coverage data task is initialized
     */
    private static boolean sUploadCoverageDataTaskInited = false;

    /*
        Time interval in seconds, default is 60s for reporting
     */
    private static int TimeInterval = 10;

    /**
     * Initialization status
     * 0: Not initialized, 1: Initialized successfully, 2: Initialization failed
     */
    private static int sHasJacocoUtilsInited = 0;

    /**
     * Environment inspection: check if it's release package and local_test channel
     */
    private static boolean envInspection(){
        return true;
    }

    /**
     * Initialize coverage monitoring, automatically collect instrumented external plugin info (if any)
     */
    private static void jacocoInit(){
        if (!envInspection()){
            // Coverage collection environment check failed, set status to 2
            sHasJacocoUtilsInited = 2;
        }
        if (sHasJacocoUtilsInited == 0) {
            try {
//                Context context = ModuleCommon.INSTANCE.getApplication();
//                String pkgName = context.getPackageName();
//                ApplicationInfo appInfo = context.getPackageManager().getApplicationInfo(pkgName, PackageManager.GET_META_DATA);
//                int versionCode = appInfo.metaData.getInt("UPDATE_VERSION_CODE");
//                BLog.i(JACOCO_TAG, ">>>>jacocoInit>>>>>>"+context.getCacheDir().getAbsolutePath());
                BLog.i(JACOCO_TAG, ">>>>jacocoInit>>>>>>");
//                coverageMonitor = new CodeCoverageMonitor(context.getCacheDir().getAbsolutePath(), ReleaseMode, context, false);
                coverageMonitor = CodeCoverageMonitor.getInstance(ModuleCommon.INSTANCE.getApplication(), AppLog.getServerDeviceId());
                coverageMonitor.startPeriodicUpload();
//                coverageMonitor.setAppVersion(String.valueOf(versionCode)); // Set current app version
//                coverageMonitor.setDeviceID(AppLog.getServerDeviceId()); // Set device ID
                // Environment check passed and monitor initialized successfully
                sHasJacocoUtilsInited = 1;
            } catch (Exception e) {
                BLog.e(JACOCO_TAG, "jacoco init failed: " + e);
            }
        }
    }

    /**
     * Get current monitored coverage external plugins list
     */
    public static Map<String, Integer> getCoveragePluginList(){
        return coverageMonitor.getCoveragePluginList();
    }

    /**
     * Function: Single data write, only for non-main processes to write collected coverage data (e.g. push process)
     * Calling scenario: Called by non-main processes for data writing
     */
    public static void writeCoverageData(){
        BLog.i(JACOCO_TAG, ">>>>writeCoverageData>>>>>>");
        // Initialization protection
        if(sHasJacocoUtilsInited == 0){
            // If not initialized, perform initialization once
            jacocoInit();
        }
        // Channel and probe injection protection
        if (sHasJacocoUtilsInited != 1 || !coverageMonitor.getInstrumentStatus()) {
            // Return if initialization failed or instrumentation failed
            return;
        }
        // Retry getting DID if not obtained
        if(TextUtils.isEmpty(coverageMonitor.getDeviceID())){
            coverageMonitor.setDeviceID(AppLog.getServerDeviceId());
        }
        coverageMonitor.dataWriteNow();
    }

    /**
     * Single code coverage data write-back and file upload
     * Calling scenarios:
     * 1. When app main process switches to background
     * 2. Manual upload in engineering mode
     */
    public static void uploadCoverageFileNow() {
        BLog.i(JACOCO_TAG, ">>>>uploadCoverageFileNow>>>>>>");
        if(sHasJacocoUtilsInited == 0){
            // If not initialized, perform initialization once
            jacocoInit();
        }
        uploadCoverageDataCore(true);
    }

    /**
     * Scheduled code coverage data write-back and file upload
     * Calling scenario: Called during app startup initialization by main process for periodic reporting
     */
    public static void uploadCoverageFileTask() {
        BLog.i(JACOCO_TAG, ">>>>uploadCoverageFileTask>>>>>>"+TimeInterval);
        if(sHasJacocoUtilsInited == 0){
            // If not initialized, perform initialization once
            jacocoInit();
        }
        if (sUploadCoverageDataTaskInited) {
            // Return if task already exists
            return;
        }
        sUploadCoverageDataTaskInited = true;
//        uploadCoverageDataCore(false);
    }

    /**
     * Core method for code coverage data reporting
     */
    private static void uploadCoverageDataCore(final boolean uploadOnce) {
        if (sHasJacocoUtilsInited != 1 || !coverageMonitor.getInstrumentStatus()) {
            // Protection: return if initialization failed or instrumentation failed
            return;
        }
        new ThreadPlus() {
            @Override
            public void run() {
                try {
                    if (uploadOnce){
                        // Immediate code coverage data write-back and upload
                        BLog.i(JACOCO_TAG, "start to upload cov data");
                        if(TextUtils.isEmpty(coverageMonitor.getDeviceID())){
                            coverageMonitor.setDeviceID(AppLog.getServerDeviceId());
                        }
                        coverageMonitor.dataWriteNow();
                        boolean result = coverageMonitor.dataUpload();
                        BLog.i(JACOCO_TAG, "upload cov data once, result: "+result+ ", deviceid: "+coverageMonitor.getDeviceID());
                    } else {
                        while(true) {
                            BLog.i(JACOCO_TAG, "start to upload cov data");
                            if(TextUtils.isEmpty(coverageMonitor.getDeviceID())){
                                coverageMonitor.setDeviceID(AppLog.getServerDeviceId());
                            }
                            // Periodic code coverage data write-back and upload
                            coverageMonitor.dataWriteNow();
                            boolean result = coverageMonitor.dataUpload();
                            BLog.i(JACOCO_TAG, "upload cov data, result: "+result+ ", deviceid: "+coverageMonitor.getDeviceID());
                            if(!coverageMonitor.getInstrumentStatus()){
                                // Additional instrumentation status check - break loop if instrumentation failed
                                break;
                            }
                            // Scheduled reporting every minute
                            try {
                                sleep(TimeInterval * 1000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                } catch (Throwable e) {
                    e.printStackTrace();
                    BLog.e(JACOCO_TAG, "upload cov data error: "+e.getMessage());
                }
            }
        }.start();
    }
}
