/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
  id("org.jetbrains.kotlin.jvm")
}

repositories { mavenCentral() }

dependencies {
  implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.10")
  implementation("com.google.guava:guava:31.0.1-jre")
  implementation("org.jetbrains.kotlin:kotlin-compiler-embeddable:2.0.10")
  testImplementation("org.assertj:assertj-core:2.9.0")
  testImplementation("org.jetbrains.kotlin:kotlin-test:2.0.10")
  testImplementation("org.jetbrains.kotlin:kotlin-test-junit:2.0.10")
}

