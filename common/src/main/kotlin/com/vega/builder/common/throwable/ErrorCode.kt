package com.vega.builder.common.throwable

enum class ErrorType(
    @Deprecated("Use ordinal")
    val code: Int,
    val message: String,
) {
    Success(0, "Success"),
    WorkspaceNotFound(1, "Workspace not found"),
    NetworkUrlNotFound(2, "Request URL not found, please contact developer"),
    SubRepoInfoLoadError(3, "Failed to load sub-repository information"),
    LoadBuildTemplateError(4, "Failed to fetch preset template"),
    ResourceNotFound(5, "Failed to read resource file"),
    GradleBuildError(code = 6, "Gradle build failed"),
    SignError(code = 7, "Signing exception"),
    WriteChannelError(code = 8, "Channel writing exception"),
    ApkNotFound(9, "APK not found"),
    AabNotFound(10, "AAB not found"),
    DexVpmTriggerError(11, "Failed to trigger reinforcement"),
    DexVpmError(12, "Reinforcement failed"),
    InnerError(13, "Internal error"),
    GradleProfilerDownloadError(14, "Failed to download GradleProfiler"),
    GradleProfilerScenariosDownloadError(15, "Failed to download GradleProfiler scenarios"),
    GradleProfilerNotFoundError(16, "GradleProfiler executable not found"),
    GradleProfilerTargetNotFoundError(17, "GradleProfiler target not specified"),
    CommandError(18, "Command execution failed"),
    TtpJobFailed(19, "TTP JOB failed"),
    TtpJobTriggerFailed(19, "Trigger TTP failed"),
    SUB_REPO_PUBLISH_DIST_FAILED(20, "Trigger sub repo publish failed"),
    SUB_REPO_PUBLISH_FAILED(21, "Trigger sub repo publish failed"),
    TTP_JOB_PUBLISH_SOURCE_VERSION_EMPTY_FAILED(22, "TTP JOB version is empty"),
    TTP_JOB_PUBLISH_SOURCE_MAVEN_EMPTY_FAILED(22, "TTP JOB MAVEN_ID is empty"),
    TTP_JOB_PUBLISH_SOURCE_MAVEN_INFO_EMPTY_FAILED(22, "TTP JOB groupId or artifactId is empty"),
    TTP_JOB_PUBLISH_SOURCE_UPLOAD_FAILED(22, "TTP JOB source upload failed"),
    GitBranchNotFound(22, "GitBranch not found"),
    SBomGenerateFailed(23, "SBom gradle generate failed"),
    SBomUploadFailed(24, "SBom upload failed"),
    TTPVersionCheck(24, "TTP Version check failed"),
    Unknown(10086, "Unknown exception")
}
