package com.vega.builder.common.utils

import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.logger.logger
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import java.util.concurrent.TimeUnit

fun String.runCommand(workingDir: File, ignoreError: Boolean = true) {

    val result = runCommandWithResult(workingDir)
    if (!ignoreError && !result) {
        throw PipelineThrowable(ErrorType.CommandError)
    }
}

fun String.runCommandWithResult(workingDir: File): Boolean {
    val commandAndArgs = split(" ", limit = 2)
    val result = Command(commandAndArgs.first())
        .directory(workingDir)
        .apply {
            if (commandAndArgs.size > 1) {
                args(commandAndArgs.last().split(" "))
            }
        }
        .default().spawn().wait()
    return result == 0

}

fun String.runCommandWithOutput(workingDir: File): String {
    val commandAndArgs = split(" ", limit = 2)
    val result = Command(commandAndArgs.first())
        .directory(workingDir)
        .apply {
            if (commandAndArgs.size > 1) {
                args(commandAndArgs.last().split(" "))
            }
        }.output()
    if (result.status == 0) {
        logger().info("run command: ${result.stdout}")
    } else {
        logger().error("run command: ${result.stderr}")
        throw PipelineThrowable(ErrorType.CommandError)
    }
    return result.stdout?.trimEnd('\n') ?: ""
}

fun String.runCommandWithOutputNoException(workingDir: File): String {
    val commandAndArgs = split(" ", limit = 2)
    val result = Command(commandAndArgs.first())
        .directory(workingDir)
        .apply {
            if (commandAndArgs.size > 1) {
                args(commandAndArgs.last().split(" "))
            }
        }.output()
    if (result.status == 0) {
        logger().info("run command: $this, ${result.stdout}")
    } else {
        logger().error("run command: $this, ${result.stderr}")
    }
    return result.stdout?.trimEnd('\n') ?: ""
}

fun String.runCommandWithBackground(workingDir: File) {
    val commandAndArgs = split(" ", limit = 2)
    Command(commandAndArgs.first())
        .directory(workingDir)
        .apply {
            if (commandAndArgs.size > 1) {
                args(commandAndArgs.last().split(" "))
            }
        }
        .default().spawn()
}

fun List<String>.runCommandWithResult(workingDir: File): Boolean {
    println("run command: $this")
    val process =
        ProcessBuilder(*this.toTypedArray()).directory(workingDir).inheritIO()
            .start()
    process.waitFor(30, TimeUnit.MINUTES)
    val reader = BufferedReader(InputStreamReader(process.inputStream))
    var line: String?
    while (reader.readLine().also { line = it } != null) {
        println(line)
    }
    return process.exitValue() == 0
}

// fun Command.default(): Command {
//    stdout(Stdio.Inherit)
//    stderr(Stdio.Inherit)
//    stdin(Stdio.Inherit)
//    Logger.i("Command", "[${debugString()}]")
//    return this
// }
