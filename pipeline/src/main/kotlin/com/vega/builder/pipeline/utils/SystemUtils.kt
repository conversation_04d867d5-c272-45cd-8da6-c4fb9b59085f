package com.vega.builder.pipeline.utils

enum class OSType {
    MAC,
    <PERSON>INDOWS,
    LINUX,
    UNKNOWN
}

object SystemUtils {
    @JvmStatic
    fun getOSType(): OSType {
        val osName = System.getProperty("os.name", "unknownOS").lowercase()
        return when {
            osName.contains("mac") -> OSType.MAC
            osName.contains("linux") -> OSType.LINUX
            osName.contains("win") -> OSType.WINDOWS
            else -> OSType.UNKNOWN
        }
    }

    fun isMac(): <PERSON>olean {
        return getOSType() == OSType.MAC
    }

    fun isLinux(): <PERSON><PERSON>an {
        return getOSType() == OSType.LINUX
    }

    fun isWindows(): <PERSON><PERSON><PERSON> {
        return getOSType() == OSType.WINDOWS
    }
}