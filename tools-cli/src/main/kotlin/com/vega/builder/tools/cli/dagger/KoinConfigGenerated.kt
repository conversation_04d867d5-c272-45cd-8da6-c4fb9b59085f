package com.vega.builder.tools.cli.dagger

import java.io.File
import kotlin.collections.map

object KoinConfigGenerated {
    val bindBlackList = listOf(
        "BaseMviViewModel",
        "JediViewModel",
        "ItemViewModel",
        "Injectable",
        "CoverTextViewModel",
        "KoinComponent",
        "AccountUpdateProxyListener"
    )

    fun modifyKoinModule(content: String, newModules: String): String {
        // Scenario 1: Overridden module property already exists
        val overridePattern =
            """(?s)(override\s+val\s+module\s*:\s*List<Module>\s*\n\s*get\(\)\s*=\s*)([^\n]+)""".toRegex()
        if (overridePattern.containsMatchIn(content)) {
            return content.replace(overridePattern) {
                val (prefix, existing) = it.destructured
                // Append new modules to existing expression
                "$prefix$existing + $newModules"
            }
        }

        // Scenario 2: No overridden module property
        val defaultPattern =
            """(?s)(/\*\*.*?\*/)?\s*(interface|object)\s+(\w+)\s*:\s*IKoinModule\s*\{.*?(\n\s*)(override\s+val\s+spiModule\b.*?)(\n\s*[}])""".toRegex()
        return defaultPattern.replace(content) {
            val (comment, type, module, indent, spiPart, end) = it.destructured
            // Insert new module property after spiModule
            """
            $comment
            $type $module : IKoinModule {
            ${indent}override val module: List<Module>
            $indent    get() = $newModules
            $spiPart$end
            """.trimIndent()
        }
    }

    fun processImportsAndCode(
        originalContent: String,
        newImports: Set<String>,
        codeGenerator: (String) -> String
    ): String {
        // Deconstruct original file structure
        val (packagePart, existingImports, body) = parseFileStructure(originalContent)

        // Merge and process imports
        val processedImports = processImports(existingImports, newImports)

        // Generate new code
        val modifiedBody = codeGenerator(body)

        // Reconstruct file content
        return buildFileContent(packagePart, processedImports, modifiedBody)
    }

    private fun parseFileStructure(content: String): Triple<String, List<String>, String> {
        val lines = content.lineSequence().toList()

        var packagePart = ""
        val imports = mutableListOf<String>()
        val body = mutableListOf<String>()

        var state = 0 // 0: Initial 1: Package processed 2: Imports processed

        lines.forEach { line ->
            when {
                state == 0 && line.startsWith("package ") -> {
                    packagePart = line
                    state = 1
                }

                state <= 1 && line.startsWith("import ") -> {
                    imports.add(line)
                    state = 1
                }

                else -> {
                    if (state == 0 && line.isNotBlank()) state = 2
                    body.add(line)
                }
            }
        }

        return Triple(packagePart, imports, body.joinToString("\n"))
    }

    private fun processImports(existing: List<String>, new: Set<String>): List<String> {
        return (existing + new)
            .map { it.trim() }
            .distinct()
            .sortedWith(
                compareBy(
                    { it.substringBeforeLast('.') }, // Sort by package name
                    { it.substringAfterLast('.') }    // Sort by class name within same package
                ))
            .toList()
    }

    private fun buildFileContent(
        packagePart: String,
        imports: List<String>,
        body: String
    ): String {
        return buildString {
            if (packagePart.isNotEmpty()) {
                appendLine(packagePart)
            }

            if (imports.isNotEmpty()) {
                appendLine()
                imports.joinTo(this, "\n")
                appendLine()
            }

            appendLine()
            append(body.trimIndent())
        }
    }

    fun generate(moduleInfo: ModuleInfo, bindList: List<BindInfo>) {
        val bindFullNameMap =
            bindList.groupBy { it.className.fullName() }.mapValues { it.value.toSet() }

        moduleInfo.kotlinFiles.groupBy { ktFile -> ktFile.flavor }.forEach { ktFile ->
            val content = StringBuilder()
            val packageName = moduleInfo.packageName(ktFile.key)
            content.generatePackage(packageName)
            val flavorModelList = ktFile.value.map { it.modelInfoList }.flatten()
            val flavorProviderList = ktFile.value.map { it.providerList }.flatten()
            if (flavorModelList.isEmpty() && flavorProviderList.isEmpty()) {
                return@forEach
            }
            val modelFullNameList = ktFile.value.map { it.modelInfoList }.flatten().map { it.name.fullName() }
            val bindModel = bindFullNameMap.filter { bindFullName ->
                modelFullNameList.any { bindFullName.key == it }
            }
            val name =
                (moduleInfo.modulePath.name.replaceFirst("lib", "").replace("-", "")
                        + ktFile.key.replaceFirstChar { it.uppercaseChar() })
                    .replaceFirstChar { it.uppercaseChar() }
            val targetFile = File(
                moduleInfo.modulePath,
                "src/${ktFile.key}/java/${moduleInfo.packageNames[ktFile.key]?.replace(".", "/") ?: ""}/di"
            ).resolve("${name}.kt")
            content.appendLine("// Generated by KoinConfigGenerated")
            content.appendLine("// Automatically generated during Dagger migration, do not modify manually. Add new Modules directly in [Koin${name}Module]")
            val importList = flavorModelList.map { it.name } +
                    bindModel.map { it.value.map { it.className } }.flatten() +
                    bindModel.map { it.value.map { it.interfaceName } }.flatten() +
                    flavorProviderList.map { provider -> provider.className } +
                    flavorProviderList.map { provider -> provider.returnType } +
                    flavorModelList.map { it.interfaces }.flatten()
            importList
                .filter { interfaceName -> !bindBlackList.any { interfaceName.typeName.startsWith(it) } }
                .map { it.fullName() }
                .filter { interfaceName -> !interfaceName.contains("<") }
                .toSortedSet(Comparator.naturalOrder())
                .forEach {
                    content.generateImport(it)
                }
            content.generateImport("com.vega.infrastructure.koin.ActivityScope")
            content.generateImport("com.vega.infrastructure.koin.providerGet")
            content.generateImport("org.koin.core.module.Module")
            content.generateImport("org.koin.core.module.dsl.scopedOf")
            content.generateImport("org.koin.core.module.dsl.factoryOf")
            content.generateImport("org.koin.core.module.dsl.singleOf")
            content.generateImport("org.koin.dsl.module")
            content.generateImport("org.koin.dsl.bind")
            content.generateImport("org.koin.dsl.binds")
            content.generateImport("org.koin.androidx.viewmodel.dsl.viewModelOf")
            content.appendLine()
            val module = content.generateKoinModule(name) {
                generateModelCode(flavorModelList, bindModel)
                generateProviderCode(flavorProviderList)
            }
            targetFile.apply {
                if (!this.parentFile.exists()) {
                    this.parentFile.mkdirs()
                }
            }.writeText(content.toString())
        }
    }

    /**
     * Generate package statement based on class package name
     */
    fun StringBuilder.generatePackage(packageName: String) {
        appendLine("package ${packageName}.di\n")
    }

    fun StringBuilder.generateProviderCode(providerList: List<ProvidersInfo>) {
        val hasScopeModel = providerList.filter { providerInfo -> providerInfo.hasScopeChile }
        if (hasScopeModel.isNotEmpty()) {
            generateKoinScope("ActivityScope") {
                hasScopeModel.sortedBy { it.returnType.typeName }
                    .forEach { provider ->
                        generateProvider(provider.daggerScope, provider, true)
                    }
            }
        }
        providerList.filter { providerInfo -> !providerInfo.hasScopeChile }.sortedBy { it.returnType.typeName }
            .forEach { provider ->
                generateProvider(provider.daggerScope, provider)
            }
    }

    fun StringBuilder.generateModelCode(modelList: List<ModelInfo>, bindFullNameMap: Map<String, Set<BindInfo>>) {
        val hasScopeModel = modelList.filter { flavorModel -> flavorModel.hasScopeChile }
        if (hasScopeModel.isNotEmpty()) {
            generateKoinScope("ActivityScope") {
                hasScopeModel.sortedBy { it.name.typeName }
                    .forEach { model ->
                        val allInterface = (bindFullNameMap[model.name.fullName()]?.map { it.interfaceName.typeName }
                            ?: emptyList()) + model.interfaces.map { it.typeName }

                        generateModel(model, allInterface, true)
                    }
            }
        }

        modelList.filter { flavorModel -> !flavorModel.hasScopeChile }.sortedBy { it.name.typeName }
            .forEach { model ->
                val allInterface =
                    (bindFullNameMap[model.name.fullName()]?.map { it.interfaceName.typeName }
                        ?: emptyList()) + model.interfaces.map { it.typeName }
                generateModel(model, allInterface)
            }
    }

    /**
     * Generate import statement based on class package name
     */
    fun StringBuilder.generateImport(clazz: String) {
        if (listOf("TemplateCoverViewModel").any { clazz.endsWith(it) }) {
            return
        }
        appendLine("import $clazz")
    }

    fun StringBuilder.generateKoinModule(moduleName: String, module: StringBuilder.() -> Unit): String {
        appendLine("object ${moduleName.replaceFirstChar { it.uppercaseChar() }}Module")
        appendLine("val ${moduleName.replaceFirstChar { it.uppercaseChar() }}Module.modules")
        appendLine("    get() = listOf(module {")
        module.invoke(this)
        appendLine("    })")
        return "${moduleName.replaceFirstChar { it.uppercaseChar() }}Module"
    }

    fun StringBuilder.generateKoinScope(scope: String, content: StringBuilder.() -> Unit) {
        val contentBuilder = StringBuilder()
        contentBuilder.content()
        if (contentBuilder.toString().isNotBlank()) {
            appendLine("        scope<${scope}>{")
            append(contentBuilder)
            appendLine("        }")
        } else {
            println("dont generate scope $scope")
        }
    }

    fun StringBuilder.generateModel(
        model: ModelInfo,
        binds: List<String>,
        inScope: Boolean = false
    ) {
        val space = if (inScope) {
            "    "
        } else {
            ""
        }
        val bindContext = generateBinds(binds)
        if ("ISession" in binds) {
            return
        }
        if (listOf(
                "BaseMviViewModel",
                "ListViewModel",
                "DefaultViewModelFactory",
                "TemplateCoverViewModel",
                "SvDigitalPresenterActionDispatcher",
                "EditDigitalHumanPanelDraftDispatcher",
                "CommonDigitalPresenterActionDispatcher",
                "EditDigitalHumanReportDispatcher",
                "SvPanelReportDispatcher",
                "CutSameSessionRepository",
                "MainVideoDigitalHumanViewModel",
                "SubVideoDigitalHumanViewModel",
                "TemplateCreditViewModel"
            ).any { model.name.typeName == it }
        ) {
            return
        }
        if (model.params.any { it.isProvider || it.isScopeInstance } || (model.daggerScope == DaggerScope.Activity && model.params.size > 10)) {
            appendLine(
                "        $space${model.daggerScope.func} { ${model.name.typeName}(${
                    generateHasProviderParams(
                        model.params
                    )
                }) } $bindContext"
            )
        } else {
            appendLine("        $space${model.daggerScope.ofFunc}(::${model.name.typeName}) $bindContext")
        }
    }

    fun StringBuilder.generateProvider(
        scope: DaggerScope,
        provider: ProvidersInfo,
        inScope: Boolean = false
    ) {
        val space = if (inScope) {
            "    "
        } else {
            ""
        }
        val bindContext = generateBinds(listOf(provider.returnType.typeName))
        if (provider.returnType.typeName in listOf("ISession", "ITemplatePlayer")) {
            return
        }
        if (provider.className.typeName in listOf(
                "CoreProvideModule",
                "EditorModule",
                "DaggerKoinProxy",
                "LauncherModule"
            )
        ) {
            return
        }
        appendLine(
            "        $space${scope.func}{ ${provider.className.typeName}().${provider.providerFunction}(${
                generateHasProviderParams(
                    provider.paramsInfoList
                )
            }) } $bindContext"
        )
    }

    fun generateHasProviderParams(paramsInfo: List<ParamsInfo>): String {
        return paramsInfo.joinToString(",") { it ->
            when {
                it.isProvider -> "providerGet()"
                it.isScopeInstance -> "this"
                else -> "get()"
            }
        }
    }

    fun generateBinds(binds: List<String>): String {
        val bindContext = StringBuilder()
        val binds = binds
            .filter { bind -> !bindBlackList.any { bind.startsWith(it) } }
            .filter { bind -> !bind.contains("<") }
        if (binds.isNotEmpty()) {
            if (binds.size == 1) {
                bindContext.append("bind ${binds.first()}::class")
            } else {
                bindContext.append("binds arrayOf(${binds.joinToString(", ") { "${it}::class" }})")
            }
        }
        return bindContext.toString()
    }
}
