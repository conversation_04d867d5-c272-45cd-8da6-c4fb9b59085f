package com.vega.builder.pipeline

import com.vega.builder.common.utils.setenvMock
import org.junit.jupiter.api.Test

class BenchmarkTest {

    fun mockEnv() {
        setenvMock("SYSTEM_MOCK", "true")
        setenvMock("WORKSPACE", System.getProperty("user.home"))
        setenvMock("MAIN_GIT_URL", "******************:faceu-android/vega.git")
        setenvMock("MAIN_GIT_BRANCH", "develop")
        setenvMock("MAIN_GIT_COMMIT", "")
        setenvMock("MAIN_CODE_TARGERT_PATH", "")
        setenvMock("CLONE_PATH", "")
        setenvMock("CLEAN_ALL", "")
        setenvMock("CLEAN_EXTRA_PARAMS", "")
        setenvMock("SUBMODULE_IGNORE", "")
//        setenvMock("TARGET_BRANCH", "feat/tanhaiyang/merge_test_target1")
//        setenvMock("MERGE_TARGET", "true")
        setenvMock(
            "BUILD_PARAMS", """
            {
                "RUNNING_TYPE":"benchmark",
                "BENCHMARK_TYPE":"lv_incremental_build_abi"
            }
            """.trimIndent()
        )
    }

    @Test
    fun clearRepoTask() {
        mockEnv()
        main()
    }
}