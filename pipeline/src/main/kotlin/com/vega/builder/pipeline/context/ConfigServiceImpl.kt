package com.vega.builder.pipeline.context

import com.google.gson.Gson
import com.vega.builder.common.config.IConfigService
import com.vega.builder.common.logger.logger
import com.vega.builder.common.utils.MD5Utils
import java.io.File
import kotlin.getValue
import kotlin.lazy

class ConfigServiceImpl(
    val buildParams: BuildParams,
    pipelineContext: PipelineContextImpl,
    workspaceParams: WorkspaceParams
) : IConfigService {
    override val taskCacheKey: String by lazy {
        val commit = pipelineContext.commitId
        val buildParams = Gson().toJson(buildParams.toSortedMap(Comparator.naturalOrder()))
        logger().info("Cache key from commit: $commit")
        logger().info("Cache key from buildParams: $buildParams")
        MD5Utils.ofString("$commit-${buildParams}")
    }
    override val taskCacheBasePath: File? by lazy { File(workspaceParams.targetProjectDir) }
    override val params: Map<String, String>
        get() = buildParams.toSortedMap(Comparator.naturalOrder())

}