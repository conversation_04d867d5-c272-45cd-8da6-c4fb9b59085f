package com.vega.builder.pipeline.core.kommand.io

import com.vega.builder.common.kommand.KommandException

class BufferedReader(
    private val reader: java.io.BufferedReader,
) {
    @Throws(KommandException::class)
    fun readLine(): String = reader.readLine()

    @Throws(KommandException::class)
    fun readAll(): String = reader.readText()

    @Throws(KommandException::class)
    fun lines(): Sequence<String> = reader.lineSequence()

    fun close() {
        reader.close()
    }
}
