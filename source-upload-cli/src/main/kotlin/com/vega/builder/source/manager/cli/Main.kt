package com.vega.builder.source.manager.cli

import com.google.gson.Gson
import com.vage.builder.source.manager.core.SourceManager
import com.vega.builder.common.git.extractRepositoryName
import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.logger.configureLogback
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.INetworkApi
import com.vega.builder.common.network.api.IBuildServiceApi
import com.vega.builder.common.network.api.SubRepoPublishTriggerReq
import com.vega.builder.common.network.request
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.source.manager.cli.module.ArtifactsInfo
import com.vega.builder.source.manager.cli.module.BytebusComponentInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.io.File
import kotlin.system.exitProcess
import kotlin.text.isNotBlank

fun main(args: Array<String>) = runBlocking {
    configureLogback()
    printParams("Read Build env:", getenvSafe().filterValues { it.isNotBlank() })
    if (args.size >= 2) {
        val artifactsInfoArg = args[1]
//            "{\"artifacts\": [{\"upload_source\": true, \"mrIid\": 18499, \"moduleName\": \"videoeditor_repo_info\", \"version\": \"15.3.1-alpha.40-test-SNAPSHOT\", \"repository\": \"http://maven.byted.org/repository/bytedance_android_snapshots/\", \"projectId\": 134421, \"upgrade\": true, \"uploadSource\": true, \"module_name\": \"videoeditor_repo_info\", \"groupId\": \"com.lemon.faceu\", \"repo_snapshot\": \"http://maven.byted.org/repository/bytedance_android_snapshots/\", \"artifactId\": \"videoeditor_repo_info\"}], \"bytebus_info_url\": \"https://${BuildConfig.hide_voffline_rul}/download/tos/schedule/toutiao.ios.arch/android_batch_upgrade/40279/44771/1730972275240.json\"}"
        val artifactsInfo = Gson().fromJson(artifactsInfoArg, ArtifactsInfo::class.java)
        val byteBusInfo = request(INetworkApi::download, artifactsInfo.bytebusInfoUrl)
        val bytebusComponentInfo =
            Gson().fromJson(byteBusInfo.string(), BytebusComponentInfo::class.java)
        val shouldUploadComponentList = bytebusComponentInfo.componentList.filter { it.upgrade }
        val repos = shouldUploadComponentList.distinctBy { it.git }
        for (repo in repos) {
            val filename = extractRepositoryName(repo.git)
            val excludeRegex = if (filename == "videoeditor") {
                listOf<String>(
                    "/cryptopp/ios/",
                    "/cryptopp/linux/",
                    "/cryptopp/swift/",
                    "/cryptopp/oc/",
                    "/cryptopp/mac/",
                    "/cryptopp/win/",
                    "^unittests/",
                    "^pc_config/",
                    "^linux_config/",
                    "^\\.",
                ).joinToString(separator = "|")
            } else {
                ""
            }


            if (filename != null) {
                val repoPath = File(File(args[0]).parentFile, filename).absolutePath
                if (filename == "videoeditor") {
                    Command("./autogen.sh").directory(File(repoPath,"android_config")).default().spawn().wait()
                    Command("./service_autogen.sh")
                        .directory(File(repoPath,"lyra"))
                        .args("-s","clip_flow_service.json")
                        .default().spawn().wait()
                }
                SourceManager().upload(
                    "${repo.groupId}:${repo.artifactId}",
                    repo.version,
                    repoPath,
                    excludeRegex
                )
            } else {
                logger().error("requested repo name is null")
                exitProcess(-1)
            }

            if (!repo.version.endsWith("-SNAPSHOT")) {
                if (bytebusComponentInfo.mainGitTargetBranch.startsWith("release/")
                    || bytebusComponentInfo.mainGitTargetBranch.startsWith("overseas/release/")
                ) {
                    val flavor = if (bytebusComponentInfo.mainProjectId == 40279) {
                        "prod"
                    } else {
                        "oversea"
                    }
                    launch {
                        publishDist(bytebusComponentInfo, flavor)
                    }.join()
                }
            }

        }
    }
    exitProcess(0)
    return@runBlocking
}

private suspend fun CoroutineScope.publishDist(
    bytebusComponentInfo: BytebusComponentInfo, string: String
) {
    val result = request(
        IBuildServiceApi::subRepoPublishTrigger, SubRepoPublishTriggerReq(
            bytebusComponentInfo.userName,
            bytebusComponentInfo.mainGitBranch,
            string,
            Gson().toJson(bytebusComponentInfo.componentList.associate {
                "${it.groupId}:${it.artifactId}" to it.version
            })
        )
    )
    if (result.isSuccess()) {
        logger().info("trigger publish sub repo success，jobId: ${result.data}")
        var publishResult = false
        do {
            val d = request(IBuildServiceApi::subRepoPublishState, result.data.toString())
            if (d.isSuccess()) {
                when (d.data) {
                    "success" -> {
                        publishResult = true
                    }

                    "failed" -> {
                        logger().error("sub repo publish failed failed: ${d.data}")
                        exitProcess(-2)
                    }

                    else -> {
                        logger().info("sub repo publishing[${result.data}]，state：${d.data}")
                        delay(15_000)
                    }
                }
            }
        } while (!publishResult)
        logger().info("wait for sub repo publish success")
    } else {
        logger().error("sub repo publish failed failed:，${result.msg}")
        exitProcess(-2)
    }
}
private fun printParams(
    msg: String,
    params: Map<String, Any>,
) {
    if (params.isNotEmpty()) {
        logger().info(msg)
        val maxKeyLength = params.keys.maxOfOrNull { it.length }
        for (entry in params.entries) {
            logger().info("${entry.key.padEnd(maxKeyLength ?: entry.key.length, ' ')}: ${entry.value}")
        }
    } else {
        logger().debug("Can't $msg")
    }
}






