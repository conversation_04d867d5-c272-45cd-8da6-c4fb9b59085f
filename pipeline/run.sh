#!/bin/bash

# 指定下载文件的URL
#URL="https://tosv.byted.org/obj/lv-android-build-script/pipeline"
#
## 指定下载后的文件名
#FILENAME="pipeline"
#
## 使用curl下载文件
#curl -o $FILENAME $URL
#
## 检查curl命令是否成功执行
#if [ $? -ne 0 ]; then
#    echo "下载失败"
#    exit 1
#fi
#
## 给下载的文件设置执行权限
#chmod +x $FILENAME
#
## 执行文件
#./$FILENAME
#
## 透传执行结果的错误码
#exit $?

set -x
# 下载JSON文件
if ! curl -o version.json https://tosv.byted.org/obj/lv-android-build-script/pipeline/pipeline.info; then
  echo "下载失败"
  exit 1
fi

# 读取JSON文件并获取版本信息
if [ "$pipeline_running_env" == "test" ]; then
    version=$(jq '.testVersion' version.json)
    file_prefix="pipeline-test-"
else
    version=$(jq '.version' version.json)
    file_prefix="pipeline-"
fi

# 拼接新的URL
new_url="https://tosv.byted.org/obj/lv-android-build-script/pipeline/${file_prefix}${version}"
FILENAME="pipeline"
# 下载二进制文件
if ! curl -o $FILENAME $new_url; then
  echo "下载失败"
  exit 1
fi

# 赋予执行权限
chmod +x $FILENAME

# 执行二进制文件
./$FILENAME

exit $?
