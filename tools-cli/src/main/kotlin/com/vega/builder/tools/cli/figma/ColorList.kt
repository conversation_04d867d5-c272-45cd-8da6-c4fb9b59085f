package com.vega.builder.tools.cli.figma

import com.google.gson.annotations.SerializedName

typealias ThemeMap = Map<String, List<Common>>

data class Common(
    @SerializedName("colorOrigin")
    val colorOrigin: Color,
    @SerializedName("type")
    val type: String,
    @SerializedName("variableName")
    val variableName: String,
    var fromBaseLine: Boolean = false
) {

}

@Suppress("NOTHING_TO_INLINE")
inline fun Float.fastCoerceIn(minimumValue: Float, maximumValue: Float) =
    this.fastCoerceAtLeast(minimumValue).fastCoerceAtMost(maximumValue)

@Suppress("NOTHING_TO_INLINE")
inline fun Float.fastCoerceAtMost(maximumValue: Float): Float {
    return if (this > maximumValue) maximumValue else this
}

/**
 * Ensures that this value is not less than the specified [minimumValue].
 */
@Suppress("NOTHING_TO_INLINE")
inline fun Float.fastCoerceAtLeast(minimumValue: Float): Float {
    return if (this < minimumValue) minimumValue else this
}

data class Color(
    @SerializedName("a")
    val a: Float = 1f,
    @SerializedName("b")
    val b: Float,
    @SerializedName("g")
    val g: Float,
    @SerializedName("r")
    val r: Float
) {
    override fun toString(): String {
        val argb = (
                ((a.fastCoerceIn(0.0f, 1.0f) * 255.0f + 0.5f).toInt() shl 24) or
                        ((r.fastCoerceIn(0.0f, 1.0f) * 255.0f + 0.5f).toInt() shl 16) or
                        ((g.fastCoerceIn(0.0f, 1.0f) * 255.0f + 0.5f).toInt() shl 8) or
                        (b.fastCoerceIn(0.0f, 1.0f) * 255.0f + 0.5f).toInt()
                )
        return String.format("Color(0x%08X)", argb)
    }
}

//class Theme(main: Main)
//class Main(solid: Solid)
//
//class Solid(val default:Color(0xFFFFFFFF))


