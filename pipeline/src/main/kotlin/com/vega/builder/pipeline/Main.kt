package com.vega.builder.pipeline

import com.google.gson.Gson
import com.vega.builder.common.logger.configureLogback
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.IComponentApi
import com.vega.builder.common.network.api.IJFrogApi
import com.vega.builder.common.network.request
import com.vega.builder.common.throwable.PipelineThrowableHandler
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.isMockEnv
import com.vega.builder.common.utils.printHello
import com.vega.builder.common.utils.retry
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.di.configureKoin
import com.vega.builder.pipeline.task.configureTask
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.asRequestBody
import org.koin.mp.KoinPlatform
import kotlin.io.path.createTempFile
import kotlin.io.path.writeText
import kotlin.system.exitProcess

fun main() {
    PipelineThrowableHandler.register()
    printHello()
    configureLogback()
    configureKoin()
    runBlocking {
        runCatching {
            KoinPlatform.getKoin().get<BuildParams>().loadParams()
            uploadPipelineState(PipelineStep.Start, true)
            configureTask()
        }.onSuccess {
            logger().info("Successfully build")
            uploadPipelineState(PipelineStep.End, true)
            reportComponentResult(true)
        }.onFailure {
            logger().info("Build failed")
            uploadPipelineState(PipelineStep.End, false)
            reportComponentResult(false)
            delay(5)
            throw it
        }
        if (!isMockEnv()) {
            // wait for all task finished
            delay(5)
            exitProcess(0)
        }
        return@runBlocking
    }
}



suspend fun reportComponentResult(success: Boolean) {
    val isComponent = getenvSafe("WORKFLOW_COMPONENT_ID", "false") != "false"

    val jobName = getenvSafe("TTP_REPO_NAME", "")
    if (jobName == "CC_MonoRepo_General_TTP_Build") {
        return
    }
    if (isComponent) {
        val isTTP = getenvSafe("TTP_BUILD_ENV", "false") != "false"
        val url = if (isTTP) {
            "http://bits-api.tiktok-sce.org/api/open/v1/repo/updateBinaryResult"
        } else {
            "http://${BuildConfig.hide_mobile_url}/inner_mpaas/v1/repo/updateBinaryResult"
        }
        retry(3, 15) {
            for (i in 1..3) {
                request(
                    IComponentApi::updateBinaryResult,
                    url,
                    getenvSafe("repo_id") ?: "",
                    getenvSafe("app_id") ?: "",
                    getenvSafe("repo_name") ?: "",
                    getenvSafe("version") ?: "",
                    if (success) "0" else "1", // 失败上报1，成功上报0
                    getenvSafe("operate_user") ?: "",
                    "4", // 平台默认值为4
                    getenvSafe("change_log") ?: "",
                    getenvSafe("WORKFLOW_JOB_ID") ?: "",
                )
                delay(5_000)
            }
        }
        logger().info("reportComponentResult")
    }
}

enum class PipelineStep(val value: String) {
    Start("start"),

    End("end"),
}


suspend fun uploadPipelineState(step: PipelineStep, success: Boolean) {
    val pipelineContext: PipelineContextImpl = KoinPlatform.getKoin().get<PipelineContextImpl>()
    if (!pipelineContext.isTTP) {
        return
    }
    val jobName = getenvSafe("TTP_REPO_NAME", "")
    if (jobName == "CC_MonoRepo_General_TTP_Build") {
        return
    }
    val stateFile = createTempFile(prefix = "pipeline_state", suffix = ".json")
    stateFile.writeText(Gson().toJson(BuildResult(success)))
    val result = request(
        IJFrogApi::upload,
        "CapCut/android/pipeline_state/${step.value}_${getenvSafe("WORKFLOW_JOB_ID")}",
        stateFile.toFile().asRequestBody("application/octet-stream".toMediaType())
    )
    if (result.isSuccessful) {
        logger().info(
            "Upload ${stateFile.toFile().absolutePath} to JFrog success,${result.body()?.let { Gson().toJson(it) } ?: "no body"}"
        )
    } else {
        logger().error("Can't upload JFrog ${stateFile.toFile().absolutePath}")
    }
}

data class BuildResult(
    val success: Boolean,
)