package com.vega.builder.pipeline.task.prepare.parses.xml

import com.vega.builder.common.logger.logger
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.buildTarget
import com.vega.builder.pipeline.context.isOutBuild
import okio.use
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.io.File

class NetworkSecurityParse : KoinComponent {
    val buildParams:BuildParams by inject()
    private val trustUserParams =
        mapOf(
            "IS_BUILD_RHEA_PRO" to true,
            "IS_BUILD_RHEA_3" to true,
            "IS_AUTO_TEST_DOMINO" to true,
            "IS_DATA_AUTOMATION" to true,
            "NETWORK_TRUST_USER" to true,
            "IS_COVERAGE" to true,
        )

    fun parse(file: File) {
        DefaultXmlParse(file).use { parse ->
            val trustUser =
                buildParams.any {
                    trustUserParams[it.key] == it.value.toBoolean()
                }
            if (trustUser) {
                logger().info("Unlock the annotations of certificates")
                parse.uncommentNode("trust-anchors", "certificates")
            }
            if (buildParams.buildTarget.isOversea && buildParams.isOutBuild) {
                val isHttp = "false" // Set it to the value you need
                logger().info("Update the value of disabled Http")
                parse.updateAttribute("base-config", "cleartextTrafficPermitted", isHttp)
                parse.updateAttribute("debug-overrides", "cleartextTrafficPermitted", isHttp)
                parse.updateAttribute("domain-config", "cleartextTrafficPermitted", isHttp)
            }

        }

    }
}
