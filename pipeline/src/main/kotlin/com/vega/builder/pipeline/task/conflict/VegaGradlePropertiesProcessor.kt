package com.vega.builder.pipeline.task.conflict

import java.io.File
import java.io.FileReader

class VegaGradlePropertiesProcessor : FileConflictProcessor {
    constructor(filePath: String) : super(filePath)
    constructor(file: File, conflictFileReader: ConflictFileReader) : super(file, conflictFileReader)

    private val supportKeys = listOf(
        "APP_VERSION_NAME",
        "APP_VERSION_CODE",
        "UPDATE_VERSION_CODE",
        "OVERSEA_APP_VERSION_NAME",
        "OVERSEA_APP_VERSION_CODE",
        "OVERSEA_UPDATE_VERSION_CODE",
        "VE_SDK_VERSION",
        "VE_SDK_VERSION_OVERSEA",
        "CLOUD_SDK_VERSION_OVERSEA",
        "ARTIFACT_VERSION"
    )

    override suspend fun processConflictBlock(conflictBlock: ConflictBlock): List<String> {
        // First determine whether the number of conflicting rows is the same. GradleProperties only resolves conflicts with the same number of rows
        if (conflictBlock.conflictLinesHead.size != conflictBlock.conflictLinesInsert.size) {
            throw IllegalStateException("GradleProperties 仅支持相同行数的冲突")
        }
        // Analyze conflict content and process it on a line basis
        return conflictBlock.conflictLinesHead.mapIndexed { index, line ->
            if (isSupportLine(line) || line == conflictBlock.conflictLinesInsert[index]) {
                if (line == conflictBlock.conflictLinesInsert[index]) {
                    return@mapIndexed line
                }
                val (key1, version1) = line.split("=").let {
                    return@let Pair(it[0], it[1])
                }
                val (key2, version2) = conflictBlock.conflictLinesInsert[index].split("=").let {
                    return@let Pair(it[0], it[1])
                }
                if (key1 != key2) {
                    throw IllegalStateException("GradleProperties only support conflicts on the same key")
                }
                return@mapIndexed if (isLargeThan(
                        version1,
                        version2,
                    )
                ) {
                    line
                } else {
                    conflictBlock.conflictLinesInsert[index]
                }
            } else {
                throw IllegalStateException("GradleProperties Only conflicts with specified keys are supported")
            }
        }
    }

    private fun isSupportLine(line: String): Boolean {
        return supportKeys.any { line.startsWith(it) }
    }
}