package com.vega.builder.pipeline.task.artifacts

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.IJFrogApi
import com.vega.builder.common.network.request
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.retry
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.utils.SlardarUtils
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.asRequestBody
import org.koin.core.component.inject
import java.io.File
import kotlin.io.path.writeText

const val SYMBOL_UPLOAD_KEY_PREFIX = "CapCut/android/pipeline_result/symbol_"

/**
 * 在TTP环境，会上传符号表和Manifest
 */
@TaskDefinition(
    name = "TTP_SLARDAR_MAPPING_UPLOAD",
    stage = Stage.After,
    allowFailure = false,
    displayName = "Upload TTP Slardar mapping"
)
class TTPSlardarMappingUpload : PipelineTask() {
    val pipelineContext by inject<PipelineContextImpl>()

    override suspend fun run() {
        TTPSymbolUploader().upload()
    }
}

class TTPSymbolUploader() {
    val uploadBaseKey by lazy { "${SYMBOL_UPLOAD_KEY_PREFIX}${getenvSafe("WORKFLOW_JOB_ID")}" }

    suspend fun upload() {
        val result = uploadSymbol()
        uploadManifest(result)
    }

    private fun querySymbolList(): List<String> {
        val nativePathInfoFile = File(PipelineOutput.buildNativePathInfo)
        return if (nativePathInfoFile.exists()) {
            Gson().fromJson<List<String>>(nativePathInfoFile.readText()).toHashSet().toList()
        } else {
            emptyList()
        }
    }

    suspend fun uploadSymbol(): List<SymbolArtifactInfo> {
        val uploadResult = mutableListOf<SymbolArtifactInfo>()
        val paths = querySymbolList()
        for (path in paths) {
            val soFile = File(path)
            if (!soFile.exists()) {
                continue
            }
            val fileInfo = SlardarUtils.queryNativeFileInfo(soFile.absolutePath)
            if (fileInfo != null) {
                logger().info("File[${soFile.absolutePath}] Build ID: $fileInfo")
                if (!fileInfo.stripped) {
                    val uploadFileKey = "${uploadBaseKey}/file/${fileInfo.buildId}_${soFile.name}"
                    try {
                        val uploadFileResult = retry(3, 50) {
                            val result = request(
                                IJFrogApi::upload,
                                uploadFileKey,
                                soFile.asRequestBody("application/octet-stream".toMediaType())
                            )
                            if (!result.isSuccessful || result.body() == null) {
                                throw Exception("Error uploading symbol $uploadFileKey")
                            }
                            result.body()!!
                        }
                        uploadResult.add(
                            SymbolArtifactInfo(
                                buildId = fileInfo.buildId,
                                name = soFile.name,
                                path = uploadFileKey,
                                md5 = uploadFileResult.checksums.md5,
                                success = true
                            )
                        )
                    } catch (e: Exception) {
                        uploadResult.add(
                            SymbolArtifactInfo(
                                buildId = fileInfo.buildId,
                                name = soFile.name,
                                path = uploadFileKey,
                                md5 = "",
                                success = false
                            )
                        )
                    }

                } else {
                    logger().warn("File[${soFile.absolutePath}] is stripped,skip upload!")
                }
            } else {
                logger().error("File[${soFile.absolutePath}] not find file info")
            }
        }
        return uploadResult
    }

    suspend fun uploadManifest(symbolArtifactInfo: List<SymbolArtifactInfo>) {
        val resultManifest = kotlin.io.path.createTempFile(prefix = "uploadResult", suffix = ".json")
        val manifestContent = Gson().toJson(symbolArtifactInfo)
        logger().info("manifest content: $manifestContent")
        resultManifest.writeText(manifestContent)
        val uploadManifestResult = retry(3, 50) {
            val result = request(
                IJFrogApi::upload,
                "${uploadBaseKey}/manifest.json",
                resultManifest.toFile().asRequestBody("application/octet-stream".toMediaType())
            )
            if (!result.isSuccessful || result.body() == null) {
                throw Exception("Upload symbol manifest  error:$uploadBaseKey")
            }
            result.body()!!
        }
        logger().info("upload result: ${Gson().toJson(uploadManifestResult)}")


    }
}


data class SymbolArtifactInfo(
    val buildId: String,
    val name: String,
    val path: String,
    val md5: String,
    val success: Boolean
)