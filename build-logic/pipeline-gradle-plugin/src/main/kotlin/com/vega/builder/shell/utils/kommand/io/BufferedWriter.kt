package com.vega.builder.shell.utils.kommand.io


import com.vega.builder.shell.utils.kommand.KommandException
import java.io.BufferedWriter

class BufferedWriter(
    private val writer: BufferedWriter,
) {
    @Throws(KommandException::class)
    fun writeLine(line: String) {
        writer.write(line)
        writer.newLine()
    }

    @Throws(KommandException::class)
    fun flush() {
        writer.flush()
    }

    @Throws(KommandException::class)
    fun close() {
        writer.close()
    }
}
