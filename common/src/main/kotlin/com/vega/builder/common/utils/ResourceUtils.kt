package com.vega.builder.common.utils

import com.vega.builder.common.logger.DefaultLogger
import com.vega.builder.common.logger.logger
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import java.io.InputStream
import java.net.URL
import java.net.URLDecoder
import java.nio.file.Files
import java.nio.file.Paths
import java.util.jar.JarFile
import kotlin.streams.toList

object ResourceUtils {
    fun readResource(filePath: String): String {
        // 获取当前类的类加载器
        val classLoader = this::class.java.classLoader
        // 使用类加载器获取资源作为输入流
        val inputStream: InputStream? = classLoader.getResourceAsStream(filePath)
        return inputStream?.reader()?.readText() ?: throw PipelineThrowable(ErrorType.ResourceNotFound)
    }

    fun readResourceStream(filePath: String): InputStream? {
        // 获取当前类的类加载器
        val classLoader = this::class.java.classLoader
        // 使用类加载器获取资源作为输入流
        val inputStream: InputStream? = classLoader.getResourceAsStream(filePath)
        return inputStream
    }
}
