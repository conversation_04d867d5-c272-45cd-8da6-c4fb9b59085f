{"CCLight": [{"variableName": "Color/Main/Solid/Default", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Text/OnContent/Primary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Background/Level 1", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Line/level 1", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.11999999731779099}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/6%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.05999999865889549}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Main/Solid/Pressed", "colorOrigin": {"r": 0.24313725531101227, "g": 0.1568627506494522, "b": 0.729411780834198, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Main/Transparency/12%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Main/Transparency/40%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Main/Transparency/60%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Main/Transparency/80%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Text/OnContent/Secondary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Text/OnContent/Tertiary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Text/OnContent/Placeholder", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Text/OnContent/Disabled", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Background/Level 3", "colorOrigin": {"r": 0.8987107872962952, "g": 0.8987107872962952, "b": 0.8987107872962952, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 4", "colorOrigin": {"r": 0.8901960849761963, "g": 0.9019607901573181, "b": 0.9098039269447327, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 5", "colorOrigin": {"r": 0.8627451062202454, "g": 0.8705882430076599, "b": 0.8784313797950745, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Transparency/Block", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.05999999865889549}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Transparency/Pressed", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Overlay", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Toast", "colorOrigin": {"r": 0.20000000298023224, "g": 0.20000000298023224, "b": 0.20000000298023224, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Line/level 2", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.07999999821186066}, "type": "RGBA"}, {"variableName": "Color/Line/level 3", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.03999999910593033}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Primary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Secondary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.699999988079071}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Tertiary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Placeholder", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.36000001430511475}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Disabled", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Black/4% · Mask light", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/4%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/12%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/20%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/40%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/60%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/80%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/BlackStationary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/BlackInverted", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/12%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/20%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/40%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/60%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/80%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/WhiteStationary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/WhiteInverted", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/SecondaryColor/Orange", "colorOrigin": {"r": 1, "g": 0.47843137383461, "b": 0, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Track/Orange", "colorOrigin": {"r": 0.843137264251709, "g": 0.43921568989753723, "b": 0.03529411926865578, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/State/Negative/Background", "colorOrigin": {"r": 1, "g": 0.4000000059604645, "b": 0.38823530077934265, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Background", "colorOrigin": {"r": 1, "g": 0.8156862854957581, "b": 0.20000000298023224, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Background", "colorOrigin": {"r": 0.1764705926179886, "g": 0.8196078538894653, "b": 0.5882353186607361, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Information/Background", "colorOrigin": {"r": 0.14901961386203766, "g": 0.40784314274787903, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Fuchsia", "colorOrigin": {"r": 0.9333333373069763, "g": 0.1921568661928177, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Blueviolet", "colorOrigin": {"r": 0.5058823823928833, "g": 0.125490203499794, "b": 0.9921568632125854, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Slateblue", "colorOrigin": {"r": 0.3921568691730499, "g": 0.3803921639919281, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Blue", "colorOrigin": {"r": 0.1411764770746231, "g": 0.6392157077789307, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Green", "colorOrigin": {"r": 0, "g": 0.7568627595901489, "b": 0.48627451062202454, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Track/Fuchsia", "colorOrigin": {"r": 0.8745098114013672, "g": 0.24313725531101227, "b": 0.5607843399047852, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Purple", "colorOrigin": {"r": 0.5803921818733215, "g": 0.23529411852359772, "b": 0.9254902005195618, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Blue Iris", "colorOrigin": {"r": 0.364705890417099, "g": 0.364705890417099, "b": 0.8980392217636108, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Blue", "colorOrigin": {"r": 0.14509804546833038, "g": 0.5372549295425415, "b": 0.9137254953384399, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Green", "colorOrigin": {"r": 0.054901961237192154, "g": 0.6274510025978088, "b": 0.32156863808631897, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/State/Negative/Primary", "colorOrigin": {"r": 1, "g": 0.1882352977991104, "b": 0.239215686917305, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Negative/Active", "colorOrigin": {"r": 0.7176470756530762, "g": 0.0941176488995552, "b": 0.24313725531101227, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Primary", "colorOrigin": {"r": 1, "g": 0.8117647171020508, "b": 0.20000000298023224, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Active", "colorOrigin": {"r": 0.9607843160629272, "g": 0.6823529601097107, "b": 0.03921568766236305, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Primary", "colorOrigin": {"r": 0.1764705926179886, "g": 0.8196078538894653, "b": 0.5882353186607361, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Active", "colorOrigin": {"r": 0.08627451211214066, "g": 0.5882353186607361, "b": 0.5058823823928833, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Information/Primary", "colorOrigin": {"r": 0.14901961386203766, "g": 0.40784314274787903, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Information/Active", "colorOrigin": {"r": 0.04313725605607033, "g": 0.27450981736183167, "b": 0.6941176652908325, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Panel", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Guide", "colorOrigin": {"r": 1, "g": 0.8901960849761963, "b": 0.34117648005485535, "a": 0.9200000166893005}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/20%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Background/Level 2", "colorOrigin": {"r": 0.9607843160629272, "g": 0.9607843160629272, "b": 0.9607843160629272, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Black/Image Mask", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Inverted", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Overlay Tag", "colorOrigin": {"r": 0.3019607961177826, "g": 0.3019607961177826, "b": 0.3019607961177826, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Link", "colorOrigin": {"r": 0.48627451062202454, "g": 0.3686274588108063, "b": 0.8980392217636108, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level1", "colorOrigin": {"r": 0.0784313753247261, "g": 0.0784313753247261, "b": 0.0784313753247261, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level2", "colorOrigin": {"r": 0.10980392247438431, "g": 0.10980392247438431, "b": 0.10980392247438431, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level3", "colorOrigin": {"r": 0.15000000596046448, "g": 0.15000000596046448, "b": 0.15000000596046448, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level4", "colorOrigin": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level5", "colorOrigin": {"r": 0.23000000417232513, "g": 0.23000000417232513, "b": 0.23000000417232513, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Cursor", "colorOrigin": {"r": 0.48627451062202454, "g": 0.3686274588108063, "b": 0.8980392217636108, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Block/Block", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.05000000074505806}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Block Pressed", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.10000000149011612}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Pressed", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.05000000074505806}, "type": "RGBA"}], "CCDark": [{"variableName": "Color/Main/Solid/Default", "colorOrigin": {"r": 0.3921568691730499, "g": 0.250980406999588, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Primary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 1", "colorOrigin": {"r": 0.054901961237192154, "g": 0.054901961237192154, "b": 0.06666667014360428, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Line/level 1", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.1599999964237213}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/6%", "colorOrigin": {"r": 0.7532150745391846, "g": 0.6956319212913513, "b": 1, "a": 0.05999999865889549}, "type": "RGBA"}, {"variableName": "Color/Main/Solid/Pressed", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Main/Transparency/12%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.11999999731779099}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/40%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.4000000059604645}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/60%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.6000000238418579}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/80%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.800000011920929}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Secondary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.800000011920929}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Tertiary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Placeholder", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Disabled", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 3", "colorOrigin": {"r": 0.10980392247438431, "g": 0.11372549086809158, "b": 0.12941177189350128, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 4", "colorOrigin": {"r": 0.14509804546833038, "g": 0.14901961386203766, "b": 0.16862745583057404, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 5", "colorOrigin": {"r": 0.18431372940540314, "g": 0.1882352977991104, "b": 0.21176470816135406, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Transparency/Block", "colorOrigin": {"r": 0.8980392217636108, "g": 0.9254902005195618, "b": 1, "a": 0.10000000149011612}, "type": "RGBA"}, {"variableName": "Color/Fill/Transparency/Pressed", "colorOrigin": {"r": 0.8980392217636108, "g": 0.9254902005195618, "b": 1, "a": 0.14000000059604645}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Overlay", "colorOrigin": {"r": 0.12941177189350128, "g": 0.13333334028720856, "b": 0.14901961386203766, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Toast", "colorOrigin": {"r": 0.20000000298023224, "g": 0.20000000298023224, "b": 0.20000000298023224, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Line/level 2", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.10000000149011612}, "type": "RGBA"}, {"variableName": "Color/Line/level 3", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.05999999865889549}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Primary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Secondary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.800000011920929}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Tertiary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Placeholder", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Disabled", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Black/4% · Mask light", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/4%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/12%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/20%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/40%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/60%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/80%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/BlackStationary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/BlackInverted", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/12%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/20%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/40%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/60%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/80%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/WhiteStationary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/WhiteInverted", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/SecondaryColor/Orange", "colorOrigin": {"r": 1, "g": 0.47843137383461, "b": 0, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Track/Orange", "colorOrigin": {"r": 0.6901960968971252, "g": 0.3490196168422699, "b": 0.06666667014360428, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/State/Negative/Background", "colorOrigin": {"r": 1, "g": 0.4000000059604645, "b": 0.38823530077934265, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Background", "colorOrigin": {"r": 1, "g": 0.8156862854957581, "b": 0.20000000298023224, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Background", "colorOrigin": {"r": 0, "g": 0.7215686440467834, "b": 0.3607843220233917, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Information/Background", "colorOrigin": {"r": 0.14901961386203766, "g": 0.40784314274787903, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Fuchsia", "colorOrigin": {"r": 0.9333333373069763, "g": 0.1921568661928177, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Blueviolet", "colorOrigin": {"r": 0.5058823823928833, "g": 0.125490203499794, "b": 0.9921568632125854, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Slateblue", "colorOrigin": {"r": 0.3921568691730499, "g": 0.3803921639919281, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Blue", "colorOrigin": {"r": 0.1411764770746231, "g": 0.6392157077789307, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Green", "colorOrigin": {"r": 0, "g": 0.7568627595901489, "b": 0.48627451062202454, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Track/Fuchsia", "colorOrigin": {"r": 0.7372549176216125, "g": 0.18431372940540314, "b": 0.47843137383461, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Purple", "colorOrigin": {"r": 0.48235294222831726, "g": 0.1568627506494522, "b": 0.7843137383460999, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Blue Iris", "colorOrigin": {"r": 0.3137255012989044, "g": 0.2862745225429535, "b": 0.8313725590705872, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Blue", "colorOrigin": {"r": 0.09019608050584793, "g": 0.4274509847164154, "b": 0.8117647171020508, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Green", "colorOrigin": {"r": 0.062745101749897, "g": 0.5176470875740051, "b": 0.27450981736183167, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/State/Negative/Primary", "colorOrigin": {"r": 1, "g": 0.1882352977991104, "b": 0.239215686917305, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Negative/Active", "colorOrigin": {"r": 0.7176470756530762, "g": 0.0941176488995552, "b": 0.24313725531101227, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Primary", "colorOrigin": {"r": 1, "g": 0.8117647171020508, "b": 0.20000000298023224, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Active", "colorOrigin": {"r": 0.9607843160629272, "g": 0.6823529601097107, "b": 0.03921568766236305, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Primary", "colorOrigin": {"r": 0.1764705926179886, "g": 0.8196078538894653, "b": 0.5882353186607361, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Active", "colorOrigin": {"r": 0.08627451211214066, "g": 0.5882353186607361, "b": 0.5058823823928833, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Information/Primary", "colorOrigin": {"r": 0.14901961386203766, "g": 0.40784314274787903, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Information/Active", "colorOrigin": {"r": 0.04313725605607033, "g": 0.27450981736183167, "b": 0.6941176652908325, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Panel", "colorOrigin": {"r": 0.11999999731779099, "g": 0.11999999731779099, "b": 0.11999999731779099, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Guide", "colorOrigin": {"r": 1, "g": 0.8901960849761963, "b": 0.34117648005485535, "a": 0.9200000166893005}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/20%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 2", "colorOrigin": {"r": 0.08235294371843338, "g": 0.08235294371843338, "b": 0.09019608050584793, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Black/Image Mask", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Text/Default/Inverted", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Overlay Tag", "colorOrigin": {"r": 0.3019607961177826, "g": 0.3019607961177826, "b": 0.3019607961177826, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Link", "colorOrigin": {"r": 0.48627451062202454, "g": 0.3686274588108063, "b": 0.8980392217636108, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level1", "colorOrigin": {"r": 0.0784313753247261, "g": 0.0784313753247261, "b": 0.0784313753247261, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level2", "colorOrigin": {"r": 0.10980392247438431, "g": 0.10980392247438431, "b": 0.10980392247438431, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level3", "colorOrigin": {"r": 0.15000000596046448, "g": 0.15000000596046448, "b": 0.15000000596046448, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level4", "colorOrigin": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level5", "colorOrigin": {"r": 0.23000000417232513, "g": 0.23000000417232513, "b": 0.23000000417232513, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Cursor", "colorOrigin": {"r": 0.48627451062202454, "g": 0.3686274588108063, "b": 0.8980392217636108, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Block/Block", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.05000000074505806}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Block Pressed", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.10000000149011612}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Pressed", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.05000000074505806}, "type": "RGBA"}], "JYLight": [{"variableName": "Color/Main/Solid/Default", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Text/OnContent/Primary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 1", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Line/level 1", "colorOrigin": {"r": 0.158720925450325, "g": 0.24798449873924255, "b": 0.3499999940395355, "a": 0.1599999964237213}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/6%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.05999999865889549}, "type": "RGBA"}, {"variableName": "Color/Main/Solid/Pressed", "colorOrigin": {"r": 0.24313725531101227, "g": 0.1568627506494522, "b": 0.729411780834198, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Main/Transparency/12%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.11999999731779099}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/40%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.4000000059604645}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/60%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.6000000238418579}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/80%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.800000011920929}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Secondary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.800000011920929}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Tertiary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Placeholder", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Disabled", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 3", "colorOrigin": {"r": 0.929411768913269, "g": 0.9333333373069763, "b": 0.9411764740943909, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 4", "colorOrigin": {"r": 0.8901960849761963, "g": 0.9019607901573181, "b": 0.9098039269447327, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 5", "colorOrigin": {"r": 0.8627451062202454, "g": 0.8705882430076599, "b": 0.8784313797950745, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Transparency/Block", "colorOrigin": {"r": 0.18039216101169586, "g": 0.29019609093666077, "b": 0.4000000059604645, "a": 0.05999999865889549}, "type": "RGBA"}, {"variableName": "Color/Fill/Transparency/Pressed", "colorOrigin": {"r": 0.16470588743686676, "g": 0.26274511218070984, "b": 0.3686274588108063, "a": 0.10000000149011612}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Overlay", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Toast", "colorOrigin": {"r": 0.2549019753932953, "g": 0.2666666805744171, "b": 0.27843138575553894, "a": 0.9800000190734863}, "type": "RGBA"}, {"variableName": "Color/Line/level 2", "colorOrigin": {"r": 0.16470588743686676, "g": 0.26274511218070984, "b": 0.3686274588108063, "a": 0.10000000149011612}, "type": "RGBA"}, {"variableName": "Color/Line/level 3", "colorOrigin": {"r": 0.18039216101169586, "g": 0.29019609093666077, "b": 0.4000000059604645, "a": 0.05999999865889549}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Primary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Secondary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.699999988079071}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Tertiary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Placeholder", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.4000000059604645}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Disabled", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Black/4% · Mask light", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/4%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/12%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/20%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/40%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/60%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/80%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/BlackStationary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/BlackInverted", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/12%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/20%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/40%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/60%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/80%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/WhiteStationary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/WhiteInverted", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/SecondaryColor/Orange", "colorOrigin": {"r": 1, "g": 0.47843137383461, "b": 0, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Track/Orange", "colorOrigin": {"r": 0.843137264251709, "g": 0.43921568989753723, "b": 0.03529411926865578, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/State/Negative/Background", "colorOrigin": {"r": 1, "g": 0.4000000059604645, "b": 0.38823530077934265, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Background", "colorOrigin": {"r": 1, "g": 0.8156862854957581, "b": 0.20000000298023224, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Background", "colorOrigin": {"r": 0, "g": 0.7215686440467834, "b": 0.3607843220233917, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Information/Background", "colorOrigin": {"r": 0.14901961386203766, "g": 0.40784314274787903, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Fuchsia", "colorOrigin": {"r": 0.9333333373069763, "g": 0.1921568661928177, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Blueviolet", "colorOrigin": {"r": 0.5058823823928833, "g": 0.125490203499794, "b": 0.9921568632125854, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Slateblue", "colorOrigin": {"r": 0.3921568691730499, "g": 0.3803921639919281, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Blue", "colorOrigin": {"r": 0.1411764770746231, "g": 0.6392157077789307, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Green", "colorOrigin": {"r": 0, "g": 0.7568627595901489, "b": 0.48627451062202454, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Track/Fuchsia", "colorOrigin": {"r": 0.8745098114013672, "g": 0.24313725531101227, "b": 0.5607843399047852, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Purple", "colorOrigin": {"r": 0.5803921818733215, "g": 0.23529411852359772, "b": 0.9254902005195618, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Blue Iris", "colorOrigin": {"r": 0.364705890417099, "g": 0.364705890417099, "b": 0.8980392217636108, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Blue", "colorOrigin": {"r": 0.14509804546833038, "g": 0.5372549295425415, "b": 0.9137254953384399, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Green", "colorOrigin": {"r": 0.054901961237192154, "g": 0.6274510025978088, "b": 0.32156863808631897, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/State/Negative/Primary", "colorOrigin": {"r": 1, "g": 0.1882352977991104, "b": 0.239215686917305, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Negative/Active", "colorOrigin": {"r": 0.7176470756530762, "g": 0.0941176488995552, "b": 0.24313725531101227, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Primary", "colorOrigin": {"r": 1, "g": 0.8117647171020508, "b": 0.20000000298023224, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Active", "colorOrigin": {"r": 0.9607843160629272, "g": 0.6823529601097107, "b": 0.03921568766236305, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Primary", "colorOrigin": {"r": 0.1764705926179886, "g": 0.8196078538894653, "b": 0.5882353186607361, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Active", "colorOrigin": {"r": 0.08627451211214066, "g": 0.5882353186607361, "b": 0.5058823823928833, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Information/Primary", "colorOrigin": {"r": 0.14901961386203766, "g": 0.40784314274787903, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Information/Active", "colorOrigin": {"r": 0.04313725605607033, "g": 0.27450981736183167, "b": 0.6941176652908325, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Panel", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Guide", "colorOrigin": {"r": 1, "g": 0.8901960849761963, "b": 0.34117648005485535, "a": 0.9200000166893005}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/20%", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 2", "colorOrigin": {"r": 0.9607843160629272, "g": 0.9647058844566345, "b": 0.9686274528503418, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Black/Image Mask", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Inverted", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Overlay Tag", "colorOrigin": {"r": 0.3019607961177826, "g": 0.3019607961177826, "b": 0.3019607961177826, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Link", "colorOrigin": {"r": 0.48627451062202454, "g": 0.3686274588108063, "b": 0.8980392217636108, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level1", "colorOrigin": {"r": 0.0784313753247261, "g": 0.0784313753247261, "b": 0.0784313753247261, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level2", "colorOrigin": {"r": 0.10980392247438431, "g": 0.10980392247438431, "b": 0.10980392247438431, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level3", "colorOrigin": {"r": 0.15000000596046448, "g": 0.15000000596046448, "b": 0.15000000596046448, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level4", "colorOrigin": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level5", "colorOrigin": {"r": 0.23000000417232513, "g": 0.23000000417232513, "b": 0.23000000417232513, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Cursor", "colorOrigin": {"r": 0.9882352948188782, "g": 0.8117647171020508, "b": 0.08235294371843338, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Block", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.05000000074505806}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Block Pressed", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.05000000074505806}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Pressed", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.05000000074505806}, "type": "RGBA"}], "JYDark": [{"variableName": "Color/Main/Solid/Default", "colorOrigin": {"r": 0.3921568691730499, "g": 0.250980406999588, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Primary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 1", "colorOrigin": {"r": 0.054901961237192154, "g": 0.054901961237192154, "b": 0.06666667014360428, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Line/level 1", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.1599999964237213}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/6%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.05999999865889549}, "type": "RGBA"}, {"variableName": "Color/Main/Solid/Pressed", "colorOrigin": {"r": 0.3686274588108063, "g": 0.250980406999588, "b": 0.8745098114013672, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Main/Transparency/12%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.11999999731779099}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/40%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.4000000059604645}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/60%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.6000000238418579}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/80%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.800000011920929}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Secondary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.800000011920929}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Tertiary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Placeholder", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "RGBA"}, {"variableName": "Color/Text/OnContent/Disabled", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 3", "colorOrigin": {"r": 0.10980392247438431, "g": 0.11372549086809158, "b": 0.12941177189350128, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 4", "colorOrigin": {"r": 0.14509804546833038, "g": 0.14901961386203766, "b": 0.16862745583057404, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 5", "colorOrigin": {"r": 0.18431372940540314, "g": 0.1882352977991104, "b": 0.21176470816135406, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Transparency/Block", "colorOrigin": {"r": 0.8980392217636108, "g": 0.9254902005195618, "b": 1, "a": 0.10000000149011612}, "type": "RGBA"}, {"variableName": "Color/Fill/Transparency/Pressed", "colorOrigin": {"r": 0.8980392217636108, "g": 0.9254902005195618, "b": 1, "a": 0.14000000059604645}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Overlay", "colorOrigin": {"r": 0.12941177189350128, "g": 0.13333334028720856, "b": 0.14901961386203766, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Toast", "colorOrigin": {"r": 0.2549019753932953, "g": 0.2666666805744171, "b": 0.27843138575553894, "a": 0.9800000190734863}, "type": "RGBA"}, {"variableName": "Color/Line/level 2", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.10000000149011612}, "type": "RGBA"}, {"variableName": "Color/Line/level 3", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.05999999865889549}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Primary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Secondary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.699999988079071}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Tertiary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Placeholder", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "RGBA"}, {"variableName": "Color/Text/Default/Disabled", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Black/4% · Mask light", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/4%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/12%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/20%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/40%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/60%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/80%", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/BlackStationary", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Black/BlackInverted", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/12%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.11999999731779099}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/20%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.20000000298023224}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/40%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.4000000059604645}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/60%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.6000000238418579}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/80%", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.800000011920929}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/WhiteStationary", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/White/WhiteInverted", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/SecondaryColor/Orange", "colorOrigin": {"r": 1, "g": 0.47843137383461, "b": 0, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Track/Orange", "colorOrigin": {"r": 0.6901960968971252, "g": 0.3490196168422699, "b": 0.06666667014360428, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/State/Negative/Background", "colorOrigin": {"r": 1, "g": 0.4000000059604645, "b": 0.38823530077934265, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Background", "colorOrigin": {"r": 1, "g": 0.8156862854957581, "b": 0.20000000298023224, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Background", "colorOrigin": {"r": 0, "g": 0.7215686440467834, "b": 0.3607843220233917, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/State/Information/Background", "colorOrigin": {"r": 0.14901961386203766, "g": 0.40784314274787903, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Fuchsia", "colorOrigin": {"r": 0.9333333373069763, "g": 0.1921568661928177, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Blueviolet", "colorOrigin": {"r": 0.5058823823928833, "g": 0.125490203499794, "b": 0.9921568632125854, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Slateblue", "colorOrigin": {"r": 0.3921568691730499, "g": 0.3803921639919281, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Blue", "colorOrigin": {"r": 0.1411764770746231, "g": 0.6392157077789307, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/SecondaryColor/Green", "colorOrigin": {"r": 0, "g": 0.7568627595901489, "b": 0.48627451062202454, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Track/Fuchsia", "colorOrigin": {"r": 0.7372549176216125, "g": 0.18431372940540314, "b": 0.47843137383461, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Purple", "colorOrigin": {"r": 0.48235294222831726, "g": 0.1568627506494522, "b": 0.7843137383460999, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Blue Iris", "colorOrigin": {"r": 0.3137255012989044, "g": 0.2862745225429535, "b": 0.8313725590705872, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Blue", "colorOrigin": {"r": 0.09019608050584793, "g": 0.4274509847164154, "b": 0.8117647171020508, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Track/Green", "colorOrigin": {"r": 0.062745101749897, "g": 0.5176470875740051, "b": 0.27450981736183167, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/State/Negative/Primary", "colorOrigin": {"r": 1, "g": 0.1882352977991104, "b": 0.239215686917305, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Negative/Active", "colorOrigin": {"r": 0.7176470756530762, "g": 0.0941176488995552, "b": 0.24313725531101227, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Primary", "colorOrigin": {"r": 1, "g": 0.8117647171020508, "b": 0.20000000298023224, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Warning/Active", "colorOrigin": {"r": 0.9607843160629272, "g": 0.6823529601097107, "b": 0.03921568766236305, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Primary", "colorOrigin": {"r": 0.1764705926179886, "g": 0.8196078538894653, "b": 0.5882353186607361, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Positive/Active", "colorOrigin": {"r": 0.08627451211214066, "g": 0.5882353186607361, "b": 0.5058823823928833, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Information/Primary", "colorOrigin": {"r": 0.14901961386203766, "g": 0.40784314274787903, "b": 1, "a": 1}, "type": "RGBA"}, {"variableName": "Color/State/Information/Active", "colorOrigin": {"r": 0.04313725605607033, "g": 0.27450981736183167, "b": 0.6941176652908325, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Panel", "colorOrigin": {"r": 0.11999999731779099, "g": 0.11999999731779099, "b": 0.11999999731779099, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Guide", "colorOrigin": {"r": 1, "g": 0.8901960849761963, "b": 0.34117648005485535, "a": 0.9200000166893005}, "type": "RGBA"}, {"variableName": "Color/Main/Transparency/20%", "colorOrigin": {"r": 0.7529411911964417, "g": 0.6941176652908325, "b": 1, "a": 0.20000000298023224}, "type": "RGBA"}, {"variableName": "Color/Fill/Background/Level 2", "colorOrigin": {"r": 0.08235294371843338, "g": 0.08235294371843338, "b": 0.09019608050584793, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Black/Image Mask", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 0.03999999910593033}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Text/Default/Inverted", "colorOrigin": {"r": 0, "g": 0, "b": 0, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Overlay Tag", "colorOrigin": {"r": 0.3019607961177826, "g": 0.3019607961177826, "b": 0.3019607961177826, "a": 0.5}, "type": "RGBA"}, {"variableName": "Color/Fill/Scenes/Link", "colorOrigin": {"r": 0.48627451062202454, "g": 0.3686274588108063, "b": 0.8980392217636108, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level1", "colorOrigin": {"r": 0.0784313753247261, "g": 0.0784313753247261, "b": 0.0784313753247261, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level2", "colorOrigin": {"r": 0.10980392247438431, "g": 0.10980392247438431, "b": 0.10980392247438431, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level3", "colorOrigin": {"r": 0.15000000596046448, "g": 0.15000000596046448, "b": 0.15000000596046448, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level4", "colorOrigin": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/EditorBackground/Level5", "colorOrigin": {"r": 0.23000000417232513, "g": 0.23000000417232513, "b": 0.23000000417232513, "a": 1}, "type": "ALIAS_RESOLVED"}, {"variableName": "Color/Fill/Scenes/Cursor", "colorOrigin": {"r": 0.9882352948188782, "g": 0.8117647171020508, "b": 0.08235294371843338, "a": 1}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Block", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.05000000074505806}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Block Pressed", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.05000000074505806}, "type": "RGBA"}, {"variableName": "Color/Fill/Block/Pressed", "colorOrigin": {"r": 1, "g": 1, "b": 1, "a": 0.05000000074505806}, "type": "RGBA"}]}