package com.vega.builder.common.network.api

import com.vega.builder.common.network.BaseUrl
import okhttp3.ResponseBody
import retrofit2.http.GET
import retrofit2.http.Query
import retrofit2.http.Url

@BaseUrl("https://fack.com", printLogs = true)
interface IComponentApi {
    /**
     * https://cloud.bytedance.net/docs/bits/docs/6480709f2207580224c6b2d2/662dc82adb106902ea94af47?x-resource-account=public&x-bc-region-id=bytedance
     */
    @GET
    suspend fun updateBinaryResult(
        @Url url: String,
        @Query("repoId") repoId: String,
        @Query("appId") appId: String,
        @Query("repoName") repoName: String,
        @Query("version") version: String,
        @Query("status") status: String,
        @Query("operateUser") operateUser: String,
        @Query("appType") appType: String,
        @Query("changeLog") changeLog: String,
        @Query("buildNumber") buildNumber: String,
    ): ResponseBody


}