package com.vega.builder.pipeline.task.conflict

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

/**
 * Test for DependencyCmakeProcessor
 *
 * <AUTHOR>
 * @time 2025/1/20
 */
class DependencyCmakeProcessorTest : BaseProcessorTest() {

    override val filePath: String = "dependency.cmake"

    override var fileContent = """
        # LVVE 草稿中间层

        ###### 中间层 组件集成，名称与组件平台保持一致 [LVVE_SDK_VERSION]
        ### 国内版
        # 二进制集成时，PC统一的中间层版本号
        <<<<<<< HEAD
        set(lv_videoeditor ******** CACHE INTERNAL "")
        =======
        set(lv_videoeditor ******* CACHE INTERNAL "")
        >>>>>>> feature-branch

        # Windows 中间层独立版本号，预留给版本号不一致的情况下使用
        set(lv_videoeditor_win "" CACHE INTERNAL "")
        # macOS 中间层独立版本号，预留给版本号不一致的情况下使用
        set(lv_videoeditor_mac "" CACHE INTERNAL "")

        ### 海外版
        # 二进制集成时，PC统一的中间层版本号
        <<<<<<< HEAD
        set(cc_videoeditor ******** CACHE INTERNAL "")
        =======
        set(cc_videoeditor 8.9.0.20 CACHE INTERNAL "")
        >>>>>>> feature-branch
        # Windows 中间层独立版本号，预留给版本号不一致的情况下使用
        set(cc_videoeditor_win "" CACHE INTERNAL "")
        # macOS 中间层独立版本号，预留给版本号不一致的情况下使用
        set(cc_videoeditor_mac "" CACHE INTERNAL "")

        # Qt默认版本号
        # 生效优先级：CMAKE变量QT_VERSION > 环境变量QT_VERSION > Qt默认版本号
        # Qt6.2环境准备 Qt6 环境

        # Windows Qt默认版本号
        <<<<<<< HEAD
        set(QT_WIN_DEFAULT_VERSION "6.2.2.536" CACHE INTERNAL "")
        =======
        set(QT_WIN_DEFAULT_VERSION "6.3.0.100" CACHE INTERNAL "")
        >>>>>>> feature-branch

        # macOS Qt默认版本号
        set(QT_MAC_DEFAULT_VERSION "6.2.2.442" CACHE INTERNAL "")

        # lvOpenPlugin 版本号
        set(lv_openplugin_src 8.4.0.4 CACHE INTERNAL "")
    """.trimIndent()

    override val resolveConflict = """
        # LVVE 草稿中间层

        ###### 中间层 组件集成，名称与组件平台保持一致 [LVVE_SDK_VERSION]
        ### 国内版
        # 二进制集成时，PC统一的中间层版本号
        set(lv_videoeditor ******* CACHE INTERNAL "")

        # Windows 中间层独立版本号，预留给版本号不一致的情况下使用
        set(lv_videoeditor_win "" CACHE INTERNAL "")
        # macOS 中间层独立版本号，预留给版本号不一致的情况下使用
        set(lv_videoeditor_mac "" CACHE INTERNAL "")

        ### 海外版
        # 二进制集成时，PC统一的中间层版本号
        set(cc_videoeditor ******** CACHE INTERNAL "")
        # Windows 中间层独立版本号，预留给版本号不一致的情况下使用
        set(cc_videoeditor_win "" CACHE INTERNAL "")
        # macOS 中间层独立版本号，预留给版本号不一致的情况下使用
        set(cc_videoeditor_mac "" CACHE INTERNAL "")

        # Qt默认版本号
        # 生效优先级：CMAKE变量QT_VERSION > 环境变量QT_VERSION > Qt默认版本号
        # Qt6.2环境准备 Qt6 环境

        # Windows Qt默认版本号
        set(QT_WIN_DEFAULT_VERSION "6.3.0.100" CACHE INTERNAL "")

        # macOS Qt默认版本号
        set(QT_MAC_DEFAULT_VERSION "6.2.2.442" CACHE INTERNAL "")

        # lvOpenPlugin 版本号
        set(lv_openplugin_src 8.4.0.4 CACHE INTERNAL "")
    """.trimIndent()

    @Test
    fun testProcessConflictBlock() {
        val mockFile = mockFile()
        val mockFileReader = mockFileReader()
        val processor = DependencyCmakeProcessor(mockFile, mockFileReader)
        runBlocking {
            processor.process()
        }
        Assertions.assertEquals(resolveConflict, processor.getContent())
    }

    @Test
    fun testVersionExtraction() {
        val processor = DependencyCmakeProcessor("test.cmake")
        val extractVersionMethod = processor.javaClass.getDeclaredMethod("extractVersion", String::class.java)
        extractVersionMethod.isAccessible = true
        
        val testCases = mapOf(
            "set(lv_videoeditor ******** CACHE INTERNAL \"\")" to "********",
            "set(QT_WIN_DEFAULT_VERSION \"6.2.2.536\" CACHE INTERNAL \"\")" to "6.2.2.536",
            "set(lv_openplugin_src 8.4.0.4 CACHE INTERNAL \"\")" to "8.4.0.4",
            "# This is a comment" to "",
            "set(lv_videoeditor_win \"\" CACHE INTERNAL \"\")" to ""
        )
        
        testCases.forEach { (input, expected) ->
            val result = extractVersionMethod.invoke(processor, input) as String
            Assertions.assertEquals(expected, result, "Failed for input: $input")
        }
    }
}
