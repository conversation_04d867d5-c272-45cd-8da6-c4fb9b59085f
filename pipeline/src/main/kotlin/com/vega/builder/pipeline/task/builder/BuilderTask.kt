package com.vega.builder.pipeline.task.builder

import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.isMockEnv
import com.vega.builder.common.utils.printDirectoryTree
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.context.buildTarget
import com.vega.builder.pipeline.context.isDebug
import com.vega.builder.pipeline.context.isGradleLoggerDebugMode
import com.vega.builder.pipeline.context.withAab
import com.vega.builder.pipeline.utils.SystemUtils
import org.koin.core.component.inject
import java.io.File

@TaskDefinition("BuilderTask", stage = Stage.Build, "Gradle Build", cachable = true)
class BuilderTask : PipelineTask() {
    val buildParams: BuildParams by inject()
    val workspaceParams: WorkspaceParams by inject()

    override val outputFiles: List<File>
        get() = listOf(
            File(PipelineOutput.buildUsagePath),
            File(PipelineOutput.buildMappingPath),
            File(PipelineOutput.buildMappingWithOutLinePath),
            File(PipelineOutput.buildResMappingPath),
            File(PipelineOutput.buildResMergeDuplicatedPath),
            File(PipelineOutput.buildDependency),
            File(PipelineOutput.buildSdkDependency),
            File(PipelineOutput.buildBundlePath),
            File(PipelineOutput.buildNativePathInfo),
            File(PipelineOutput.allClassJarPath),
            File(PipelineOutput.classDiffJarPath),
            File(PipelineOutput.rehaMethodMappingPath),
            File(
                listOfNotNull(PipelineOutput.buildPath, "cakeKnife", "cakeKnife.txt").joinToString(
                    separator = File.separator
                )
            ),
            File(
                listOfNotNull(
                    PipelineOutput.buildPath,
                    "hotfix-output",
                    "methodId.txt"
                ).joinToString(separator = File.separator)
            ),
            File(
                listOfNotNull(
                    PipelineOutput.buildPath, "ByteX",
                    "settings_collect_plugin",
                    "outputs",
                    "all_settings.json"
                ).joinToString(
                    separator = File.separator
                )
            ),
            *PipelineOutput.buildApkPaths.map { File(it) }.toTypedArray()
        )


    override suspend fun run() {
        val target = buildParams.buildTarget
        val flavor = target.flavor.replaceFirstChar { it.uppercaseChar() }
        val taskName = StringBuilder()
        val isDebug = buildParams.isDebug

        val isApkSizeOp = buildParams.isEnable("IS_APK_SIZE_OP", true)
        if (buildParams.withAab) {
            taskName.append("bundle")
        } else if (isApkSizeOp && !isDebug) {
            taskName.append("resguard")
        } else {
            taskName.append("assemble")
        }
        if (!buildParams.removeFlavor) {
            taskName.append(flavor)
        }
        if (isDebug) {
            taskName.append("Debug")
        } else {
            taskName.append("Release")
        }
        val result =
            Command("./gradlew")
                .directory(File(workspaceParams.targetProjectDir))
                .args("${target.moduleName}:$taskName")
                .arg("--parallel")
                .apply {
                    if (buildParams.isGradleLoggerDebugMode) {
                        arg("--debug")
                    }
                }
                .args("-x", "lint")
                .apply {
                    if (buildParams.isEnable("IS_PUSH_CACHE", false)) {
                        arg("-PpushGalaxyKtCache=true")
                    }
                    if (buildParams.isEnable("IS_PUSH_ACCESS", false) ||
                        buildParams.isEnable("IS_GRADLE_CACHE_PUBLISH", false)
                    ) {
                        arg("-Pbenchmark=true")
                        arg("-PbenchmarkType=PublishCache")
                    }
                }
                .arg("--stacktrace")
                .env(
                    "JAVA_TOOL_OPTIONS",
                    buildJavaEnv(),
                ).apply {
                    if (!isDebug) {
                        args("-x", "lintVital${flavor}Release")
                    }
                }.default()
                .spawn()
                .wait()
        File(PipelineOutput.buildOutputsPath).printDirectoryTree(5)
        if (result != 0) {
            throw PipelineThrowable(ErrorType.GradleBuildError)
        }
    }

    fun buildJavaEnv(): String {
        val originOption = getenvSafe("JAVA_TOOL_OPTIONS", "")
        val containerSupport = if (isMockEnv() || SystemUtils.isMac()) {
            ""
        } else {
            "-XX:-UseContainerSupport "
        }
        return "$originOption $containerSupport -Djava.net.preferIPv6Addresses=true"
    }
}
