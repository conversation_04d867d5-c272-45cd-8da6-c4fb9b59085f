package com.vega.builder.common.utils.compression

import com.vega.builder.common.logger.logger
import java.io.BufferedOutputStream
import java.io.File
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream

object Zip : Compression() {
    override val extension = ".zip"

    override fun compress(
        entries: List<FileEntry>,
        outputFile: File
    ) {
        BufferedOutputStream(outputFile.outputStream()).use { fileOut ->
            ZipOutputStream(fileOut).use { zipOut ->
                entries.sortedBy { it.path }.forEach { (file, path) ->
                    file.inputStream().buffered().use { input ->
                        logger().info("Compressing: $path")
                        val entry = ZipEntry(path)
                        zipOut.putNextEntry(entry)
                        if (file.isFile) file.inputStream().buffered().use { it.copyTo(zipOut) }
                        zipOut.closeEntry()
                    }
                }
            }
        }
    }

    override fun decompress(archiveFile: File, outputDir: File) {
        require(archiveFile.name.endsWith(".zip")) { "File must be a .zip archive" }
        require(outputDir.isDirectory) { "Output directory must exist" }

        archiveFile.inputStream().buffered().use { fis ->
            ZipInputStream(fis).use { zis ->
                generateSequence { zis.nextEntry }.forEach { entry ->
                    processZipEntry(entry, zis, outputDir)
                }
            }
        }
    }

    private fun processZipEntry(
        entry: ZipEntry,
        zipIn: ZipInputStream,
        outputDir: File
    ) {
        val targetFile = outputDir.resolve(entry.name)
            .validateSafeDestination(outputDir)

        when {
            entry.isDirectory -> targetFile.mkdirsSafe()
            else -> {
                targetFile.parentFile.mkdirsSafe()
                targetFile.outputStream().buffered().use { out ->
                    zipIn.copyTo(out)
                }
            }
        }
    }
}
