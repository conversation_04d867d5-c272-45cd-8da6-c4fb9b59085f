package com.vega.builder.pipeline.task.conflict

import java.io.File

class DependencyCmakeProcessor : FileConflictProcessor {
    constructor(filePath: String) : super(filePath)
    constructor(file: File, fileReader: ConflictFileReader) : super(file, fileReader)

    override suspend fun processConflictBlock(conflictBlock: ConflictBlock): List<String> {
        // First determine whether the number of conflicting rows is the same. CMake only resolves conflicts with the same number of rows
        if (conflictBlock.conflictLinesHead.size != conflictBlock.conflictLinesInsert.size) {
            throw IllegalStateException("CMake dependency only supports conflicts with the same number of lines")
        }
        // Determine the version to use by using the version in the conflict block
        // Find the line with version first
        val versionHead = conflictBlock.conflictLinesHead.first { extractVersion(it).isNotEmpty() }
        val versionInsert =
            conflictBlock.conflictLinesInsert.first { extractVersion(it).isNotEmpty() }
        // compare versions
        return if (isLargeThan(extractVersion(versionHead), extractVersion(versionInsert))) {
            conflictBlock.conflictLinesHead
        } else {
            conflictBlock.conflictLinesInsert
        }
    }

    private fun extractVersion(input: String): String {
        // Extract version from CMake set() command
        // Matches patterns like: set(variable_name version_number CACHE INTERNAL "")
        // Also handles quoted versions: set(variable_name "version_number" CACHE INTERNAL "")
        val regex = """set\s*\(\s*\w+\s+["']?([0-9]+(?:\.[0-9]+)*(?:\.[0-9a-zA-Z]+)*)["']?\s+CACHE\s+INTERNAL""".toRegex()
        val matchResult = regex.find(input)

        return if (matchResult != null) {
            matchResult.groupValues[1]
        } else {
            ""
        }
    }
}