package com.vega.builder.pipeline.task.conflict

import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.io.File

/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/20
 */
class VegaGradlePropertiesProcessorTest: BaseProcessorTest() {

    override val filePath: String
        get() = ""

    override var fileContent = """
            <<<<<<< HEAD
            APP_VERSION_NAME=13.4.0
            APP_VERSION_CODE=134000000
            UPDATE_VERSION_CODE=134000000
            
            # oversea version
            OVERSEA_APP_VERSION_NAME=11.4.0
            OVERSEA_APP_VERSION_CODE=11400000
            OVERSEA_UPDATE_VERSION_CODE=11400000
            =======
            APP_VERSION_NAME=13.3.0
            APP_VERSION_CODE=133000200
            UPDATE_VERSION_CODE=133000200
    
            # oversea version
            OVERSEA_APP_VERSION_NAME=11.3.0
            OVERSEA_APP_VERSION_CODE=11300100
            OVERSEA_UPDATE_VERSION_CODE=11300100
            >>>>>>> adecf30d587a479c6e8a205d9d8a6bf9159077af
            
            <<<<<<< HEAD
            VE_SDK_VERSION=18.5.0.0_rel_20250217214101_88bee482-lv
            VE_SDK_VERSION_OVERSEA=18.5.0.0_rel_20250217214045_88bee482_oversea-cc
            =======
            VE_SDK_VERSION=18.5.0.0_dev_20250217223425_93eff9e3_assert-lv
            VE_SDK_VERSION_OVERSEA=18.5.0.0_dev_20250217223305_93eff9e3_oversea_assert-cc
            >>>>>>> 14c88aa0debc71b76b7d9749ffa9c7b40c319418
            
            <<<<<<< HEAD
            ARTIFACT_VERSION = 0.0.1739354767116-SNAPSHOT
            =======
            ARTIFACT_VERSION = 0.0.1735638843483-SNAPSHOT
            >>>>>>> 7d432b901ac2d5b1e9860f1f1be69cbb37d41ba2
            
            haha
        """.trimIndent()

    override val resolveConflict = """
        APP_VERSION_NAME=13.4.0
        APP_VERSION_CODE=134000000
        UPDATE_VERSION_CODE=134000000
        
        # oversea version
        OVERSEA_APP_VERSION_NAME=11.4.0
        OVERSEA_APP_VERSION_CODE=11400000
        OVERSEA_UPDATE_VERSION_CODE=11400000
        
        VE_SDK_VERSION=18.5.0.0_dev_20250217223425_93eff9e3_assert-lv
        VE_SDK_VERSION_OVERSEA=18.5.0.0_dev_20250217223305_93eff9e3_oversea_assert-cc
        
        ARTIFACT_VERSION = 0.0.1739354767116-SNAPSHOT
        
        haha
    """.trimIndent()

    @Test
    fun testProcessConflictBlock() {
        val mockFile = mockFile()
        val mockFileReader = mockFileReader()
        val processor = VegaGradlePropertiesProcessor(mockFile, mockFileReader)
        runBlocking {
            processor.process()
        }
        assertEquals(processor.getContent().trimIndent(), resolveConflict)
    }

    override fun mockFile(): File {
        val file = mockk<File>()
        mockkStatic("kotlin.io.FilesKt__FileReadWriteKt")
        every { file.readText() } returns fileContent
        every { file.absolutePath } returns "/Users/<USER>/Work/AndroidProject/vega_project1/vega/CapCut/src/oversea/res/values-ar/do_not_modify_strings.xml"
        every { file.readLines() } returns fileContent.split("\n")
        every { file.writeText(any(), any()) } returns Unit
        return file
    }
}