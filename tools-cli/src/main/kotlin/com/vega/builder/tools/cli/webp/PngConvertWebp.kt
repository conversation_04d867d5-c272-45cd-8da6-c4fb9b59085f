package com.vega.builder.tools.cli.webp

import com.vega.builder.common.kommand.process.Command
import java.io.File
import kotlin.io.path.createTempDirectory
import kotlin.io.path.createTempFile

object PngConvertWebp {
    fun convert(rootFile: File) {
        val root = createTempDirectory("webp")
        rootFile.walk()
            .filter { it.isFile && it.extension == "png" && !it.name.endsWith(".9.png") }
            .toList().parallelStream()
            .forEach {
                val webpFile = File(it.parentFile, it.name.replace(".png", ".webp"))
                val tempFile = createTempFile(directory = root, suffix = ".webp").toFile()
                Command("cwebp")
                    .directory(root.toFile())
                    .arg(it.absolutePath)
                    .args("-q", "75")
                    .args("-m", "6", "-quiet", "-mt")
                    .arg("-o")
                    .arg(tempFile.absolutePath)
                    .spawn(false)
                    .wait()
                if (it.length() > tempFile.length()) {
                    println("convert ${it.absolutePath} to webp")
                    tempFile.renameTo(webpFile)
                    it.delete()
                } else {
                    println(
                        "convert skip webp size ${tempFile.length()} not smaller than ${it.length()} [${
                            it.relativeTo(
                                rootFile
                            )
                        }]"
                    )
                }
            }
    }
}