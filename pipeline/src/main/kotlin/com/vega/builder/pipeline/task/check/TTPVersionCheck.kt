package com.vega.builder.pipeline.task.check

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.PathDeclare
import org.koin.core.component.inject
import com.vega.builder.common.utils.fromJson
import com.vega.builder.pipeline.BuildConfig
import java.net.HttpURLConnection
import java.net.URL

@TaskDefinition(name = "ttp_version_check", stage = Stage.Check, displayName = "ttp version check")
class TTPVersionCheck : PipelineTask() {
    val pathDeclare: PathDeclare by inject<PathDeclare>()

    override suspend fun run() {
        if (pathDeclare.dependencyLockFile.exists() && pathDeclare.dependencyLockFile.isFile) {
            pathDeclare.dependencyLockFile.readText()
            val skipCheckList = listOf(
                "com.lemon.faceu:videoeditor_cc_repo_info",
                "com.lemon.faceu:lv_videoeditor-oversea",
                "com.lemon.faceu:lv_videoeditor_native-oversea"
            )
            val dependenciesLock = Gson().fromJson<LockFile>(pathDeclare.dependencyLockFile.reader())
            val mavenIds =
                dependenciesLock.dependencies.map(Component::toArtifactMavenList).flatten().filter { mavenId ->
                    !skipCheckList.any { mavenId.startsWith(it) }
                }


            val mavenStateList = mavenIds.map { mavenId ->
                val mavenInfo = mavenId.split(":")
                if (mavenInfo.size == 3) {
                    mavenId to checkArtifactExists(
                        mavenInfo[0], mavenInfo[1], mavenInfo[2], repoUrls = listOf(
                            "https://${BuildConfig.hide_tt_maven_gateway}/repository/tiktok-maven-release-group-ttp"
                        )
                    )
                } else {
                    mavenId to false
                }
            }
            if (mavenStateList.any { !it.second }) {
                logger().error("Found ${pathDeclare.dependencyLockFile.name}'s version not in TTP result:")
                for (mavenId in mavenStateList.filter { !it.second }) {
                    logger().error("  | ${mavenId.first} not found in TTP")
                }
                throw PipelineThrowable(ErrorType.TTPVersionCheck)
            }

        } else {
            throw PipelineThrowable(ErrorType.TTPVersionCheck)
        }
    }

    private fun checkArtifactExists(
        group: String,
        artifact: String,
        version: String,
        repoUrls: List<String>
    ): Boolean {
        // 构建POM路径
        val path = "${group.replace('.', '/')}/$artifact/$version/$artifact-$version.pom"

        val result = repoUrls.any { repoUrl ->
            try {
                val fullUrl = if (repoUrl.endsWith('/')) "$repoUrl$path" else "$repoUrl/$path"
                val checkResult = checkUrlExists(fullUrl)
                checkResult
            } catch (e: Exception) {
                e.printStackTrace()
                false
            }
        }
        return result
    }

    private fun checkUrlExists(urlString: String): Boolean {
        val url = URL(urlString)
        val connection = url.openConnection() as HttpURLConnection
        return try {
            connection.requestMethod = "HEAD"
            connection.connectTimeout = 3000
            connection.readTimeout = 3000
            connection.responseCode >= HttpURLConnection.HTTP_OK && connection.responseCode < HttpURLConnection.HTTP_MULT_CHOICE
        } catch (e: Exception) {
            false
        } finally {
            connection.disconnect()
        }
    }

}

data class LockFile(
    val dependencies: List<Component>
)

data class Component(
    val groupId: String? = null,
    val artifactId: String? = null,
    val version: String? = null,
    val targets: List<Target>? = null,
    val aliasName: String? = null,
    val flavorType: Boolean? = false
) {
    // 模拟用户提供的函数所需的上下文
    fun toArtifactMavenList(): Set<String> {
        val componentName = aliasName ?: artifactId
        if (componentName.isNullOrEmpty()) {
            // 对于无法处理的组件，返回空Map
            println("not found groupId/artifactId: $this")
            return emptySet()
        }
        if (groupId.isNullOrEmpty() || artifactId.isNullOrEmpty()) {
            println("groupId or artifactId is null: $this")
            return emptySet()
        }

        return try {
            if (targets.isNullOrEmpty()) {
                setOf("$groupId:$artifactId:$version")
            } else {
                targets.map {
                    if (flavorType == true) {
                        "$groupId:$artifactId-${it.name}:${it.version}"
                    } else {
                        "$groupId:$artifactId:$version"
                    }
                }.toSet()
            }
        } catch (e: Exception) {
            println("error: $this : ${e.message}")
            emptySet()
        }
    }
}

data class Target(
    val name: String,
    val version: String? = null
)