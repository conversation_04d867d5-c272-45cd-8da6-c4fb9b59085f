package com.vega.builder.pipeline.task.self

import com.vega.builder.common.utils.CommonPropertiesEditor
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PathDeclare
import org.koin.core.component.inject
import kotlin.getValue

@TaskDefinition("", stage = Stage.Prepare, displayName = "Publish pipeline-cli Params")
class PublishParamsTask : PipelineTask() {
    val buildParams by inject<BuildParams>()
    val pathDeclare by inject<PathDeclare>()

    override suspend fun run() {
        CommonPropertiesEditor(pathDeclare.loaclPropertiesFile).use { editor ->
            editor.edit("UPLOAD_TOS", "yes")
            buildParams["TOS_ENDPOINT"]?.let {
                editor.edit("TOS_ENDPOINT", it)
            } ?: throw NullPointerException("Param 'TOS_ENDPOINT' is not set")
            buildParams["TOS_AK"]?.let {
                editor.edit("TOS_AK", it)
            } ?: throw NullPointerException("Param 'TOS_AK' is not set")
            buildParams["TOS_BUCKET"]?.let {
                editor.edit("TOS_BUCKET", it)
            } ?: throw NullPointerException("Param 'TOS_BUCKET' is not set")

        }
    }
}