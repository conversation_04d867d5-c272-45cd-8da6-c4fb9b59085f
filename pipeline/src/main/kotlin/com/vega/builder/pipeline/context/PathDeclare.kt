package com.vega.builder.pipeline.context

import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.io.File
import kotlin.io.path.createTempFile


class PathDeclare(val workspaceParams: WorkspaceParams, val buildParams: BuildParams) : KoinComponent {
    val networkSecurityFile: File
        get() =
            File(
                "${workspaceParams.targetProjectDir}${File.separator}${buildParams.buildTarget.modulePath}${File.separator}src/main/res/xml/network_security_config.xml",
            )
    val ssPropertiesFile: File
        get() = File("${workspaceParams.targetProjectDir}${File.separator}${buildParams.buildTarget.modulePath}${File.separator}/src/main/assets/ss.properties")

    val loaclPropertiesFile: File
        get() = File("${workspaceParams.targetProjectDir}${File.separator}local.properties")

    val gradlePropertiesFile: File
        get() = File("${workspaceParams.targetProjectDir}${File.separator}gradle.properties")

    val customSbomFile: File by lazy {
        createTempFile("sbom", ".json").toFile()
    }

    val dependencyLockFile: File
        get() = File(listOf(workspaceParams.targetProjectDir, "dependency-lock.json").joinToString(File.separator))
}
/**
 *
 */

