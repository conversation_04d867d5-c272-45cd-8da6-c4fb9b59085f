package com.vega.builder.pipeline.task.conflict

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

/**
 * Test for PodfileSeerProcessor
 *
 * <AUTHOR>
 * @time 2025/1/20
 */
class PodfileSeerProcessorTest : BaseProcessorTest() {

    override val filePath: String = "Podfile.seer"

    override var fileContent = """
        - AALVLaunchTracker (path 'Modules/AALVLaunchTracker')
        - ABRInterface (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')
        <<<<<<< HEAD
        - ABRModule (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')
        =======
        - ABRModule (2.5.1, from '******************:iOS_Library/privatethird_binary_repo.git')
        >>>>>>> feature-branch
        - ABUAdCsjAdapter (*******.1, from '******************:ad/ad_union_source_repo.git')
        <<<<<<< HEAD
        - ADFeelGood (2.1.16, from '******************:iOS_Library/privatethird_binary_repo.git')
        =======
        - ADFeelGood (2.0.5, from '******************:iOS_Library/privatethird_binary_repo.git')
        >>>>>>> feature-branch
        - Ads-CN (*******-alpha.4, from '******************:ad/ad_union_source_repo.git')
        <<<<<<< HEAD
        - ByteDanceKit (git '******************:ugc/ByteDanceKit.git', commit 'a123b45c67d89ef01234567890abcdef12345678')
        =======
        - ByteDanceKit (git '******************:ugc/ByteDanceKit.git', commit 'a123b45c67d89ef01234567890abcdef12345678')
        >>>>>>> feature-branch
        - AFgzipRequestSerializer (0.0.6, from '******************:iOS_Library/privatethird_source_repo.git')
        - AFNetworking (2.7.0, from '******************:iOS_Library/publicthird_binary_repo.git')
    """.trimIndent()

    override val resolveConflict = """
        - AALVLaunchTracker (path 'Modules/AALVLaunchTracker')
        - ABRInterface (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')
        - ABRModule (2.5.1, from '******************:iOS_Library/privatethird_binary_repo.git')
        - ABUAdCsjAdapter (*******.1, from '******************:ad/ad_union_source_repo.git')
        - ADFeelGood (2.1.16, from '******************:iOS_Library/privatethird_binary_repo.git')
        - Ads-CN (*******-alpha.4, from '******************:ad/ad_union_source_repo.git')
        - ByteDanceKit (git '******************:ugc/ByteDanceKit.git', commit 'd993a91d42c29fda3ee32fe1cedf54f46ec08f72')
        - AFgzipRequestSerializer (0.0.6, from '******************:iOS_Library/privatethird_source_repo.git')
        - AFNetworking (2.7.0, from '******************:iOS_Library/publicthird_binary_repo.git')
    """.trimIndent()

    @Test
    fun testProcessConflictBlock() {
        val mockFile = mockFile()
        val mockFileReader = mockFileReader()
        val processor = PodfileSeerProcessor(mockFile, mockFileReader)
        runBlocking {
            processor.process()
        }
        Assertions.assertEquals(resolveConflict, processor.getContent())
    }

    @Test
    fun testVersionExtraction() {
        val processor = PodfileSeerProcessor("test.seer")
        
        // 使用反射访问私有方法进行测试
        val extractVersionMethod = processor.javaClass.getDeclaredMethod("extractVersion", String::class.java)
        extractVersionMethod.isAccessible = true
        
        val testCases = mapOf(
            "- ABRModule (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')" to "2.4.0",
            "- ADFeelGood (2.1.16, from '******************:iOS_Library/privatethird_binary_repo.git')" to "2.1.16",
            "- Ads-CN (*******-alpha.4, from '******************:ad/ad_union_source_repo.git')" to "*******-alpha.4",
            "- ByteDanceKit (git '******************:ugc/ByteDanceKit.git', commit 'd993a91d42c29fda3ee32fe1cedf54f46ec08f72')" to "d993a91d42c29fda3ee32fe1cedf54f46ec08f72",
            "- AALVLaunchTracker (path 'Modules/AALVLaunchTracker')" to "",
            "# This is a comment" to ""
        )
        
        testCases.forEach { (input, expected) ->
            val result = extractVersionMethod.invoke(processor, input) as String
            Assertions.assertEquals(expected, result, "Failed for input: $input")
        }
    }
}
