plugins {
    kotlin("jvm")
    alias(libs.plugins.kotlinx.serialization)
    alias(libs.plugins.build.shell)

}
version = "1.0.0"

dependencies {
    implementation(kotlin("stdlib-common"))
    implementation(kotlin("reflect"))

    implementation(project(":common"))
    implementation(project(":kotlin-psi"))


    implementation(libs.bundles.kotlinx.official.libs)
    implementation(libs.okio)
    implementation(libs.bundles.retrofit)
    implementation(libs.bundles.jgit)
    implementation(libs.gson)
    implementation(libs.bundles.log)
    implementation(libs.tos.core)
    implementation(libs.kotlinx.coroutines)
    implementation(libs.bundles.koin) {
        exclude(group = "org.slf4j", module = "slf4j-api")
    }
    implementation(libs.source.manager.core)
    implementation(libs.kotlin.compiler.embeddable)

    testImplementation(kotlin("test"))
    testImplementation("io.mockk:mockk:1.12.0")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.5.2")
}
kotlin {
    jvmToolchain(11)
}

tasks.test {
    useJUnitPlatform()
}

standalone {
    mainClass = "com.vega.builder.pipeline.MainKt"
    baseName = "pipeline"
    enableProguard = true
    proguardFiles.add("${project.file("proguard-rules.pro")}")
}

