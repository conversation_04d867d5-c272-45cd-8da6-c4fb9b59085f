package com.vega.builder.common.utils.compression

import com.vega.builder.common.logger.logger
import kotlinx.io.IOException
import org.apache.commons.compress.archivers.tar.TarArchiveEntry
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream
import java.io.BufferedOutputStream
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.attribute.FileTime
import java.nio.file.attribute.PosixFilePermission

object TarGz : Compression() {
    override val extension = ".tar.gz"

    override fun compress(
        entries: List<FileEntry>,
        outputFile: File
    ) {
        BufferedOutputStream(outputFile.outputStream()).use { fileOut ->
            GzipCompressorOutputStream(fileOut).use { gzipOut ->
                TarArchiveOutputStream(gzipOut).use { tarOut ->
                    tarOut.setLongFileMode(TarArchiveOutputStream.LONGFILE_POSIX)
                    entries.sortedBy { it.path }.forEach { (file, path) ->
                        logger().info("Packaging: $path")
                        val entry = tarOut.createArchiveEntry(file, path)
                        tarOut.putArchiveEntry(entry)
                        entry.mode = getFilePermissions(file)
                        entry.lastModifiedTime = FileTime.fromMillis(file.lastModified())
                        if (file.isFile) file.inputStream().buffered().use { it.copyTo(tarOut) }
                        tarOut.closeArchiveEntry()
                    }
                }
            }
        }
    }

    override fun decompress(archiveFile: File, outputDir: File) {
        require(archiveFile.name.endsWith(".tar.gz")) { "File must be a .tar.gz archive" }
        require(outputDir.isDirectory) { "Output directory must exist" }

        archiveFile.inputStream().buffered().use { fis ->
            GzipCompressorInputStream(fis).use { gzIn ->
                TarArchiveInputStream(gzIn).use { tarIn ->
                    generateSequence { tarIn.nextEntry }.forEach { entry ->
                        processTarEntry(entry, tarIn, outputDir)
                    }
                }
            }
        }
    }

    private fun processTarEntry(
        entry: TarArchiveEntry,
        tarIn: TarArchiveInputStream,
        outputDir: File
    ) {
        val targetFile = outputDir.resolve(entry.name)
            .validateSafeDestination(outputDir)

        when {
            entry.isDirectory -> targetFile.mkdirsSafe()
            entry.isSymbolicLink -> handleSymlink(entry, targetFile)
            else -> {
                targetFile.parentFile.mkdirsSafe()
                targetFile.outputStream().buffered().use { out ->
                    tarIn.copyTo(out)
                }
                // Preserve file attributes
                targetFile.setLastModified(entry.lastModifiedDate.time)
                Files.setPosixFilePermissions(targetFile.toPath(), convertModeToPosixPermissions(entry.mode))
            }
        }
    }

    private fun handleSymlink(entry: TarArchiveEntry, target: File) {
        try {
            Files.createSymbolicLink(
                target.toPath(),
                Paths.get(entry.linkName)
            )
        } catch (e: IOException) {
            logger().warn("Failed to create symbolic link: ${entry.linkName}", e)
        } catch (e: SecurityException) {
            logger().warn("No permission to create symbolic link: ${entry.linkName}", e)
        }
    }

    private fun getFilePermissions(file: File): Int {
        val permissions = Files.getPosixFilePermissions(file.toPath())
        return permissions.fold(0) { acc, perm ->
            acc or when (perm) {
                PosixFilePermission.OWNER_READ -> 0x100
                PosixFilePermission.OWNER_WRITE -> 0x80
                PosixFilePermission.OWNER_EXECUTE -> 0x40
                PosixFilePermission.GROUP_READ -> 0x20
                PosixFilePermission.GROUP_WRITE -> 0x10
                PosixFilePermission.GROUP_EXECUTE -> 0x8
                PosixFilePermission.OTHERS_READ -> 0x4
                PosixFilePermission.OTHERS_WRITE -> 0x2
                PosixFilePermission.OTHERS_EXECUTE -> 0x1
            }
        }
    }

    fun convertModeToPosixPermissions(mode: Int): Set<PosixFilePermission> {
        val perms = mutableSetOf<PosixFilePermission>()
        if (mode and 0x100 != 0) perms.add(PosixFilePermission.OWNER_READ)
        if (mode and 0x80 != 0) perms.add(PosixFilePermission.OWNER_WRITE)
        if (mode and 0x40 != 0) perms.add(PosixFilePermission.OWNER_EXECUTE)
        if (mode and 0x20 != 0) perms.add(PosixFilePermission.GROUP_READ)
        if (mode and 0x10 != 0) perms.add(PosixFilePermission.GROUP_WRITE)
        if (mode and 0x8 != 0) perms.add(PosixFilePermission.GROUP_EXECUTE)
        if (mode and 0x4 != 0) perms.add(PosixFilePermission.OTHERS_READ)
        if (mode and 0x2 != 0) perms.add(PosixFilePermission.OTHERS_WRITE)
        if (mode and 0x1 != 0) perms.add(PosixFilePermission.OTHERS_EXECUTE)
        return perms
    }
}
