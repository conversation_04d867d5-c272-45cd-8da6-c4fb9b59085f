package com.vega.builder.common.utils

import java.io.File
import java.nio.ByteBuffer
import java.nio.channels.FileChannel
import java.nio.file.StandardOpenOption
import java.security.MessageDigest

object MD5Utils {
    fun ofFile(filePath: String): String {
        val digest = MessageDigest.getInstance("MD5")
        File(filePath).inputStream().use { fis ->
            val byteArray = ByteArray(1024)
            var bytesCount: Int
            while (fis.read(byteArray).also { bytesCount = it } != -1) {
                digest.update(byteArray, 0, bytesCount)
            }
        }
        val bytes = digest.digest()
        return bytes.joinToString("") {
            "%02x".format(it)
        }
    }

    fun ofString(content: String): String {
        val digest = MessageDigest.getInstance("MD5")
        digest.update(content.toByteArray())
        val bytes = digest.digest()
        return bytes.joinToString("") {
            "%02x".format(it)
        }
    }
}

private val HEX_ARRAY = "0123456789abcdef".toCharArray()

object SHA256Utils {

    fun ofFile(file: File): String {
        val sha256 = MessageDigest.getInstance("SHA-256")
        FileChannel.open(file.toPath(), StandardOpenOption.READ).use { channel ->
            val buffer = ByteBuffer.allocate(8192)
            while (channel.read(buffer) != -1) {
                buffer.flip()
                sha256.update(buffer)
                buffer.clear()
            }
        }
        val result = sha256.digest()
        return bytesToHex(result)
    }


    private fun bytesToHex(bytes: ByteArray): String {
        val hexChars = CharArray(bytes.size * 2)
        for (i in bytes.indices) {
            val v = bytes[i].toInt() and 0xff
            hexChars[i * 2] = HEX_ARRAY[v ushr 4]
            hexChars[i * 2 + 1] = HEX_ARRAY[v and 0x0f]
        }
        return String(hexChars)
    }

}