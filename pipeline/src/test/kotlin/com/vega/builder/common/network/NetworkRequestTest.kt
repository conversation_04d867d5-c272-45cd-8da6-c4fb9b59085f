package com.vega.builder.common.network

import com.vega.builder.common.airplane.AirplaneResponse
import kotlin.test.Test
import com.vega.builder.common.airplane.IAirplaneApi
import kotlinx.coroutines.runBlocking

class NetworkRequestTest {
    @Test
    fun test() = runBlocking {
        val result = request(IAirplaneApi::queryTemplate, "MR_BUILD_CC")

        request<IAirplaneApi, AirplaneResponse<Map<String, String>>> {
            queryTemplate("MR_BUILD_CC")
        }
        println(result)
    }
}