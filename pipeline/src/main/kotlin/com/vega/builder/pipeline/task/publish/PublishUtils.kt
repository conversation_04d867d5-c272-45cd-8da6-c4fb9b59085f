package com.vega.builder.pipeline.task.publish

import com.vega.builder.pipeline.task.publish.PublishSourceTask.Companion.SOURCE_FILE_EXTENSION

object PublishUtils {
    fun buildTosId(id: String, version: String): String {
        return if (version.endsWith("SNAPSHOT")) {
            "SS/$id/$version${SOURCE_FILE_EXTENSION}"
        } else {
            "$id/$version${SOURCE_FILE_EXTENSION}"
        }
    }

}