package com.vega.builder.pipeline.task.conflict

import com.vega.builder.common.psi.asttools.KotlinParserUtil
import org.jetbrains.kotlin.com.intellij.psi.PsiErrorElement
import org.jetbrains.kotlin.com.intellij.psi.PsiRecursiveElementVisitor
import org.jetbrains.kotlin.psi.KtFile
import org.jetbrains.kotlin.psi.KtImportInfo
import java.io.File

fun KtFile.hasSyntaxErrors(): Boolean {
    var hasErrors = false
    this.accept(object : PsiRecursiveElementVisitor() {
        override fun visitErrorElement(element: PsiErrorElement) {
            super.visitErrorElement(element)
            hasErrors = true // syntax errors were found
            println("hasSyntaxErrors: " + element.errorDescription + ", " + element.text)
        }
    })
    return hasErrors
}

/**
 *
 *
 * <AUTHOR>
 * @time 2025/3/11
 */
class KotlinProcessor : FileConflictProcessor {

    constructor(filePath: String) : super(filePath)
    constructor(file: File, fileReader: ConflictFileReader) : super(file, fileReader)

    private val canProcessBothAddFiles = listOf(
        "LVDatabase.kt",
        "KoinDeeplinkProviderModule.kt",
        "EditorCreateDeepLinkData.kt",
    )

    override suspend fun processEnd(): Boolean {
        val success = super.processEnd()
        return success && !KotlinParserUtil.parseAsFile(fileReader.getContent(), file.absolutePath).hasSyntaxErrors()
    }

    override suspend fun processConflictBlock(conflictBlock: ConflictBlock): List<String> {
        if (canProcessImport(conflictBlock)) {
            return processImport(conflictBlock)
        } else if (canProcessBothAdd(conflictBlock)) {
            return processBothAdd(conflictBlock)
        } else {
            throw IllegalStateException("Only import conflicts and fixed file conflicts are supported")
        }
    }

    private fun processBothAdd(conflictBlock: ConflictBlock): MutableList<String> {
        val result = mutableListOf<String>()
        result.addAll(conflictBlock.conflictLinesHead)
        result.addAll(conflictBlock.conflictLinesInsert)
        println("processBothAdd: " + result)
        return result
    }

    private fun processImport(conflictBlock: ConflictBlock): MutableList<String> {
        // 先判断相对于Base是有新的插入，还是有修改
        val headImport = conflictBlock.conflictLinesHead.filter { it.isNotBlank() }.map {
            KotlinParserUtil.parseAsImportStatement(it)
        }
        val baseImport = conflictBlock.conflictLinesBase.filter { it.isNotBlank() }.map {
            KotlinParserUtil.parseAsImportStatement(it)
        }
        val insertImport = conflictBlock.conflictLinesInsert.filter { it.isNotBlank() }.map {
            KotlinParserUtil.parseAsImportStatement(it)
        }
        val headImportAdd = headImport.filter {
            baseImport.none { base ->
                base.isAllUnder == it.isAllUnder && base.alias == it.alias && base.importedFqName == it.importedFqName
            }
        }
        val headImportDelete = baseImport.filter {
            headImport.none { head ->
                head.isAllUnder == it.isAllUnder && head.alias == it.alias && head.importedFqName == it.importedFqName
            }
        }
        val insertImportAdd = insertImport.filter {
            baseImport.none { base ->
                base.isAllUnder == it.isAllUnder && base.alias == it.alias && base.importedFqName == it.importedFqName
            }
        }
        val insertImportDelete = baseImport.filter {
            insertImport.none { insert ->
                insert.isAllUnder == it.isAllUnder && insert.alias == it.alias && insert.importedFqName == it.importedFqName
            }
        }
        // Make sure that the imported imports have no duplicate names
        if (insertImportAdd.any { insert ->
                headImportAdd.find { head ->
                    extractPackageAndClassName(head).second == extractPackageAndClassName(
                        insert
                    ).second
                } != null
            }) {
            throw IllegalStateException("The inserted import has a duplicate name")
        }
        // Merge into the final result
        val result = mutableListOf<String>()
        result.addAll(baseImport.map { it.toString() })
        result.addAll(headImportAdd.map { it.toString() })
        result.addAll(insertImportAdd.map { it.toString() })
        result.removeAll(headImportDelete.map { it.toString() })
        result.addAll(insertImportDelete.map { it.toString() })
        return result
    }

    private fun canProcessImport(conflictBlock: ConflictBlock): Boolean {
        // Ensure that all conflicts handled are import conflicts and are kotlin files
        return conflictBlock.conflictLinesHead.all {
            it.startsWith("import") || it.isBlank()
        } && conflictBlock.conflictLinesInsert.all {
            it.startsWith("import") || it.isBlank()
        } && conflictBlock.conflictLinesBase.all {
            it.startsWith("import") || it.isBlank()
        }
    }

    private fun canProcessBothAdd(conflictBlock: ConflictBlock): Boolean {
        // Make sure you can process files and add them
        val noModify = conflictBlock.conflictLinesBase.isEmpty()
        val canProcessFile = file.name in canProcessBothAddFiles
        return noModify && canProcessFile
    }

    private fun extractPackageAndClassName(importInfo: KtImportInfo): Pair<String?, String?> {
        val fqName = importInfo.importedFqName ?: return null to null

        return if (importInfo.isAllUnder) {
            // wildcard import（eg. `import java.util.*`）
            fqName.asString() to null
        } else {
            // general introduction（eg. `import java.util.HashMap`）
            val packageName = fqName.parent()?.asString() // Parent path (package name)
            val className = fqName.shortName().asString() // Last level (class name/function name)
            packageName to className
        }
    }
}