package com.vega.builder.pipeline.task.artifacts

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.IJFrogApi
import com.vega.builder.common.network.request
import com.vega.builder.common.utils.ResourceUtils
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.retry
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.task.artifacts.TTPArtifactsUpload.Companion.TAG
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.asRequestBody
import org.koin.core.component.inject
import java.io.File
import java.util.regex.Pattern
import java.util.regex.PatternSyntaxException
import kotlin.io.path.writeText

@TaskDefinition(TAG, stage = Stage.After, displayName = "TTP Artifacts Upload")
class TTPArtifactsUpload : PipelineTask() {
    companion object {
        const val TAG = "TTPArtifactsUpload"
        const val UPLOAD_KEY_PREFIX = "CapCut/android/pipeline_result/result_"
    }

    val buildParams: BuildParams by inject()

    val workspaceParams: WorkspaceParams by inject()

    val pipelineContext: PipelineContextImpl by inject()


    override suspend fun run() {
        val uploadResult = mutableListOf<ArtifactsUploadResult>()

        val uploadBaseKey = "${UPLOAD_KEY_PREFIX}${getenvSafe("WORKFLOW_JOB_ID")}"
        PipelineOutput.buildOutputs().forEach { (type, value) ->
            val file = File(value)
            file.extension
            if (file.exists()) {
                val uploadFileKey = "${uploadBaseKey}/file/${file.name}"

                val uploadFileResult = retry(3, 50) {
                    val result = request(
                        IJFrogApi::upload,
                        uploadFileKey,
                        file.asRequestBody("application/octet-stream".toMediaType())
                    )
                    if (!result.isSuccessful || result.body() == null) {
                        throw Exception("Error uploading $uploadFileKey")
                    }
                    result.body()!!
                }
                uploadResult.add(
                    ArtifactsUploadResult(
                        name = file.name,
                        url = uploadFileKey,
                        type = type,
                        md5 = uploadFileResult.checksums.md5,
                        size = uploadFileResult.size.toLong(),
                    )
                )
            } else {
                logger().info("file does not exist: ${file.absolutePath},skip upload")
            }
        }

        val resultManifest = kotlin.io.path.createTempFile(prefix = "uploadResult", suffix = ".json")
        val manifestContent = Gson().toJson(uploadResult)
        logger().info("manifest content: $manifestContent")
        resultManifest.writeText(manifestContent)
        val uploadManifestResult = retry(3, 50) {
            val result = request(
                IJFrogApi::upload,
                "${uploadBaseKey}/manifest.json",
                resultManifest.toFile().asRequestBody("application/octet-stream".toMediaType())
            )
            if (!result.isSuccessful || result.body() == null) {
                throw Exception("Error uploading $uploadBaseKey")
            }
            result.body()!!
        }
        val requiredArtifactsRuleContent = buildParams["build_required_artifacts_rule"] ?: return
        val ruleNameList = requiredArtifactsRuleContent.split(",")
        val ruleList = ruleNameList.map {ruleName->
            val checkerContent = ResourceUtils.readResource("ttp-checker/$ruleName.json")
            Gson().fromJson<RequiredFileRule>(checkerContent)
        }
        val result = RequiredFileChecker(ruleList).checker(uploadResult)
        if (!result) {
            throw Exception("Has Artifacts Upload not match all rules:${requiredArtifactsRuleContent}")
        }
        logger().info("upload result: ${Gson().toJson(uploadManifestResult)}")
    }
}

data class RequiredFileRule(
    val type: RequiredFileRuleType,
    val name: String,
    val rule: String,
) {
    // 预编译正则表达式（非Regex类型时为null）
    private val regexPattern: Pattern? by lazy {
        if (type == RequiredFileRuleType.Regex) {
            try {
                // 编译正则表达式（不区分大小写）
                Pattern.compile(rule, Pattern.CASE_INSENSITIVE)
            } catch (e: PatternSyntaxException) {
                throw IllegalArgumentException("Invalid regex pattern: '$rule'", e)
            }
        } else {
            null
        }
    }

    // 检查单个文件是否满足当前规则
    fun checker(result: ArtifactsUploadResult): Boolean {
        return when (type) {
            RequiredFileRuleType.Type -> result.type.equals(rule, ignoreCase = true)
            RequiredFileRuleType.Extension -> {
                val normalizedRule = if (rule.startsWith(".")) rule else ".$rule"
                result.name.endsWith(normalizedRule, ignoreCase = true)
            }

            RequiredFileRuleType.Regex -> {
                // 使用预编译的正则进行匹配
                regexPattern?.matcher(result.name)?.find() ?: false
            }
        }.apply {
            logger().info("checker[${name}] output: $this ,input: ${Gson().toJson(result)}")
        }
    }
}

enum class RequiredFileRuleType {
    Type, Extension, Regex
}


data class RequiredFileChecker(val list: List<RequiredFileRule>) {
    fun checker(resultList: List<ArtifactsUploadResult>): Boolean {
        if (list.isEmpty()) return true

        return list.all { rule ->
            resultList.any { file ->
                rule.checker(file)
            }
        }
    }

}

