import org.eclipse.jgit.api.Git
import java.text.SimpleDateFormat
import java.util.*
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlin.collections.contains

plugins {
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlinx.serialization)
}

version = "1.0.0"

sourceSets["main"].kotlin.setSrcDirs(
    sourceSets["main"].kotlin.srcDirs + listOf("${layout.buildDirectory.asFile.get()}/generated/source/kotlin/"),
)
dependencies {
    implementation(fileTree(mapOf("dir" to rootProject.file("libs").absolutePath, "include" to listOf("*.jar"))))
    implementation(kotlin("stdlib-common"))
    implementation(kotlin("reflect"))

    implementation(libs.bundles.kotlinx.official.libs)
    implementation(libs.okio)
    implementation(libs.bundles.retrofit)
    implementation(libs.bundles.koin) {
        exclude(group = "org.slf4j", module = "slf4j-api")
    }
    implementation(libs.jgit)
    implementation(libs.gson)
    implementation(libs.tos.core)
    implementation(libs.bundles.log)
    implementation("org.apache.commons:commons-compress:1.26.0")
    // https://mvnrepository.com/artifact/org.tukaani/xz
    implementation("org.tukaani:xz:1.10")

}
kotlin {
    jvmToolchain(11)
}

afterEvaluate {
    val buildDir = "${layout.buildDirectory.asFile.get()}/generated/source/kotlin/com/vega/builder/pipeline"
    val fileName = "BuildConfig.kt"
    val date = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date())
    val info = queryBuildInfo()
    val env = System.getenv()
    val content =
        """
        package com.vega.builder.pipeline
        
        object BuildConfig {
            const val VERSION = "${env.getOrDefault("version", project.version)}"
            const val BUILD_DATE = "$date"
            ${info.entries.joinToString("\n") { "const val ${it.key} = \"${it.value}\"" }}
        }
        """.trimIndent()
    File(buildDir, fileName)
        .apply {
            if (!parentFile.exists()) {
                parentFile.mkdirs()
            }
        }.writeText(content)
    println("生成:BuildConfig.kt")
    println(content)
}

fun queryBuildInfo(): Map<String, String> {
    val env = System.getenv()
    env.forEach {
        println("env: ${it.key} = ${it.value}")
    }
    val info = mutableMapOf<String, String>()
    if (env.containsKey("WORKFLOW_PIPELINE_ID")) {
        info["pipelineId"] = env["WORKFLOW_PIPELINE_ID"] ?: ""
    }
    if (env.containsKey("TARGETCODEPATH")) {
        val workspace = env["TARGETCODEPATH"] ?: ""
        if (workspace.isNotBlank()) {
            val git = Git.open(File(workspace))
            val commitId = git.repository.resolve("HEAD")
            val commit = git.repository.parseCommit(commitId)
            info["commitId"] = commitId.name
            info["author"] = commit.authorIdent.emailAddress
        }
    }
    queryExtInfo(info)
    return info
}


data class BitsProperty(val ext_info: String)

fun exportByteBusInfo(): Map<String, String> {
    val envs = System.getenv()

    return if (envs.contains("bytebusInfo")) {
        val byteBusInfo = envs.getOrDefault("bytebusInfo", "")
        val bitsPlatform = Gson().fromJson(
            byteBusInfo.toString(),
            BitsProperty::class.java
        )
        val extParams = bitsPlatform.ext_info
        println("ext_params${extParams}")
        if (extParams != "") {
            Gson().fromJson<Map<String, String>>(
                extParams,
                object : TypeToken<Map<String, String>>() {}.type
            )
        } else {
            emptyMap()
        }
    } else {
        emptyMap()
    }
}

fun exportFromLocal(): Map<String, String> {
    if (!rootProject.file("config.json").exists()) {
        throw RuntimeException("config.json not found")
    }
    return Gson().fromJson<Map<String, String>>(
        rootProject.file("config.json").bufferedReader(Charsets.UTF_8),
        object : TypeToken<Map<String, String>>() {}.type
    )
}

fun exportFromEnv(): Map<String, String> {
    val extParams = System.getenv().getOrDefault("ext_params", "")
    if (extParams != "") {
        return Gson().fromJson<Map<String, String>>(extParams, object : TypeToken<Map<String, String>>() {}.type)
    } else {
        println("ext_params is empty or blank")
    }
    return emptyMap()
}

fun queryExtInfo(info: MutableMap<String, String>): Map<String, String> {
    var buildParams = exportByteBusInfo()
    if (buildParams.isEmpty()) {
        buildParams = exportFromEnv()
    }
    if (buildParams.isEmpty()) {
        buildParams = exportFromLocal()
    }
    info["hide_tt_maven"] = buildParams.getOrDefault("hide_tt_maven", "")
    info["hide_tt_maven_gateway"] = buildParams.getOrDefault("hide_tt_maven_gateway", "")
    info["hide_ttp_artifactory_url"] = buildParams.getOrDefault("hide_ttp_artifactory_url", "")
    info["hide_dex_vmp_url"] = buildParams.getOrDefault("hide_dex_vmp_url", "")
    info["hide_slardar_symbol_url"] = buildParams.getOrDefault("hide_slardar_symbol_url", "")
    info["hide_pa_build_url"] = buildParams.getOrDefault("hide_pa_build_url", "")
    info["hide_pa_url"] = buildParams.getOrDefault("hide_pa_url", "")
    info["hide_starling_url"] = buildParams.getOrDefault("hide_starling_url", "")
    info["hide_voffline_rul"] = buildParams.getOrDefault("hide_voffline_rul", "")

    info["hide_tos_url"] = buildParams.getOrDefault("hide_tos_url", "")
    info["hide_tos_endpoint_url"] = buildParams.getOrDefault("hide_tos_endpoint_url", "")
    info["hide_mobile_url"] = buildParams.getOrDefault("hide_mobile_url", "")
    info["hide_bits_token"] = buildParams.getOrDefault("hide_bits_token", "")
    info["hide_LvBuildScript_ak"] = buildParams.getOrDefault("hide_LvBuildScript_ak", "")
    info["hide_LvBuildArtifact_ak"] = buildParams.getOrDefault("hide_LvBuildArtifact_ak", "")
    info["hide_LvBuildResult_ak"] = buildParams.getOrDefault("hide_LvBuildResult_ak", "")

    return info
}