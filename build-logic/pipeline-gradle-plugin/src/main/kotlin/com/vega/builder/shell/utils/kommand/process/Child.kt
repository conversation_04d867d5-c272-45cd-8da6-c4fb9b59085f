package com.vega.builder.shell.utils.kommand.process

import com.vega.builder.shell.utils.kommand.io.BufferedReader
import com.vega.builder.shell.utils.kommand.io.BufferedWriter
import com.vega.builder.shell.utils.kommand.io.Output
import com.vega.builder.shell.utils.kommand.KommandErrorType
import com.vega.builder.shell.utils.kommand.KommandException
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.util.concurrent.atomic.AtomicReference

class Child(
    private val process: Process,
) {
    private var stdin: AtomicReference<BufferedWriter?> = AtomicReference(null)
    private var stdout: AtomicReference<BufferedReader?> = AtomicReference(null)
    private var stderr: AtomicReference<BufferedReader?> = AtomicReference(null)

    fun id(): UInt = process.pid().toUInt()

    fun bufferedStdin(): BufferedWriter? {
        stdin.compareAndSet(null, BufferedWriter(java.io.BufferedWriter(OutputStreamWriter(process.outputStream))))
        return stdin.get()
    }

    fun bufferedStdout(): BufferedReader? {
        stdout.compareAndSet(null, BufferedReader(java.io.BufferedReader(InputStreamReader(process.inputStream))))
        return stdout.get()
    }

    fun bufferedStderr(): BufferedReader? {
        stderr.compareAndSet(null, BufferedReader(java.io.BufferedReader(InputStreamReader(process.errorStream))))
        return stderr.get()
    }

    @Throws(KommandException::class)
    fun kill() {
        stdin.get()?.close()
        process.destroy()
    }

    @Throws(KommandException::class)
    fun wait(): Int {
        stdin.get()?.close()
        return process.waitFor()
    }

    @Throws(KommandException::class)
    fun waitWithOutput(): Output {
        stdin.get()?.close()
        val stdoutContent =
            runCatching { bufferedStdout()?.readAll() }
                .getOrElse { throw KommandException("Child has been consumed", KommandErrorType.None) }
        val stderrContent =
            runCatching { bufferedStderr()?.readAll() }
                .getOrElse { throw KommandException("Child has been consumed", KommandErrorType.None) }
        val status = process.waitFor()
        stdout.get()?.close()
        stderr.get()?.close()
        return Output(status, stdoutContent, stderrContent)
    }
}
