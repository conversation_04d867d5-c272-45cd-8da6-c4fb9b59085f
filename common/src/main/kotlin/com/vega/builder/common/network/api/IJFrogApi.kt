package com.vega.builder.common.network.api

import com.vega.builder.common.network.BaseUrl
import com.vega.builder.pipeline.BuildConfig
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.PUT
import retrofit2.http.Path
import com.google.gson.annotations.SerializedName


/**
 * curl --request PUT https://artifactory.byted.org/repository/mobile-build/${your path} -T
 * 国内下载地址: https://tiktok-maven-gateway.byted.org/repository/mobile-build-copy/${key}
 * TTP下载地址: https://artifactory.byted.org/artifactory/mobile-build-copy/${key}
 *
 */
@BaseUrl("https://${BuildConfig.hide_ttp_artifactory_url}/repository/mobile-build/")
interface IJFrogApi {
    @PUT("{path}")
    suspend fun upload(
        @Path("path") fullPath: String,
        @Body requestBody: RequestBody
    ): Response<JfrogResp>
}

@BaseUrl("https://maven.byted.org/repository/bytedance_android/")
interface IBytedanceAndroidApi {
    @PUT("{path}")
    suspend fun upload(
        @Path("path") fullPath: String,
        @Body requesdatatBody: RequestBody
    ): Response<ResponseBody>
}

class JfrogResp(
    @SerializedName("checksums")
    val checksums: Checksums,
    @SerializedName("created")
    val created: String,
    @SerializedName("createdBy")
    val createdBy: String,
    @SerializedName("downloadUri")
    val downloadUri: String,
    @SerializedName("mimeType")
    val mimeType: String,
    @SerializedName("originalChecksums")
    val originalChecksums: OriginalChecksums,
    @SerializedName("path")
    val path: String,
    @SerializedName("repo")
    val repo: String,
    @SerializedName("size")
    val size: String,
    @SerializedName("uri")
    val uri: String
)

data class Checksums(
    @SerializedName("md5")
    val md5: String,
    @SerializedName("sha1")
    val sha1: String,
    @SerializedName("sha256")
    val sha256: String
)

data class OriginalChecksums(
    @SerializedName("sha256")
    val sha256: String
)
