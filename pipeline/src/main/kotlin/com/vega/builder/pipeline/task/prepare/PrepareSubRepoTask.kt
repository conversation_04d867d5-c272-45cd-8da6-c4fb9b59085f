package com.vega.builder.pipeline.task.prepare

import com.google.gson.Gson
import com.vega.builder.common.git.GitXWrapper
import com.vega.builder.common.git.extractRepositoryName
import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.BaseUrl
import com.vega.builder.common.network.request
import com.vega.builder.common.utils.BitsUtils
import com.vega.builder.common.utils.CommonPropertiesEditor
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.WorkspaceParams
import kotlinx.serialization.Serializable
import org.koin.core.component.inject
import retrofit2.http.Body
import retrofit2.http.POST
import java.io.File
import kotlin.getValue

@TaskDefinition("LoadSubRepo", stage = Stage.Prepare, displayName = "Clone Sub Repo")
class PrepareSubRepoTask : PipelineTask() {


    val pipelineContext: PipelineContextImpl by inject()
    val workspaceParams: WorkspaceParams by inject()


    override suspend fun run() {
        if (pipelineContext.buildId != PipelineContextImpl.EMPTY_BUILD_ID) {
            val result =
                request(
                    IBuildInfoRequest::buildInfo,
                    BuildInfo(pipelineContext.buildId),
                )
            logger().info("Get sub repo[${pipelineContext.buildId}]info:${Gson().toJson(result)}")
            val repoList = result.data
            if (result.code == 0 && !repoList.isNullOrEmpty()) {
                GitXWrapper.downloadGitX(File(pipelineContext.workspace, ".gitx"))
                CommonPropertiesEditor(
                    File(
                        workspaceParams.targetProjectDir,
                        "ci-multi-source-config.properties"
                    )
                ).use { editor ->
                    repoList.forEach { repo ->
                        val repoName =
                            extractRepositoryName(repo.repoUrl) ?: return@forEach logger().warn(
                                "from [${repo.repoUrl}] extract Repository name Error",
                            )
                        val repoFile = File("${pipelineContext.workspace}${File.separator}$repoName")
                        if (repoFile.exists()) {
                            Command("rm")
                                .args("-rf", repoFile.absolutePath)
                                .default()
                                .spawn()
                                .wait()
                        }
                        Command(GitXWrapper.gitx)
                            .args("clone", repo.repoUrl, repoFile.absolutePath)
                            .default()
                            .spawn()
                            .wait()
                        Command(GitXWrapper.gitx)
                            .args("checkout", repo.branch)
                            .directory(repoFile)
                            .spawn()
                            .wait()
                        Command(GitXWrapper.gitx)
                           .args("lfs", "pull")
                           .directory(repoFile)
                           .spawn()
                        editor.add("repo.ci.${repoName}", "${true}")
                    }
                }
                val propertiesFile = File(
                    workspaceParams.targetProjectDir,
                    "ci-multi-source-config.properties"
                )
                if (propertiesFile.exists()) {
                    logger().info(propertiesFile.readText())
                } else {
                    logger().warn("ci-multi-source-config.properties not exists")
                }
                BitsUtils.setEnv("CUSTOM_INCLUDE_REPO", repoList.joinToString(",") { repo ->
                    repo.repoUrl
                })
            }
        } else {
            logger().info("disabled sub repo")
        }
    }
}

@BaseUrl("https://lv-paper-airplane.cn.goofy.app/api/")
interface IBuildInfoRequest {
    @POST(value = "customBuild/buildInfo")
    suspend fun buildInfo(
        @Body json: BuildInfo,
    ): BuildInfoResponse
}

@Serializable
data class BuildInfoResponse(
    val data: List<RepoInfo>? = null,
    val code: Int,
    val message: String,
)

@Serializable
data class RepoInfo(
    val projectId: Int,
    val repoUrl: String,
    val branch: String,
    val openComponent: List<String> = listOf(),
)

@Serializable
data class BuildInfo(
    val buildId: String,
)
