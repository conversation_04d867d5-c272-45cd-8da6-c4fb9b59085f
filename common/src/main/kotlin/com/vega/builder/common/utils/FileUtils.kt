package com.vega.builder.common.utils

import com.vega.builder.common.logger.logger
import org.apache.commons.io.FilenameUtils.removeExtension
import java.io.File
import java.security.MessageDigest
import java.util.Locale

private const val TAG = ""

fun File.printDirectoryTree(
    maxDepth: Int,
    level: Int = 0,
) {
    if (level == 0) {
        logger().info("DirTree[${this.absolutePath}]:")
    }
    if (!this.exists()) {
        println("File or directory does not exist.")
        return
    }

    // 创建缩进字符串
    val indent = "  ".repeat(level)

    // 打印当前文件或目录名
    if (this.isDirectory) {
        println("$indent[D]${this.name}")
        // 如果当前深度小于最大深度，继续递归遍历
        if (level < maxDepth) {
            this.listFiles()?.forEach { child ->
                child.printDirectoryTree(maxDepth, level + 1)
            }
        } else {
            println("$indent  ...") // 表示有更多内容，但不展示
        }
    } else {
        println("$indent${this.name}")
    }
}

fun File.md5(): String {
    val buffer = ByteArray(1024)
    val md5 = MessageDigest.getInstance("MD5")
    var numRead: Int

    inputStream().use { fis ->
        do {
            numRead = fis.read(buffer)
            if (numRead > 0) {
                md5.update(buffer, 0, numRead)
            }
        } while (numRead != -1)
    }

    val hashBytes = md5.digest()
    return hashBytes.joinToString("") { "%02x".format(it) }
}

fun withExtension(filePath: String, extension: String): String {
    if (filePath.lowercase(Locale.getDefault()).endsWith(extension)) {
        return filePath
    }
    return removeExtension(filePath) + extension
}
