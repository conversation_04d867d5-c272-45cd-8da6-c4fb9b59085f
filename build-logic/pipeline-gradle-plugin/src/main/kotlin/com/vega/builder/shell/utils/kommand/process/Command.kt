package com.vega.builder.shell.utils.kommand.process


import com.vega.builder.shell.utils.kommand.KommandException
import com.vega.builder.shell.utils.kommand.io.Output
import java.io.File
import java.io.InputStreamReader

class Command(
    val command: String,
    private val builder: ProcessBuilder,
) {
    constructor(command: String) : this(command, ProcessBuilder(command))

    fun debugString() {
        println(
            "Command(command='$command', builder=${
                builder.command()
                    .toMutableList()
                    .apply { this.removeFirst() }
                    .joinToString(",", "[", "]")
            },workingDir=${builder.directory()?.absolutePath ?: File(".").absolutePath})",
        )
    }

    fun arg(arg: String): Command {
        builder.command().add(arg)
        return this
    }

    fun args(args: List<String>): Command {
        builder.command().addAll(args)
        return this
    }

    fun args(vararg args: String): Command {
        builder.command().addAll(args)
        return this
    }

    fun env(
        key: String,
        value: String,
    ): Command {
        builder.environment()[key] = value
        return this
    }

    fun envs(envs: Map<String, String>): Command {
        builder.environment().putAll(envs)
        return this
    }

    fun envs(vararg envs: Pair<String, String>): Command {
        builder.environment().putAll(envs)
        return this
    }

    fun removeEnv(key: String): Command {
        builder.environment().remove(key)
        return this
    }

    fun envClear(): Command {
        builder.environment().clear()
        return this
    }

    fun cwd(dir: String): Command {
        builder.directory(File(dir))
        return this
    }

    fun stdin(stdio: Stdio): Command {
        builder.redirectInput(stdio.to())
        return this
    }

    fun stdout(stdio: Stdio): Command {
        builder.redirectOutput(stdio.to())
        return this
    }

    fun stderr(stdio: Stdio): Command {
        builder.redirectError(stdio.to())
        return this
    }

    fun directory(workingDir: File): Command {
        builder.directory(workingDir)
        return this
    }

    fun default(): Command {
        stdout(Stdio.Inherit)
        stderr(Stdio.Inherit)
        stdin(Stdio.Inherit)
        return this
    }

    @Throws(KommandException::class)
    fun spawn(printCmd: Boolean = true): Child {
        if (printCmd) {
            debugString()
        }
        val process = builder.start()
        return Child(process)
    }

    @Throws(KommandException::class)
    fun output(): Output {
        val process = builder.start()
        val stdoutContent = InputStreamReader(process.inputStream).readText()
        val stderrContent = InputStreamReader(process.errorStream).readText()
        val status = process.waitFor()
        return Output(status, stdoutContent, stderrContent)
    }

    @Throws(KommandException::class)
    fun status(): Int = builder.start().waitFor()
}

enum class Stdio {
    Inherit,
    Pipe,
    Null,
    ;

    companion object;
}

fun Stdio.to(): ProcessBuilder.Redirect =
    when (this) {
        Stdio.Inherit -> ProcessBuilder.Redirect.INHERIT
        Stdio.Null -> ProcessBuilder.Redirect.DISCARD
        Stdio.Pipe -> ProcessBuilder.Redirect.PIPE
    }
