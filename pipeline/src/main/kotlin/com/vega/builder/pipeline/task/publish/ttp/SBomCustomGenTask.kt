package com.vega.builder.pipeline.task.publish.ttp

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.vega.builder.common.kommand.process.Command
import com.vega.builder.common.logger.logger
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.runCommand
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.GitParams
import com.vega.builder.pipeline.context.PathDeclare
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.task.publish.PublishUtils
import org.koin.core.component.inject
import java.io.File
import kotlin.getValue
import kotlin.io.path.createTempDirectory
import kotlin.io.path.createTempFile

@TaskDefinition("SBomTask", stage = Stage.After, "sbom generate and upload")
class SBomCustomGenTask : PipelineTask() {
    val pipelineContext by inject<PipelineContextImpl>()
    val pathDeclare: PathDeclare by inject()
    val workspaceParams by inject<WorkspaceParams>()
    val gitParams by inject<GitParams>()
    val buildParams by inject<BuildParams>()
    override suspend fun run() {

        val publishGenerateRepoRoot = createTempDirectory("publish").toFile().apply { if (!exists()) mkdirs() }
        val publishGenerateRepoFile = File(publishGenerateRepoRoot, "publish")

        "git clone --depth 1 ******************:tech_client/publish.git -b feat/parfait_prepare".runCommand(
            publishGenerateRepoRoot, false
        )


        val groupId = buildParams["MAVEN_GROUP_ID"]
        val artifactId = buildParams["MAVEN_ARTIFACT_ID"]
        val mavenId = buildParams["MAVEN_ID"]
        val version = getenvSafe("version", "")

        if (version.isEmpty()) {
            logger().error("version is empty")
            throw PipelineThrowable(ErrorType.TTP_JOB_PUBLISH_SOURCE_VERSION_EMPTY_FAILED)
        }
        if (mavenId.isNullOrBlank()) {
            logger().error("mavenId is empty")
            throw PipelineThrowable(ErrorType.TTP_JOB_PUBLISH_SOURCE_MAVEN_EMPTY_FAILED)
        }
        if (groupId.isNullOrBlank() || artifactId.isNullOrBlank()) {
            logger().error("groupId or artifactId is empty")
            throw PipelineThrowable(ErrorType.TTP_JOB_PUBLISH_SOURCE_MAVEN_INFO_EMPTY_FAILED)
        }
        val uploadKey = PublishUtils.buildTosId(mavenId, version)

        val componentInfo = listOf(
            ComponentInfo(
                platform = "custom",
                group = groupId,
                name = artifactId,
                version = version,
                distribution = "https://maven.byted.org/repository/android_public/${TosConfig.LvBuildArtifact.bucket}/${uploadKey}",
                path = PipelineOutput.ttpSourceCompressionPath,
                vcs = gitParams.mainUrl,
                sha1 = pipelineContext.commitFullId,
            )
        )
        val componentInfoFile = createTempFile(prefix = "component_info", suffix = ".json").toFile()
        componentInfoFile.writeText(Gson().toJson(componentInfo))
        val result = Command("python3")
            .args("python/entry/ttp_sbom_generate.py")
            .directory(publishGenerateRepoFile)
            .env("IS_TTP", "true")
            .env("COMPONENT_INFO", componentInfoFile.absolutePath)
            .default().spawn().wait()
        val genSbomMetadata = File(
            listOf(
                workspaceParams.workspace,
                "sbom.json"
            ).joinToString(File.separator)
        )
        if (result == 0 && genSbomMetadata.exists()) {
            genSbomMetadata.copyTo(pathDeclare.customSbomFile, true)
        } else {
            logger().error("sbom generate failed $result")

        }

    }
}

data class ComponentInfo(
    val platform: String,
    val group: String,
    val name: String,
    val version: String,
    val distribution: String,
    val path: String,
    val vcs: String,
    @SerializedName("sha-1")
    val sha1: String,
)