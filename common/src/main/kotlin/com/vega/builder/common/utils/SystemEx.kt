package com.vega.builder.common.utils

import kotlin.collections.plus

private val env by lazy { System.getenv() }
private val mockEnv = mutableMapOf<String, String>()

fun getenvSafe() = mockEnv + env

fun getenvSafe(
    key: String,
    default: String,
): String = getenvInternal(key, default)

fun getenvSafe(key: String): String? = getenvInternal(key)

fun setenvMock(key: String, value: String) {
    mockEnv[key] = value
}

fun isMockEnv(): Boolean {
    return mockEnv.isNotEmpty()
}

private fun getenvInternal(key: String, default: String): String {
    if (mockEnv.containsKey(key)) {
        return mockEnv[key]!!
    }
    return env[key] ?: default
}

private fun getenvInternal(key: String): String? {
    if (mockEnv.containsKey(key)) {
        return mockEnv[key]!!
    }
    return env[key]
}