package com.vega.builder.pipeline.context

import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.io.File
import kotlin.io.path.createTempFile

object PipelineOutput : KoinComponent {

    private val buildParams: BuildParams by inject()

    private val pipelineContext: PipelineContextImpl by inject()

    private val workspaceParams: WorkspaceParams by inject()

    fun buildOutputs(): List<Pair<String, String>> {
        return listOf(
            "mapping" to buildMappingPath,
            "mapping_with_line_number" to buildMappingWithOutLinePath,
            "trace_mapping" to rehaMethodMappingPath,
            "usage" to buildUsagePath,
            "resmapping" to buildResMappingPath,
            "dependency" to buildDependency,
            "txt" to buildSdkDependency,
            "aab" to buildFinalBundlePath,
            "cakeknife_txt" to listOfNotNull(buildPath, "cakeKnife", "cakeKnife.txt").joinToString(
                separator = File.separator
            ),
            "txt" to listOfNotNull(buildPath, "hotfix-output", "methodId.txt").joinToString(
                separator = File.separator
            ),
            "resguard_txt" to buildResMergeDuplicatedPath,
            "all_class" to allClassJarPath,
            "class_diff" to classDiffJarPath,
            "settings" to listOfNotNull(
                buildPath,
                "ByteX",
                "settings_collect_plugin",
                "outputs",
                "all_settings.json"
            ).joinToString(separator = File.separator),
            *buildFinalApkPath.map { "apk" to it }.toTypedArray()
        )
    }
    /**
     * todo ***********************************产物输出路径，需要根据不同项目调整***********************************
     */
    /**
     * build dir
     */
    val buildPath: String
        get() {
            return listOf(
                workspaceParams.targetProjectDir,
                buildParams.buildTarget.modulePath,
                "build",
            ).joinToString(File.separator)
        }

    /**
     * module Output dir
     */
    val buildOutputsPath: String
        get() {
            return listOf(
                buildPath,
                "outputs",
            ).joinToString(File.separator)
        }

    /**
     * Mapping file root dir
     */
    val buildMappingRootPath: String
        get() {
            var type = (if (buildParams.isDebug) "debug" else "release")
            if (buildParams.buildTarget.flavor.isNotEmpty()) {
                type = type.replaceFirstChar { it.uppercaseChar() }
            }
            return listOf(
                buildOutputsPath,
                "mapping",
                "${buildParams.buildTarget.flavor}$type",
            ).joinToString(File.separator)
        }

    /**
     * usage.txt 's path
     */
    val buildUsagePath: String
        get() = listOf(buildMappingRootPath, "usage.txt").joinToString(File.separator)

    /**
     * Mapping path
     */
    val buildMappingPath: String
        get() = listOf(buildMappingRootPath, "mapping.txt").joinToString(File.separator)


    val buildMappingWithOutLinePath: String
        get() {
            val type =
                (if (buildParams.isDebug) "debug" else "release").replaceFirstChar { it.uppercaseChar() }

            return listOf(
                buildPath,
                "dsln",
                "${buildParams.buildTarget.flavor.replaceFirstChar { it.uppercaseChar() }}$type",
                "MappingWithLineNumber.txt",
            ).joinToString(File.separator)
        }

    /**
     * ResGuard Mapping, aab/apk has different path
     */
    val buildResMappingPath: String
        get() {
            return if (buildParams.withAab) {
                listOf(buildBundleRootPath, "resources-mapping.txt").joinToString(File.separator)
            } else {
                listOf(
                    buildApkRootPath,
                    "AndResGuard_${pipelineContext.defaultApkName}",
                    "resource_mapping_${pipelineContext.defaultApkName}.txt",
                ).joinToString(File.separator)
            }
        }

    /**
     * AndResGuard res Mapping，aab not has this path
     */
    val buildResMergeDuplicatedPath: String
        get() {
            return listOf(
                buildApkRootPath,
                "AndResGuard_${pipelineContext.defaultApkName}",
                "merge_duplicated_res_mapping_${pipelineContext.defaultApkName}.txt",
            ).joinToString(File.separator)
        }

    val buildDependency: String
        get() {
            val type = if (buildParams.isDebug) "debug" else "release"
            return listOf(
                buildOutputsPath,
                "dependency",
                buildParams.buildTarget.flavor,
                type,
                "dependency.txt",
            ).joinToString(File.separator)
        }

    val buildSdkDependency: String
        get() {
            var type = (if (buildParams.isDebug) "debug" else "release")
            if (buildParams.buildTarget.flavor.isNotEmpty()) {
                type = type.replaceFirstChar { it.uppercaseChar() }
            }
            return listOf(
                buildOutputsPath,
                "sdk-dependencies",
                "${buildParams.buildTarget.flavor}$type",
                "sdkDependencies.txt",
            ).joinToString(File.separator)
        }

    val buildApkRootPath: String
        get() {
            val type = if (buildParams.isDebug) "debug" else "release"
            return listOf(
                buildOutputsPath,
                "apk",
                buildParams.buildTarget.flavor,
                type,
            ).joinToString(File.separator)
        }

    val buildApkPaths: List<String>
        get() {
            val type = if (buildParams.isDebug) "debug" else "release"
            return pipelineContext.apkNames.map {
                listOf(
                    buildOutputsPath,
                    "apk",
                    buildParams.buildTarget.flavor,
                    type,
                    it,
                ).joinToString(File.separator)
            }
        }

    private val finalApkPath: MutableList<String> = mutableListOf()

    fun recordFinalApkPath(apkPath: String) {
        finalApkPath.add(apkPath)
    }

    val buildFinalApkPath: List<String>
        get() {
            return finalApkPath
        }

    val buildFinalBundlePath: String
        get() {
            return listOf(buildBundleRootPath, "${pipelineContext.finalTargetName}.aab").joinToString(File.separator)
        }

    val buildBundleRootPath: String
        get() {
            var type = (if (buildParams.isDebug) "debug" else "release")
            if (buildParams.buildTarget.flavor.isNotEmpty()) {
                type = type.replaceFirstChar { it.uppercaseChar() }
            }

            return listOf(
                buildOutputsPath,
                "bundle",
                "${buildParams.buildTarget.flavor}$type",
            ).joinToString(File.separator)
        }

    val buildBundlePath: String
        get() {
            return listOf(buildBundleRootPath, "app-complete.aab").joinToString(File.separator)
        }

    val buildNativePathInfo: String
        get() {
            return listOf(workspaceParams.targetProjectDir, "build", "native_build_obj.json").joinToString(
                File.separator
            )
        }

    val rehaMethodMappingPath: String
        get() {
            var type = (if (buildParams.isDebug) "debug" else "release")
            return listOf(
                buildOutputsPath,
                "rhea",
                type,
                "methodMapping.txt"
            ).joinToString(File.separator)
        }

    val allClassJarPath: String
        get() {
            return listOf(
                buildPath,
                "offlineDetection",
                "allClass.jar"
            ).joinToString(File.separator)
        }

    val classDiffJarPath: String
        get() {
            return listOf(
                buildPath,
                "offlineDetection",
                "class_diff.jar"
            ).joinToString(File.separator)
        }

    var ttpSourceCompressionPath: String = ""
        get() {
            if (field == "") {
                val result =
                    createTempFile(prefix = "${File(workspaceParams.targetProjectDir).name}-", suffix = ".tar.gz").toFile()
                field = result.absolutePath
            }
            return field
        }
}

