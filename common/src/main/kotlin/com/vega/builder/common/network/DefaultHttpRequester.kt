package com.vega.builder.common.network

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import okhttp3.Call
import okhttp3.EventListener
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import okhttp3.logging.LoggingEventListener
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import kotlin.reflect.KClass
import kotlin.reflect.KFunction

private val cachedServices = ConcurrentHashMap<KClass<*>, Any>()

fun logFunctionInfo(func: Any) {
    val functionName = when (func) {
        is KFunction<*> -> func.name
        is Function<*> -> func.javaClass.simpleName
        else -> "Anonymous"
    }
    logger().info("call func is：$functionName")
}

suspend inline fun <reified F, Req, Resp> request(
    noinline func: suspend F.(Req) -> Resp,
    req: Req,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, req)
    }
    return request(m)
}

suspend inline fun <reified F, Resp, R1, R2> request(
    noinline func: suspend F.(R1, R2) -> Resp,
    r1: R1,
    r2: R2,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2)
    }
    return request(m)
}

suspend inline fun <reified F, Resp, R1, R2, R3> request(
    noinline func: suspend F.(R1, R2, R3) -> Resp,
    r1: R1,
    r2: R2,
    r3: R3,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2, r3)
    }
    return request(m)
}

suspend inline fun <reified F, Resp, R1, R2, R3, R4> request(
    noinline func: suspend F.(R1, R2, R3, R4) -> Resp,
    r1: R1,
    r2: R2,
    r3: R3,
    r4: R4,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2, r3, r4)
    }
    return request(m)
}

suspend inline fun <reified F, Resp, R1, R2, R3, R4, R5> request(
    noinline func: suspend F.(R1, R2, R3, R4, R5) -> Resp,
    r1: R1,
    r2: R2,
    r3: R3,
    r4: R4,
    r5: R5,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2, r3, r4, r5)
    }
    return request(m)
}

suspend inline fun <reified F, Resp, R1, R2, R3, R4, R5, R6> request(
    noinline func: suspend F.(R1, R2, R3, R4, R5, R6) -> Resp,
    r1: R1,
    r2: R2,
    r3: R3,
    r4: R4,
    r5: R5,
    r6: R6,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2, r3, r4, r5, r6)
    }
    return request(m)
}

suspend inline fun <reified F, Resp, R1, R2, R3, R4, R5, R6, R7> request(
    noinline func: suspend F.(R1, R2, R3, R4, R5, R6, R7) -> Resp,
    r1: R1,
    r2: R2,
    r3: R3,
    r4: R4,
    r5: R5,
    r6: R6,
    r7: R7,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2, r3, r4, r5, r6, r7)
    }
    return request(m)
}


suspend inline fun <reified F, Resp, R1, R2, R3, R4, R5, R6, R7, R8> request(
    noinline func: suspend F.(R1, R2, R3, R4, R5, R6, R7, R8) -> Resp,
    r1: R1,
    r2: R2,
    r3: R3,
    r4: R4,
    r5: R5,
    r6: R6,
    r7: R7,
    r8: R8,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2, r3, r4, r5, r6, r7, r8)
    }
    return request(m)
}

suspend inline fun <reified F, Resp, R1, R2, R3, R4, R5, R6, R7, R8, R9> request(
    noinline func: suspend F.(R1, R2, R3, R4, R5, R6, R7, R8, R9) -> Resp,
    r1: R1,
    r2: R2,
    r3: R3,
    r4: R4,
    r5: R5,
    r6: R6,
    r7: R7,
    r8: R8,
    r9: R9,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2, r3, r4, r5, r6, r7, r8, r9)
    }
    return request(m)
}

suspend inline fun <reified F, Resp, R1, R2, R3, R4, R5, R6, R7, R8, R9, R10> request(
    noinline func: suspend F.(R1, R2, R3, R4, R5, R6, R7, R8, R9, R10) -> Resp,
    r1: R1,
    r2: R2,
    r3: R3,
    r4: R4,
    r5: R5,
    r6: R6,
    r7: R7,
    r8: R8,
    r9: R9,
    r10: R10,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2, r3, r4, r5, r6, r7, r8, r9, r10)
    }
    return request(m)
}

suspend inline fun <reified F, Resp, R1, R2, R3, R4, R5, R6, R7, R8, R9, R10, R11> request(
    noinline func: suspend F.(R1, R2, R3, R4, R5, R6, R7, R8, R9, R10, R11) -> Resp,
    r1: R1,
    r2: R2,
    r3: R3,
    r4: R4,
    r5: R5,
    r6: R6,
    r7: R7,
    r8: R8,
    r9: R9,
    r10: R10,
    r11: R11,
): Resp {
    val m: suspend F.() -> Resp = {
        logFunctionInfo(func)
        func.invoke(this, r1, r2, r3, r4, r5, r6, r7, r8, r9, r10, r11)
    }
    return request(m)
}


suspend inline fun <reified F : Any, Resp> request(noinline block: suspend F.() -> Resp): Resp =
    request(F::class, block)

suspend fun <F : Any, Resp> request(
    clazz: KClass<F>,
    block: suspend F.() -> Resp,
): Resp {
    val service: F = getService(clazz)
    return service.block()
}

private fun <T : Any> getService(clazz: KClass<T>): T {
    val baseUrlInfo = clazz.annotations.filterIsInstance<BaseUrl>().firstOrNull()
    val baseUrl =
        baseUrlInfo?.value ?: throw PipelineThrowable(
            ErrorType.NetworkUrlNotFound,
        )
    @Suppress("UNCHECKED_CAST")
    return cachedServices.getOrPut(clazz) { createRetrofit(baseUrl, baseUrlInfo.printLogs).create(clazz.java) } as T
}

val okhttpClient: OkHttpClient by lazy {
    OkHttpClient
        .Builder()
        .connectTimeout(30, TimeUnit.SECONDS) // 设置连接超时时间为30秒
        .readTimeout(10, TimeUnit.MINUTES) // 设置读取超时时间为30秒
        .writeTimeout(10, TimeUnit.MINUTES) // 设置写入超时时间为30秒
        .build()
}

val hasLogOkHttpClient by lazy {
    val logger = HttpLoggingInterceptor.Logger {
        OkHttpClient.logger().info(it)
    }
    OkHttpClient
        .Builder()
//        .eventListenerFactory(LoggingEventListener.Factory(logger))
        .addInterceptor(HttpLoggingInterceptor(logger).setLevel(HttpLoggingInterceptor.Level.BODY))
        .connectTimeout(30, TimeUnit.SECONDS) // 设置连接超时时间为30秒
        .readTimeout(10, TimeUnit.MINUTES) // 设置读取超时时间为30秒
        .writeTimeout(10, TimeUnit.MINUTES) // 设置写入超时时间为30秒
        .build()
}

private fun createRetrofit(url: String, printLog: Boolean): Retrofit {

//    val networkJson = Json { ignoreUnknownKeys = true }
    return Retrofit
        .Builder()
        .baseUrl(
            if (url.endsWith("/")) {
                url
            } else {
                "$url/"
            }
        )
        .client(
            if (printLog) {
                hasLogOkHttpClient
            } else {
                okhttpClient
            }
        )
        .addConverterFactory(GsonConverterFactory.create())
        .build()
}
