package com.vega.builder.pipeline.task.artifacts

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.BaseUrl
import com.vega.builder.common.network.request
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.tos.slicePutObject
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.retry
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.BuildConfig
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.context.buildTarget
import com.vega.builder.pipeline.context.isDebug
import com.vega.builder.pipeline.context.isOutBuild
import com.vega.builder.pipeline.utils.SlardarUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.koin.core.component.inject
import retrofit2.HttpException
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.PartMap
import java.io.File
import kotlin.time.measureTime

@TaskDefinition(RowSlardarMappingUpload.TAG, stage = Stage.After, displayName = "Slardar Mapping Upload")
class RowSlardarMappingUpload : PipelineTask() {
    companion object {
        const val TAG = "slardarMappingUpload"
        val buildIDRegex = Regex("BuildID\\[sha1]=([0-9a-f]+)")
        val isStrippedRegex = Regex("not stripped")
    }

    val buildParams: BuildParams by inject()

    val pipelineContext: PipelineContextImpl by inject()


    override suspend fun run() {
        if (buildParams.isEnable("IS_GRADLE_CACHE_PUBLISH", false)
            || buildParams.isEnable("IS_PUSH_CACHE", false)
            || buildParams.isDebug
        ) {
            logger().info(
                "Skip upload Slardar,IS_GRADLE_CACHE_PUBLISH[{}],IS_PUSH_CACHE[{}],Debug[{}]",
                buildParams.isEnable("IS_GRADLE_CACHE_PUBLISH", false),
                buildParams.isEnable("IS_PUSH_CACHE", false),
                buildParams.isDebug
            )
            return
        }
        val nativePathInfoFile = File(PipelineOutput.buildNativePathInfo)
        if (nativePathInfoFile.exists()) {

            val paths = Gson().fromJson<List<String>>(nativePathInfoFile.readText().apply {
                logger().info("Native path info read from file: $this")
            }).toHashSet()
            val taskId = getenvSafe("history_id", getenvSafe("TASK_ID", ""))
            val uploadTosSuccessPath = mutableListOf<String>()
            for (path in paths) {
                val soFile = File(path)
                if (!soFile.exists()) {
                    continue
                }
                val fileInfo = SlardarUtils.queryNativeFileInfo(soFile.absolutePath)
                if (fileInfo != null) {
                    logger().info("File[${soFile.absolutePath}] Build ID: $fileInfo")
                    if (!fileInfo.stripped) {
                        val remoteName =
                            listOfNotNull(taskId, "native-obj", "${fileInfo.buildId}_${soFile.name}").joinToString("/")
                        val (uploadSuccess, url) = retry(times = 5, delayMillis = 5_000) { upload(remoteName, soFile) }
                        if (uploadSuccess && url != null) {
                            uploadTosSuccessPath.add(url)
                        }
                    } else {
                        logger().warn("File[${soFile.absolutePath}] is stripped,skip upload!")
                    }
                } else {
                    logger().error("File[${soFile.absolutePath}] not find file info")
                }
            }
            uploadTosSuccessPath.map { path ->
                CoroutineScope(Dispatchers.IO).launch {
                    val time = uploadTosPath2Slardar(path)
                    logger().info("Upload [${path}] to Slardar cost: $time")
                }
            }.joinAll()

        } else {
            logger().warn("Can't find native path info file")
        }
    }

    suspend fun upload(remoteName: String, soFile: File): Pair<Boolean, String?> {
        val tosClient = TosConfig.LvBuildResult.createTosClient()
        return if (tosClient.slicePutObject(remoteName, soFile)) {
            true to "https://${BuildConfig.hide_voffline_rul}/download/tos/schedule/${TosConfig.LvBuildResult.bucket}/${remoteName}"
        } else {
            logger().error("Can't upload tos ${soFile.absolutePath}")
            false to null
        }
    }

    private suspend fun uploadTosPath2Slardar(tosPath: String) = measureTime {
        val extra = hashMapOf(
            "uuid" to "default".toRequestBody("multipart/form-data".toMediaType()),
        )
        try {
            val result = request(
                ISlardarUploadApi::androidUpload,
                buildParams.buildTarget.aid.toString().toRequestBody("multipart/form-data".toMediaType()),
                "Native".toRequestBody("multipart/form-data".toMediaType()),
                tosPath.toRequestBody(
                    "multipart/form-data".toMediaType()
                ),
                if (buildParams.buildTarget.isOversea) {
                    ISlardarUploadApi.TargetReason.Maliva
                } else {
                    ISlardarUploadApi.TargetReason.Cn
                },

                if (buildParams.isOutBuild) {
                    ISlardarUploadApi.TtlType.Online
                } else {
                    ISlardarUploadApi.TtlType.Test
                },
                extra
            )
            logger().info("Slardar upload result is ${Gson().toJson(result)}")
        } catch (e: HttpException) {
            logger().error("error:$tosPath,${e.response()?.errorBody()?.string()}")
        }
    }
}

@BaseUrl("https://${BuildConfig.hide_slardar_symbol_url}")
interface ISlardarUploadApi {
    @Multipart
    @POST("/android_upload")
    suspend fun androidUpload(
        @Part("aid") aid: RequestBody,
        @Part("type") type: RequestBody,
        @Part("tos_url") tosUrl: RequestBody,
        @Part("target_region") region: RequestBody?,
        @Part("ttl_type") ttlType: RequestBody?,
        @PartMap extra: Map<String, @JvmSuppressWildcards RequestBody> = hashMapOf()
    ): HashMap<String, Any>

    object TtlType {
        val Online: RequestBody = "online".toRequestBody("multipart/form-data".toMediaType())
        val Test: RequestBody = "test".toRequestBody("multipart/form-data".toMediaType())
    }

    object TargetReason {
        val Maliva: RequestBody = "maliva".toRequestBody("multipart/form-data".toMediaType())
        val Cn: RequestBody = "cn".toRequestBody("multipart/form-data".toMediaType())
    }
}