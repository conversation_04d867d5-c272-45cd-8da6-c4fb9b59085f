package com.vega.builder.pipeline.task.conflict

import java.io.File

class DependencyLockProcessor : FileConflictProcessor {
    constructor(filePath: String) : super(filePath)
    constructor(file: File, fileReader: ConflictFileReader) : super(file, fileReader)

    override suspend fun processConflictBlock(conflictBlock: ConflictBlock): List<String> {
        // First determine whether the number of conflicting rows is the same. GradleProperties only resolves conflicts with the same number of rows
        if (conflictBlock.conflictLinesHead.size != conflictBlock.conflictLinesInsert.size) {
            throw IllegalStateException("Gradle Properties only supports conflicts with the same number of lines")
        }
        // Determine the version to use by using the version in the conflict block
        // Find the line with version first
        val versionHead = conflictBlock.conflictLinesHead.first { extractVersion(it).isNotEmpty() }
        val versionInsert =
            conflictBlock.conflictLinesInsert.first { extractVersion(it).isNotEmpty() }
        // compare versions
        return if (isLargeThan(extractVersion(versionHead), extractVersion(versionInsert))) {
            conflictBlock.conflictLinesHead
        } else {
            conflictBlock.conflictLinesInsert
        }
    }

    private fun extractVersion(input: String): String {
        val regex = """"version"\s*:\s*"(\d+(\.\d+)+)"""".toRegex()
        val matchResult = regex.find(input)

        return if (matchResult != null) {
            matchResult.groupValues[1]
        } else {
            ""
        }
    }
}