package com.vega.builder.common.network.api

import com.vega.builder.common.network.BaseUrl
import okhttp3.ResponseBody
import retrofit2.http.GET
import retrofit2.http.QueryMap
import retrofit2.http.Streaming
import retrofit2.http.Url

@BaseUrl("https://fack.com")
interface INetworkApi {
    @GET
    @Streaming
    suspend fun download(
        @Url relativePath: String,
        @QueryMap queryMap: Map<String, String> = emptyMap(),
    ): ResponseBody
}