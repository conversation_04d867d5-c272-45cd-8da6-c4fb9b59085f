package com.vega.builder.pipeline.task.ttp

import com.vega.builder.common.logger.configureLogback
import com.vega.builder.common.throwable.PipelineThrowableHandler
import com.vega.builder.common.utils.printHello
import com.vega.builder.common.utils.setenvMock
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.di.configureKoin
import kotlinx.coroutines.runBlocking
import org.koin.mp.KoinPlatform
import java.lang.Runtime.version
import kotlin.test.Test

class SBomTaskTest {
    @Test
    fun `test create SBomTask task`() {

        setenvMock("WORKSPACE","/Users/<USER>/develop/bytedance")
        setenvMock("MAIN_GIT_URL","******************:faceu/videoeditor.git")
        setenvMock("MAVEN_GROUP_ID", "com.bytedance.vega")
        setenvMock("MAVEN_ARTIFACT_ID", "vega")
        setenvMock("MAVEN_ID", "com_lemon_faceu/videoeditor_cc_repo_info")
        setenvMock("version", "1.0.0")
        setenvMock("TTP_BUILD_ENV", "true")
        setenvMock("IS_TTP",true.toString())
        setenvMock("DEFAULT_TEMPLATE","publish_ttp_source_videoeditor")
        runBlocking {
            PipelineThrowableHandler.register()
            printHello()
            configureLogback()
            configureKoin()
            KoinPlatform.getKoin().get<BuildParams>().loadParams()
        }
    }
}