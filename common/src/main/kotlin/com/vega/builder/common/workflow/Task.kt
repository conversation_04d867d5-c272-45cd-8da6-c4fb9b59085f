package com.vega.builder.common.workflow

import com.vega.builder.common.bits.IBitsNetwork
import com.vega.builder.common.bits.model.workflow.step.update.StepUpdateRequest
import com.vega.builder.common.config.IConfigService
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.BuildJobRecordReq
import com.vega.builder.common.network.api.IBuildServiceApi
import com.vega.builder.common.network.request
import com.vega.builder.common.throwable.ErrorType
import com.vega.builder.common.throwable.PipelineThrowable
import com.vega.builder.common.tos.TosConfig
import com.vega.builder.common.tos.slicePutObject
import com.vega.builder.common.utils.compression.Compression
import com.vega.builder.common.utils.compression.TarGz
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.retry
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.io.File
import java.nio.file.Files
import kotlin.io.path.createTempDirectory
import kotlin.io.path.createTempFile
import kotlin.random.Random
import kotlin.time.measureTime

enum class TaskStatus(
    val value: String,
) {
    Success("success"),
    Failure("failed"),
    Running("running"),
    Pending("pending"),
}

abstract class PipelineTask : KoinComponent {
    open var taskDefinition: TaskDefinition? = null

    companion object {
        fun PipelineTask.getTaskDefinition(): TaskDefinition =
            this.taskDefinition
                ?: this::class.java.getAnnotation(TaskDefinition::class.java)?.apply {
                    <EMAIL> = this
                } ?: throw Exception("TaskDefinition not found")
    }

    val taskInfo by lazy { getTaskDefinition() }

    val configService: IConfigService by inject()

    open val outputFiles: List<File> = emptyList()


    protected abstract suspend fun run()

    suspend fun executeWithTTP() {
        logger().info("####################################################################")
        logger().info("#                  Start Task: ${taskInfo.name}")
        logger().info("####################################################################")
        val duration = measureTime {
            runCatching {
                run()
            }.onFailure { throwable ->
                val eWrapper = throwable as? PipelineThrowable ?: PipelineThrowable(ErrorType.Unknown, throwable)
                if (!taskInfo.allowFailure) {
                    logger().error("Task execute failed: ${eWrapper.message}")
                    throw eWrapper
                } else {
                    logger().error("Task execute failed: ${eWrapper.message}, but allow failure, so ignore it")
                }
            }
        }
        logger().info("Task execute down[$duration]")

    }

    suspend fun execute() {
        report(TaskStatus.Pending)
        println("${taskInfo.name} start")
        val start = System.currentTimeMillis()
        runCatching {
            report(TaskStatus.Running, (start / 1000).toInt())
            if (!tryUseCache()) {
                run()
                uploadCache()
            }
        }.onFailure { throwable ->
            val end = System.currentTimeMillis()
            val eWrapper = throwable as? PipelineThrowable ?: PipelineThrowable(ErrorType.Unknown, throwable)
            report(
                TaskStatus.Failure,
                startTime = (start / 1000).toInt(),
                stopTime = (end / 1000).toInt(),
                errorCode = eWrapper.errorType.code,
                errorMsg = eWrapper.message
            )
            println("${taskInfo.name} failure")
            if (!taskInfo.allowFailure) {
                runCatching {
                    request(
                        IBuildServiceApi::jobBuildResult,
                        BuildJobRecordReq(
                            jobId = getenvSafe("TASK_ID") ?: "${Random(1000).nextInt()}",
                            errorTaskName = taskInfo.name,
                            params = configService.params
                        )
                    )
                }
                throw eWrapper
            }
        }
        val end = System.currentTimeMillis()
        logger().info("Task execute down[${end - start}ms]")
        report(TaskStatus.Success, startTime = (start / 1000).toInt(), stopTime = (end / 1000).toInt())
        println("${taskInfo.name} success")
    }

    private suspend fun report(
        status: TaskStatus,
        startTime: Int? = null,
        stopTime: Int? = null,
        errorMsg: String? = null,
        errorCode: Int? = null,
    ) {
        try {
            val taskInfo = getTaskDefinition()
            if (getenvSafe("WORKFLOW_JOB_ID") != null) {
                val req = StepUpdateRequest.creator(
                    stageName = taskInfo.stage.display,
                    stepName = taskInfo.name,
                    stepDisplayName = taskInfo.displayName,
                    status = status.value,
                    startedTime = startTime,
                    stoppedTime = stopTime,
                    errorCode = errorCode,
                    errorMsg = errorMsg,
                    logKey = taskInfo.name,
                )
                request(IBitsNetwork::workflowStepUpdate, req)
            }
        } catch (ignore: Throwable) {
        }
    }

    suspend fun tryUseCache(): Boolean {
        if (!taskInfo.cachable) {
            return false
        }
        if (configService.taskCacheKey == null || configService.taskCacheBasePath == null) {
            return false
        }
        val fileKey = listOfNotNull("build-cache", configService.taskCacheKey, taskInfo.name).joinToString("/")
        val tosClient = TosConfig.LvBuildResult.createTosClient()
        val cacheHead = tosClient.headObject(fileKey)
        if (cacheHead == null) {
            return false
        }
        val tempRoot = createTempDirectory()
        val tempFile = createTempFile(directory = tempRoot, suffix = TarGz.extension).toFile()
        retry(3, 15_000) {
            logger().info("Download cache[${fileKey}] to ${tempFile.absolutePath}")
            tosClient.getObject(fileKey)?.apply {
                Files.createDirectories(tempRoot)
                tempFile.outputStream().use { out ->
                    objectContent.use { tos ->
                        tos.copyTo(out)
                    }
                }
            }

            if (tempFile.length() != cacheHead.metadata.size) {
                tempFile.delete()
                throw IllegalStateException("download file size not match")
            }
        }
        if (tempFile.length() != cacheHead.metadata.size) {
            tempFile.delete()
            return false
        }

        try {
            Compression.decompress(tempFile, configService.taskCacheBasePath!!)
        } catch (e: Exception) {
            logger().warn("Failed to decompress seven Z file", e)
            tempFile.delete()
            return false
        }

        return true
    }

    suspend fun uploadCache() {
        try {
            if (!taskInfo.cachable) {
                return
            }
            if (configService.taskCacheKey == null || configService.taskCacheBasePath == null || outputFiles.isEmpty()) {
                return
            }
            logger().info("Uploading cache for ${configService.taskCacheBasePath}")
            val tempRoot = createTempDirectory()
            val tempFile = createTempFile(directory = tempRoot, suffix = TarGz.extension).toFile()
            Compression.compressFiles(
                TarGz,
                outputFiles.filter { it.exists() },
                configService.taskCacheBasePath!!,
                tempFile
            )
            if (tempFile.exists() && tempFile.length() > 0) {
                TosConfig.LvBuildResult.createTosClient()
                    .slicePutObject(
                        listOfNotNull(
                            "build-cache",
                            configService.taskCacheKey,
                            taskInfo.name
                        ).joinToString("/"), tempFile
                    )
            }
        } catch (e: Exception) {
            logger().error("Failed to upload cache", e)
        }
    }
}
