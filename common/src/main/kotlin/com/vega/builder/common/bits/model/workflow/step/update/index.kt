package com.vega.builder.common.bits.model.workflow.step.update

import com.google.gson.annotations.SerializedName
import com.vega.builder.common.utils.getenvSafe

data class StepUpdateRequest(
    val jobId: Int,
    val stageName: String,
    val stepName: String,
    val status: String,
    val errorMsg: String?,
    val errorCode: Int?,
    val startedTime: Int?,
    val stoppedTime: Int?,
    val logKey: String?,
    val stageDisplayName: String?,
    val stepDisplayName: String?,
    val allowFailed: Boolean?,
    @SerializedName("plugin_id")
    val pluginId: Int?,
    @SerializedName("plugin_iid")
    val pluginIid: Int,
) {
    companion object {
        fun creator(
            stageName: String,
            stepName: String,
            status: String,
            errorMsg: String? = null,
            errorCode: Int? = null,
            startedTime: Int? = null,
            stoppedTime: Int? = null,
            logKey: String? = null,
            stageDisplayName: String? = null,
            stepDisplayName: String? = null,
            allowFailed: Boolean? = null,
        ): StepUpdateRequest =
            StepUpdateRequest(
                jobId = getenvSafe("WORKFLOW_JOB_ID", "0").toInt(),
                stageName = stageName,
                stepName = stepName,
                status = status,
                errorMsg = errorMsg,
                errorCode = errorCode,
                startedTime = startedTime,
                stoppedTime = stoppedTime,
                logKey = logKey,
                stageDisplayName = stageDisplayName,
                stepDisplayName = stepDisplayName,
                allowFailed = allowFailed,
                pluginId = getenvSafe("PLUGIN_ID")?.toInt(),
                pluginIid = getenvSafe("PLUGIN_IID", "").toInt(),
            )
    }
}
